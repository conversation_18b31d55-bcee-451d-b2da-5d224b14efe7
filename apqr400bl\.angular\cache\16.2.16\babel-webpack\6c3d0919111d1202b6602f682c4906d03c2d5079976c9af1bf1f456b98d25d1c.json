{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/material/icon\";\nimport * as i3 from \"@angular/material/button\";\nfunction DashboardComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 36);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const stat_r5 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r5.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r5.value);\n  }\n}\nfunction DashboardComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 36);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const stat_r6 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r6.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r6.value);\n  }\n}\nfunction DashboardComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 36);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const stat_r7 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r7.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r7.value);\n  }\n}\nfunction DashboardComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 36);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const stat_r8 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r8.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r8.value);\n  }\n}\nfunction DashboardComponent_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"div\", 16);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 38);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r9 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r9.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", item_r9.status.toLowerCase().replace(\" \", \"-\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", item_r9.status, \" \");\n  }\n}\nexport class DashboardComponent {\n  constructor() {\n    this.batchStats = [{\n      label: 'Batch Released',\n      value: '23'\n    }, {\n      label: 'Batch Rejected',\n      value: '02'\n    }, {\n      label: 'Batch in process',\n      value: '14'\n    }];\n    this.deviationStats = [{\n      label: 'Deviation',\n      value: '03'\n    }, {\n      label: 'CC',\n      value: '05'\n    }, {\n      label: 'CAPA',\n      value: '06'\n    }, {\n      label: 'Complaint',\n      value: '02'\n    }];\n    this.apqrStats = [{\n      label: 'aPQR till date',\n      value: '33'\n    }, {\n      label: 'aPQR year long',\n      value: '05'\n    }, {\n      label: 'Products',\n      value: '300'\n    }];\n    this.taskStats = [{\n      label: 'Assignments',\n      value: '05'\n    }, {\n      label: 'Spreadsheet Return',\n      value: '03'\n    }, {\n      label: 'Sections Update',\n      value: '02'\n    }];\n    this.schedulerItems = [{\n      id: '29_aPQR/2140/02-Apr-2025',\n      status: 'Completed'\n    }, {\n      id: '29_aPQR/2140/02-Apr-2025',\n      status: 'Completed'\n    }, {\n      id: '29_aPQR/2140/02-Apr-2025',\n      status: 'In Progress'\n    }, {\n      id: '29_aPQR/2140/02-Apr-2025',\n      status: 'In Progress'\n    }, {\n      id: '29_aPQR/2140/02-Apr-2025',\n      status: 'Not Defined'\n    }];\n  }\n  ngOnInit() {}\n  static {\n    this.ɵfac = function DashboardComponent_Factory(t) {\n      return new (t || DashboardComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardComponent,\n      selectors: [[\"app-dashboard\"]],\n      decls: 158,\n      vars: 5,\n      consts: [[1, \"dashboard-container\"], [1, \"dashboard-grid\"], [1, \"dashboard-card\"], [1, \"card-content\"], [\"class\", \"stat-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"card-footer\"], [1, \"footer-text\"], [1, \"card-header\"], [1, \"dashboard-card\", \"wide-card\"], [1, \"scheduler-header\"], [1, \"month-selector\"], [\"mat-icon-button\", \"\"], [1, \"month\"], [1, \"scheduler-content\"], [1, \"scheduler-table\"], [1, \"scheduler-row\", \"header\"], [1, \"scheduler-cell\"], [\"class\", \"scheduler-row\", 4, \"ngFor\", \"ngForOf\"], [1, \"calendar-header\"], [1, \"calendar-content\"], [1, \"calendar-grid\"], [1, \"calendar-weekdays\"], [1, \"weekday\"], [1, \"calendar-days\"], [1, \"day\", \"prev-month\"], [1, \"day\"], [1, \"day\", \"published\"], [1, \"day\", \"delayed\"], [1, \"calendar-legend\"], [1, \"legend-item\"], [1, \"legend-color\", \"published\"], [1, \"legend-text\"], [1, \"legend-color\", \"upcoming\"], [1, \"legend-color\", \"delayed\"], [1, \"stat-item\"], [1, \"stat-label\"], [1, \"stat-value\"], [1, \"scheduler-row\"], [1, \"scheduler-cell\", \"status\", 3, \"ngClass\"]],\n      template: function DashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵtemplate(4, DashboardComponent_div_4_Template, 5, 2, \"div\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 5)(6, \"span\", 6);\n          i0.ɵɵtext(7, \"*previous day data\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(8, \"div\", 2)(9, \"div\", 3);\n          i0.ɵɵtemplate(10, DashboardComponent_div_10_Template, 5, 2, \"div\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 5)(12, \"span\", 6);\n          i0.ɵɵtext(13, \"*previous day data\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(14, \"div\", 2)(15, \"div\", 3);\n          i0.ɵɵtemplate(16, DashboardComponent_div_16_Template, 5, 2, \"div\", 4);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"div\", 2)(18, \"div\", 7)(19, \"h3\");\n          i0.ɵɵtext(20, \"My Task\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(21, \"div\", 3);\n          i0.ɵɵtemplate(22, DashboardComponent_div_22_Template, 5, 2, \"div\", 4);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(23, \"div\", 8)(24, \"div\", 7)(25, \"h3\");\n          i0.ɵɵtext(26, \"aPQR Scheduler\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(27, \"div\", 3)(28, \"div\", 9)(29, \"div\", 10)(30, \"button\", 11)(31, \"mat-icon\");\n          i0.ɵɵtext(32, \"chevron_left\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(33, \"span\", 12);\n          i0.ɵɵtext(34, \"April 2025\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"button\", 11)(36, \"mat-icon\");\n          i0.ɵɵtext(37, \"chevron_right\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(38, \"div\", 13)(39, \"div\", 14)(40, \"div\", 15)(41, \"div\", 16);\n          i0.ɵɵtext(42, \"Product\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(43, DashboardComponent_div_43_Template, 5, 3, \"div\", 17);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(44, \"div\", 8)(45, \"div\", 7)(46, \"h3\");\n          i0.ɵɵtext(47, \"aPQR Calender\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(48, \"div\", 3)(49, \"div\", 18)(50, \"div\", 10)(51, \"button\", 11)(52, \"mat-icon\");\n          i0.ɵɵtext(53, \"chevron_left\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(54, \"span\", 12);\n          i0.ɵɵtext(55, \"April 2025\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"button\", 11)(57, \"mat-icon\");\n          i0.ɵɵtext(58, \"chevron_right\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(59, \"div\", 19)(60, \"div\", 20)(61, \"div\", 21)(62, \"div\", 22);\n          i0.ɵɵtext(63, \"Mo\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"div\", 22);\n          i0.ɵɵtext(65, \"Tu\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"div\", 22);\n          i0.ɵɵtext(67, \"We\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(68, \"div\", 22);\n          i0.ɵɵtext(69, \"Th\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(70, \"div\", 22);\n          i0.ɵɵtext(71, \"Fr\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(72, \"div\", 22);\n          i0.ɵɵtext(73, \"Sa\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(74, \"div\", 22);\n          i0.ɵɵtext(75, \"Su\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(76, \"div\", 23)(77, \"div\", 24);\n          i0.ɵɵtext(78, \"28\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"div\", 24);\n          i0.ɵɵtext(80, \"29\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(81, \"div\", 24);\n          i0.ɵɵtext(82, \"30\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(83, \"div\", 24);\n          i0.ɵɵtext(84, \"31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(85, \"div\", 25);\n          i0.ɵɵtext(86, \"1\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(87, \"div\", 25);\n          i0.ɵɵtext(88, \"2\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(89, \"div\", 25);\n          i0.ɵɵtext(90, \"3\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(91, \"div\", 25);\n          i0.ɵɵtext(92, \"4\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(93, \"div\", 25);\n          i0.ɵɵtext(94, \"5\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(95, \"div\", 25);\n          i0.ɵɵtext(96, \"6\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(97, \"div\", 25);\n          i0.ɵɵtext(98, \"7\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(99, \"div\", 25);\n          i0.ɵɵtext(100, \"8\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(101, \"div\", 25);\n          i0.ɵɵtext(102, \"9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(103, \"div\", 25);\n          i0.ɵɵtext(104, \"10\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(105, \"div\", 25);\n          i0.ɵɵtext(106, \"11\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(107, \"div\", 25);\n          i0.ɵɵtext(108, \"12\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(109, \"div\", 25);\n          i0.ɵɵtext(110, \"13\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(111, \"div\", 26);\n          i0.ɵɵtext(112, \"14\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(113, \"div\", 25);\n          i0.ɵɵtext(114, \"15\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(115, \"div\", 25);\n          i0.ɵɵtext(116, \"16\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(117, \"div\", 25);\n          i0.ɵɵtext(118, \"17\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(119, \"div\", 25);\n          i0.ɵɵtext(120, \"18\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(121, \"div\", 25);\n          i0.ɵɵtext(122, \"19\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(123, \"div\", 27);\n          i0.ɵɵtext(124, \"20\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(125, \"div\", 25);\n          i0.ɵɵtext(126, \"21\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(127, \"div\", 25);\n          i0.ɵɵtext(128, \"22\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(129, \"div\", 25);\n          i0.ɵɵtext(130, \"23\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(131, \"div\", 25);\n          i0.ɵɵtext(132, \"24\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(133, \"div\", 25);\n          i0.ɵɵtext(134, \"25\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(135, \"div\", 25);\n          i0.ɵɵtext(136, \"26\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(137, \"div\", 25);\n          i0.ɵɵtext(138, \"27\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(139, \"div\", 25);\n          i0.ɵɵtext(140, \"28\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(141, \"div\", 25);\n          i0.ɵɵtext(142, \"29\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(143, \"div\", 25);\n          i0.ɵɵtext(144, \"30\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(145, \"div\", 28)(146, \"div\", 29);\n          i0.ɵɵelement(147, \"div\", 30);\n          i0.ɵɵelementStart(148, \"div\", 31);\n          i0.ɵɵtext(149, \"Published\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(150, \"div\", 29);\n          i0.ɵɵelement(151, \"div\", 32);\n          i0.ɵɵelementStart(152, \"div\", 31);\n          i0.ɵɵtext(153, \"Upcoming\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(154, \"div\", 29);\n          i0.ɵɵelement(155, \"div\", 33);\n          i0.ɵɵelementStart(156, \"div\", 31);\n          i0.ɵɵtext(157, \"Delayed\");\n          i0.ɵɵelementEnd()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngForOf\", ctx.batchStats);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngForOf\", ctx.deviationStats);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngForOf\", ctx.apqrStats);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngForOf\", ctx.taskStats);\n          i0.ɵɵadvance(21);\n          i0.ɵɵproperty(\"ngForOf\", ctx.schedulerItems);\n        }\n      },\n      dependencies: [i1.NgClass, i1.NgForOf, i2.MatIcon, i3.MatIconButton],\n      styles: [\".dashboard-container[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n}\\n\\n.dashboard-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(4, 1fr);\\n  gap: 1.5rem;\\n}\\n\\n.dashboard-card[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\\n  overflow: hidden;\\n}\\n\\n.wide-card[_ngcontent-%COMP%] {\\n  grid-column: span 2;\\n}\\n\\n.card-header[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n  border-bottom: 1px solid #f0f0f0;\\n}\\n\\n.card-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.1rem;\\n  font-weight: 500;\\n}\\n\\n.card-content[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n}\\n\\n.card-footer[_ngcontent-%COMP%] {\\n  padding: 0.5rem 1rem;\\n  background-color: #f9f9f9;\\n  font-size: 0.8rem;\\n  color: #666;\\n  text-align: right;\\n}\\n\\n.stat-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 0.5rem 0;\\n  border-bottom: 1px solid #f0f0f0;\\n}\\n\\n.stat-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n\\n.stat-label[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: #333;\\n}\\n\\n.stat-value[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  font-weight: 500;\\n  color: #2196f3;\\n}\\n\\n\\n\\n.scheduler-header[_ngcontent-%COMP%], .calendar-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 1rem;\\n}\\n\\n.month-selector[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.month[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  font-weight: 500;\\n  margin: 0 0.5rem;\\n}\\n\\n.scheduler-table[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.scheduler-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  border-bottom: 1px solid #f0f0f0;\\n}\\n\\n.scheduler-row.header[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n}\\n\\n.scheduler-cell[_ngcontent-%COMP%] {\\n  padding: 0.75rem 0.5rem;\\n  flex: 1;\\n}\\n\\n.scheduler-cell.status[_ngcontent-%COMP%] {\\n  flex: 0 0 100px;\\n  text-align: right;\\n}\\n\\n.status.completed[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n}\\n\\n.status.in-progress[_ngcontent-%COMP%] {\\n  color: #ff9800;\\n}\\n\\n.status.not-defined[_ngcontent-%COMP%] {\\n  color: #9e9e9e;\\n}\\n\\n\\n\\n.calendar-grid[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n\\n.calendar-weekdays[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(7, 1fr);\\n  text-align: center;\\n  font-weight: 500;\\n  margin-bottom: 0.5rem;\\n}\\n\\n.weekday[_ngcontent-%COMP%] {\\n  padding: 0.5rem;\\n  font-size: 0.8rem;\\n  color: #666;\\n}\\n\\n.calendar-days[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(7, 1fr);\\n  gap: 0.5rem;\\n}\\n\\n.day[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  height: 2rem;\\n  border-radius: 4px;\\n  font-size: 0.9rem;\\n  cursor: pointer;\\n}\\n\\n.day[_ngcontent-%COMP%]:hover {\\n  background-color: #f5f5f5;\\n}\\n\\n.day.prev-month[_ngcontent-%COMP%], .day.next-month[_ngcontent-%COMP%] {\\n  color: #bdbdbd;\\n}\\n\\n.day.published[_ngcontent-%COMP%] {\\n  background-color: #e8f5e9;\\n  color: #4caf50;\\n  position: relative;\\n}\\n\\n.day.published[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: 4px;\\n  width: 4px;\\n  height: 4px;\\n  border-radius: 50%;\\n  background-color: #4caf50;\\n}\\n\\n.day.upcoming[_ngcontent-%COMP%] {\\n  background-color: #fff8e1;\\n  color: #ff9800;\\n  position: relative;\\n}\\n\\n.day.upcoming[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: 4px;\\n  width: 4px;\\n  height: 4px;\\n  border-radius: 50%;\\n  background-color: #ff9800;\\n}\\n\\n.day.delayed[_ngcontent-%COMP%] {\\n  background-color: #ffebee;\\n  color: #f44336;\\n  position: relative;\\n}\\n\\n.day.delayed[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: 4px;\\n  width: 4px;\\n  height: 4px;\\n  border-radius: 50%;\\n  background-color: #f44336;\\n}\\n\\n.calendar-legend[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  gap: 1rem;\\n}\\n\\n.legend-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-size: 0.8rem;\\n}\\n\\n.legend-color[_ngcontent-%COMP%] {\\n  width: 10px;\\n  height: 10px;\\n  border-radius: 50%;\\n  margin-right: 0.5rem;\\n}\\n\\n.legend-color.published[_ngcontent-%COMP%] {\\n  background-color: #4caf50;\\n}\\n\\n.legend-color.upcoming[_ngcontent-%COMP%] {\\n  background-color: #ff9800;\\n}\\n\\n.legend-color.delayed[_ngcontent-%COMP%] {\\n  background-color: #f44336;\\n}\\n\\n\\n\\n@media (max-width: 1200px) {\\n  .dashboard-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .dashboard-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .wide-card[_ngcontent-%COMP%] {\\n    grid-column: span 1;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "stat_r5", "label", "value", "stat_r6", "stat_r7", "stat_r8", "item_r9", "id", "ɵɵproperty", "status", "toLowerCase", "replace", "ɵɵtextInterpolate1", "DashboardComponent", "constructor", "batchStats", "deviationStats", "apqrStats", "taskStats", "schedulerItems", "ngOnInit", "selectors", "decls", "vars", "consts", "template", "DashboardComponent_Template", "rf", "ctx", "ɵɵtemplate", "DashboardComponent_div_4_Template", "DashboardComponent_div_10_Template", "DashboardComponent_div_16_Template", "DashboardComponent_div_22_Template", "DashboardComponent_div_43_Template", "ɵɵelement"], "sources": ["C:\\cal_wf\\Augment\\4-img\\apqr400bl\\src\\app\\layout\\components\\dashboard\\dashboard.component.ts", "C:\\cal_wf\\Augment\\4-img\\apqr400bl\\src\\app\\layout\\components\\dashboard\\dashboard.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\n\n@Component({\n  selector: 'app-dashboard',\n  templateUrl: './dashboard.component.html',\n  styleUrls: ['./dashboard.component.scss']\n})\nexport class DashboardComponent implements OnInit {\n  batchStats = [\n    { label: 'Batch Released', value: '23' },\n    { label: 'Batch Rejected', value: '02' },\n    { label: 'Batch in process', value: '14' }\n  ];\n\n  deviationStats = [\n    { label: 'Deviation', value: '03' },\n    { label: 'CC', value: '05' },\n    { label: 'CAPA', value: '06' },\n    { label: 'Complaint', value: '02' }\n  ];\n\n  apqrStats = [\n    { label: 'aPQR till date', value: '33' },\n    { label: 'aPQR year long', value: '05' },\n    { label: 'Products', value: '300' }\n  ];\n\n  taskStats = [\n    { label: 'Assignments', value: '05' },\n    { label: 'Spreadsheet Return', value: '03' },\n    { label: 'Sections Update', value: '02' }\n  ];\n\n  schedulerItems = [\n    { id: '29_aPQR/2140/02-Apr-2025', status: 'Completed' },\n    { id: '29_aPQR/2140/02-Apr-2025', status: 'Completed' },\n    { id: '29_aPQR/2140/02-Apr-2025', status: 'In Progress' },\n    { id: '29_aPQR/2140/02-Apr-2025', status: 'In Progress' },\n    { id: '29_aPQR/2140/02-Apr-2025', status: 'Not Defined' }\n  ];\n\n  constructor() { }\n\n  ngOnInit(): void {\n  }\n}\n", "<div class=\"dashboard-container\">\n  <div class=\"dashboard-grid\">\n    <!-- Batch Stats Card -->\n    <div class=\"dashboard-card\">\n      <div class=\"card-content\">\n        <div class=\"stat-item\" *ngFor=\"let stat of batchStats\">\n          <div class=\"stat-label\">{{ stat.label }}</div>\n          <div class=\"stat-value\">{{ stat.value }}</div>\n        </div>\n      </div>\n      <div class=\"card-footer\">\n        <span class=\"footer-text\">*previous day data</span>\n      </div>\n    </div>\n\n    <!-- Deviation Stats Card -->\n    <div class=\"dashboard-card\">\n      <div class=\"card-content\">\n        <div class=\"stat-item\" *ngFor=\"let stat of deviationStats\">\n          <div class=\"stat-label\">{{ stat.label }}</div>\n          <div class=\"stat-value\">{{ stat.value }}</div>\n        </div>\n      </div>\n      <div class=\"card-footer\">\n        <span class=\"footer-text\">*previous day data</span>\n      </div>\n    </div>\n\n    <!-- APQR Stats Card -->\n    <div class=\"dashboard-card\">\n      <div class=\"card-content\">\n        <div class=\"stat-item\" *ngFor=\"let stat of apqrStats\">\n          <div class=\"stat-label\">{{ stat.label }}</div>\n          <div class=\"stat-value\">{{ stat.value }}</div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Task Stats Card -->\n    <div class=\"dashboard-card\">\n      <div class=\"card-header\">\n        <h3>My Task</h3>\n      </div>\n      <div class=\"card-content\">\n        <div class=\"stat-item\" *ngFor=\"let stat of taskStats\">\n          <div class=\"stat-label\">{{ stat.label }}</div>\n          <div class=\"stat-value\">{{ stat.value }}</div>\n        </div>\n      </div>\n    </div>\n\n    <!-- APQR Scheduler Card -->\n    <div class=\"dashboard-card wide-card\">\n      <div class=\"card-header\">\n        <h3>aPQR Scheduler</h3>\n      </div>\n      <div class=\"card-content\">\n        <div class=\"scheduler-header\">\n          <div class=\"month-selector\">\n            <button mat-icon-button><mat-icon>chevron_left</mat-icon></button>\n            <span class=\"month\">April 2025</span>\n            <button mat-icon-button><mat-icon>chevron_right</mat-icon></button>\n          </div>\n        </div>\n        <div class=\"scheduler-content\">\n          <div class=\"scheduler-table\">\n            <div class=\"scheduler-row header\">\n              <div class=\"scheduler-cell\">Product</div>\n            </div>\n            <div class=\"scheduler-row\" *ngFor=\"let item of schedulerItems\">\n              <div class=\"scheduler-cell\">{{ item.id }}</div>\n              <div class=\"scheduler-cell status\" [ngClass]=\"item.status.toLowerCase().replace(' ', '-')\">\n                {{ item.status }}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- APQR Calendar Card -->\n    <div class=\"dashboard-card wide-card\">\n      <div class=\"card-header\">\n        <h3>aPQR Calender</h3>\n      </div>\n      <div class=\"card-content\">\n        <div class=\"calendar-header\">\n          <div class=\"month-selector\">\n            <button mat-icon-button><mat-icon>chevron_left</mat-icon></button>\n            <span class=\"month\">April 2025</span>\n            <button mat-icon-button><mat-icon>chevron_right</mat-icon></button>\n          </div>\n        </div>\n        <div class=\"calendar-content\">\n          <div class=\"calendar-grid\">\n            <div class=\"calendar-weekdays\">\n              <div class=\"weekday\">Mo</div>\n              <div class=\"weekday\">Tu</div>\n              <div class=\"weekday\">We</div>\n              <div class=\"weekday\">Th</div>\n              <div class=\"weekday\">Fr</div>\n              <div class=\"weekday\">Sa</div>\n              <div class=\"weekday\">Su</div>\n            </div>\n            <div class=\"calendar-days\">\n              <!-- Previous month days -->\n              <div class=\"day prev-month\">28</div>\n              <div class=\"day prev-month\">29</div>\n              <div class=\"day prev-month\">30</div>\n              <div class=\"day prev-month\">31</div>\n              \n              <!-- Current month days -->\n              <div class=\"day\">1</div>\n              <div class=\"day\">2</div>\n              <div class=\"day\">3</div>\n              <div class=\"day\">4</div>\n              <div class=\"day\">5</div>\n              <div class=\"day\">6</div>\n              <div class=\"day\">7</div>\n              <div class=\"day\">8</div>\n              <div class=\"day\">9</div>\n              <div class=\"day\">10</div>\n              <div class=\"day\">11</div>\n              <div class=\"day\">12</div>\n              <div class=\"day\">13</div>\n              <div class=\"day published\">14</div>\n              <div class=\"day\">15</div>\n              <div class=\"day\">16</div>\n              <div class=\"day\">17</div>\n              <div class=\"day\">18</div>\n              <div class=\"day\">19</div>\n              <div class=\"day delayed\">20</div>\n              <div class=\"day\">21</div>\n              <div class=\"day\">22</div>\n              <div class=\"day\">23</div>\n              <div class=\"day\">24</div>\n              <div class=\"day\">25</div>\n              <div class=\"day\">26</div>\n              <div class=\"day\">27</div>\n              <div class=\"day\">28</div>\n              <div class=\"day\">29</div>\n              <div class=\"day\">30</div>\n            </div>\n          </div>\n          <div class=\"calendar-legend\">\n            <div class=\"legend-item\">\n              <div class=\"legend-color published\"></div>\n              <div class=\"legend-text\">Published</div>\n            </div>\n            <div class=\"legend-item\">\n              <div class=\"legend-color upcoming\"></div>\n              <div class=\"legend-text\">Upcoming</div>\n            </div>\n            <div class=\"legend-item\">\n              <div class=\"legend-color delayed\"></div>\n              <div class=\"legend-text\">Delayed</div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;;;;;ICKQA,EAAA,CAAAC,cAAA,cAAuD;IAC7BD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC9CH,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADtBH,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAC,OAAA,CAAAC,KAAA,CAAgB;IAChBP,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAC,OAAA,CAAAE,KAAA,CAAgB;;;;;IAW1CR,EAAA,CAAAC,cAAA,cAA2D;IACjCD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC9CH,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADtBH,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAI,OAAA,CAAAF,KAAA,CAAgB;IAChBP,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAI,OAAA,CAAAD,KAAA,CAAgB;;;;;IAW1CR,EAAA,CAAAC,cAAA,cAAsD;IAC5BD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC9CH,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADtBH,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAK,OAAA,CAAAH,KAAA,CAAgB;IAChBP,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAK,OAAA,CAAAF,KAAA,CAAgB;;;;;IAW1CR,EAAA,CAAAC,cAAA,cAAsD;IAC5BD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC9CH,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADtBH,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAM,OAAA,CAAAJ,KAAA,CAAgB;IAChBP,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAM,OAAA,CAAAH,KAAA,CAAgB;;;;;IAuBtCR,EAAA,CAAAC,cAAA,cAA+D;IACjCD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC/CH,EAAA,CAAAC,cAAA,cAA2F;IACzFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAHsBH,EAAA,CAAAI,SAAA,GAAa;IAAbJ,EAAA,CAAAK,iBAAA,CAAAO,OAAA,CAAAC,EAAA,CAAa;IACNb,EAAA,CAAAI,SAAA,GAAuD;IAAvDJ,EAAA,CAAAc,UAAA,YAAAF,OAAA,CAAAG,MAAA,CAAAC,WAAA,GAAAC,OAAA,WAAuD;IACxFjB,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAkB,kBAAA,MAAAN,OAAA,CAAAG,MAAA,MACF;;;ADlEd,OAAM,MAAOI,kBAAkB;EAkC7BC,YAAA;IAjCA,KAAAC,UAAU,GAAG,CACX;MAAEd,KAAK,EAAE,gBAAgB;MAAEC,KAAK,EAAE;IAAI,CAAE,EACxC;MAAED,KAAK,EAAE,gBAAgB;MAAEC,KAAK,EAAE;IAAI,CAAE,EACxC;MAAED,KAAK,EAAE,kBAAkB;MAAEC,KAAK,EAAE;IAAI,CAAE,CAC3C;IAED,KAAAc,cAAc,GAAG,CACf;MAAEf,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAI,CAAE,EACnC;MAAED,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAI,CAAE,EAC5B;MAAED,KAAK,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAI,CAAE,EAC9B;MAAED,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAI,CAAE,CACpC;IAED,KAAAe,SAAS,GAAG,CACV;MAAEhB,KAAK,EAAE,gBAAgB;MAAEC,KAAK,EAAE;IAAI,CAAE,EACxC;MAAED,KAAK,EAAE,gBAAgB;MAAEC,KAAK,EAAE;IAAI,CAAE,EACxC;MAAED,KAAK,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAK,CAAE,CACpC;IAED,KAAAgB,SAAS,GAAG,CACV;MAAEjB,KAAK,EAAE,aAAa;MAAEC,KAAK,EAAE;IAAI,CAAE,EACrC;MAAED,KAAK,EAAE,oBAAoB;MAAEC,KAAK,EAAE;IAAI,CAAE,EAC5C;MAAED,KAAK,EAAE,iBAAiB;MAAEC,KAAK,EAAE;IAAI,CAAE,CAC1C;IAED,KAAAiB,cAAc,GAAG,CACf;MAAEZ,EAAE,EAAE,0BAA0B;MAAEE,MAAM,EAAE;IAAW,CAAE,EACvD;MAAEF,EAAE,EAAE,0BAA0B;MAAEE,MAAM,EAAE;IAAW,CAAE,EACvD;MAAEF,EAAE,EAAE,0BAA0B;MAAEE,MAAM,EAAE;IAAa,CAAE,EACzD;MAAEF,EAAE,EAAE,0BAA0B;MAAEE,MAAM,EAAE;IAAa,CAAE,EACzD;MAAEF,EAAE,EAAE,0BAA0B;MAAEE,MAAM,EAAE;IAAa,CAAE,CAC1D;EAEe;EAEhBW,QAAQA,CAAA,GACR;;;uBArCWP,kBAAkB;IAAA;EAAA;;;YAAlBA,kBAAkB;MAAAQ,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP/BjC,EAAA,CAAAC,cAAA,aAAiC;UAKzBD,EAAA,CAAAmC,UAAA,IAAAC,iCAAA,iBAGM;UACRpC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,aAAyB;UACGD,EAAA,CAAAE,MAAA,yBAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAKvDH,EAAA,CAAAC,cAAA,aAA4B;UAExBD,EAAA,CAAAmC,UAAA,KAAAE,kCAAA,iBAGM;UACRrC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,cAAyB;UACGD,EAAA,CAAAE,MAAA,0BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAKvDH,EAAA,CAAAC,cAAA,cAA4B;UAExBD,EAAA,CAAAmC,UAAA,KAAAG,kCAAA,iBAGM;UACRtC,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAC,cAAA,cAA4B;UAEpBD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAElBH,EAAA,CAAAC,cAAA,cAA0B;UACxBD,EAAA,CAAAmC,UAAA,KAAAI,kCAAA,iBAGM;UACRvC,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAC,cAAA,cAAsC;UAE9BD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEzBH,EAAA,CAAAC,cAAA,cAA0B;UAGcD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzDH,EAAA,CAAAC,cAAA,gBAAoB;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACrCH,EAAA,CAAAC,cAAA,kBAAwB;UAAUD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAG9DH,EAAA,CAAAC,cAAA,eAA+B;UAGGD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAE3CH,EAAA,CAAAmC,UAAA,KAAAK,kCAAA,kBAKM;UACRxC,EAAA,CAAAG,YAAA,EAAM;UAMZH,EAAA,CAAAC,cAAA,cAAsC;UAE9BD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAExBH,EAAA,CAAAC,cAAA,cAA0B;UAGcD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzDH,EAAA,CAAAC,cAAA,gBAAoB;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACrCH,EAAA,CAAAC,cAAA,kBAAwB;UAAUD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAG9DH,EAAA,CAAAC,cAAA,eAA8B;UAGHD,EAAA,CAAAE,MAAA,UAAE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC7BH,EAAA,CAAAC,cAAA,eAAqB;UAAAD,EAAA,CAAAE,MAAA,UAAE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC7BH,EAAA,CAAAC,cAAA,eAAqB;UAAAD,EAAA,CAAAE,MAAA,UAAE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC7BH,EAAA,CAAAC,cAAA,eAAqB;UAAAD,EAAA,CAAAE,MAAA,UAAE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC7BH,EAAA,CAAAC,cAAA,eAAqB;UAAAD,EAAA,CAAAE,MAAA,UAAE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC7BH,EAAA,CAAAC,cAAA,eAAqB;UAAAD,EAAA,CAAAE,MAAA,UAAE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC7BH,EAAA,CAAAC,cAAA,eAAqB;UAAAD,EAAA,CAAAE,MAAA,UAAE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAE/BH,EAAA,CAAAC,cAAA,eAA2B;UAEGD,EAAA,CAAAE,MAAA,UAAE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACpCH,EAAA,CAAAC,cAAA,eAA4B;UAAAD,EAAA,CAAAE,MAAA,UAAE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACpCH,EAAA,CAAAC,cAAA,eAA4B;UAAAD,EAAA,CAAAE,MAAA,UAAE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACpCH,EAAA,CAAAC,cAAA,eAA4B;UAAAD,EAAA,CAAAE,MAAA,UAAE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAGpCH,EAAA,CAAAC,cAAA,eAAiB;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACxBH,EAAA,CAAAC,cAAA,eAAiB;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACxBH,EAAA,CAAAC,cAAA,eAAiB;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACxBH,EAAA,CAAAC,cAAA,eAAiB;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACxBH,EAAA,CAAAC,cAAA,eAAiB;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACxBH,EAAA,CAAAC,cAAA,eAAiB;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACxBH,EAAA,CAAAC,cAAA,eAAiB;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACxBH,EAAA,CAAAC,cAAA,eAAiB;UAAAD,EAAA,CAAAE,MAAA,UAAC;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACxBH,EAAA,CAAAC,cAAA,gBAAiB;UAAAD,EAAA,CAAAE,MAAA,UAAC;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACxBH,EAAA,CAAAC,cAAA,gBAAiB;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACzBH,EAAA,CAAAC,cAAA,gBAAiB;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACzBH,EAAA,CAAAC,cAAA,gBAAiB;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACzBH,EAAA,CAAAC,cAAA,gBAAiB;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACzBH,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACnCH,EAAA,CAAAC,cAAA,gBAAiB;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACzBH,EAAA,CAAAC,cAAA,gBAAiB;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACzBH,EAAA,CAAAC,cAAA,gBAAiB;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACzBH,EAAA,CAAAC,cAAA,gBAAiB;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACzBH,EAAA,CAAAC,cAAA,gBAAiB;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACzBH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACjCH,EAAA,CAAAC,cAAA,gBAAiB;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACzBH,EAAA,CAAAC,cAAA,gBAAiB;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACzBH,EAAA,CAAAC,cAAA,gBAAiB;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACzBH,EAAA,CAAAC,cAAA,gBAAiB;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACzBH,EAAA,CAAAC,cAAA,gBAAiB;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACzBH,EAAA,CAAAC,cAAA,gBAAiB;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACzBH,EAAA,CAAAC,cAAA,gBAAiB;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACzBH,EAAA,CAAAC,cAAA,gBAAiB;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACzBH,EAAA,CAAAC,cAAA,gBAAiB;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACzBH,EAAA,CAAAC,cAAA,gBAAiB;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAG7BH,EAAA,CAAAC,cAAA,gBAA6B;UAEzBD,EAAA,CAAAyC,SAAA,gBAA0C;UAC1CzC,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,kBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAE1CH,EAAA,CAAAC,cAAA,gBAAyB;UACvBD,EAAA,CAAAyC,SAAA,gBAAyC;UACzCzC,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAEzCH,EAAA,CAAAC,cAAA,gBAAyB;UACvBD,EAAA,CAAAyC,SAAA,gBAAwC;UACxCzC,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAM;;;UAtJJH,EAAA,CAAAI,SAAA,GAAa;UAAbJ,EAAA,CAAAc,UAAA,YAAAoB,GAAA,CAAAb,UAAA,CAAa;UAabrB,EAAA,CAAAI,SAAA,GAAiB;UAAjBJ,EAAA,CAAAc,UAAA,YAAAoB,GAAA,CAAAZ,cAAA,CAAiB;UAajBtB,EAAA,CAAAI,SAAA,GAAY;UAAZJ,EAAA,CAAAc,UAAA,YAAAoB,GAAA,CAAAX,SAAA,CAAY;UAaZvB,EAAA,CAAAI,SAAA,GAAY;UAAZJ,EAAA,CAAAc,UAAA,YAAAoB,GAAA,CAAAV,SAAA,CAAY;UAyBJxB,EAAA,CAAAI,SAAA,IAAiB;UAAjBJ,EAAA,CAAAc,UAAA,YAAAoB,GAAA,CAAAT,cAAA,CAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}