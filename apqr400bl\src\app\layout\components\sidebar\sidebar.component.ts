import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { SelectionService } from '../../../shared/selection.service';

@Component({
  selector: 'app-sidebar',
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.scss']
})
export class SidebarComponent implements OnInit {
  menuItems = [
    { 
      name: 'aPQR Flow', 
      icon: 'assignment', 
      route: '/layout/apqr-flow',
      active: false
    },
    { 
      name: 'Reports', 
      icon: 'summarize', 
      route: '/layout/reports',
      active: false
    },
    { 
      name: 'Analytics', 
      icon: 'insights', 
      route: '/layout/analytics',
      active: false
    },
    { 
      name: 'Info', 
      icon: 'info', 
      route: '/layout',
      active: false,
      position: 'bottom'
    },
    { 
      name: 'Notifications', 
      icon: 'notifications', 
      route: '/layout',
      active: false,
      position: 'bottom'
    }
  ];

  selectedModule = '';

  constructor(
    private router: Router,
    private selectionService: SelectionService
  ) { }

  ngOnInit(): void {
    this.selectionService.selectedModule$.subscribe(module => {
      this.selectedModule = module;
    });
    
    // Set active menu based on current route
    const currentRoute = this.router.url;
    this.menuItems.forEach(item => {
      item.active = currentRoute.includes(item.route) && item.route !== '/layout';
    });
    
    // If we're on the dashboard, don't highlight any menu
    if (currentRoute === '/layout') {
      this.menuItems.forEach(item => {
        item.active = false;
      });
    }
  }

  navigateTo(item: any): void {
    this.menuItems.forEach(menuItem => {
      menuItem.active = menuItem === item;
    });
    this.router.navigate([item.route]);
  }
}
