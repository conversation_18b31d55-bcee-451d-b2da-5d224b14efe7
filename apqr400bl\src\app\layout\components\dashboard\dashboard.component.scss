.dashboard-container {
  padding: 1rem;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
}

.dashboard-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.wide-card {
  grid-column: span 2;
}

.card-header {
  padding: 1rem;
  border-bottom: 1px solid #f0f0f0;
}

.card-header h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
}

.card-content {
  padding: 1rem;
}

.card-footer {
  padding: 0.5rem 1rem;
  background-color: #f9f9f9;
  font-size: 0.8rem;
  color: #666;
  text-align: right;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f0f0f0;
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-label {
  font-size: 0.9rem;
  color: #333;
}

.stat-value {
  font-size: 0.9rem;
  font-weight: 500;
  color: #2196f3;
}

/* Scheduler styles */
.scheduler-header, .calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.month-selector {
  display: flex;
  align-items: center;
}

.month {
  font-size: 1rem;
  font-weight: 500;
  margin: 0 0.5rem;
}

.scheduler-table {
  width: 100%;
}

.scheduler-row {
  display: flex;
  border-bottom: 1px solid #f0f0f0;
}

.scheduler-row.header {
  font-weight: 500;
}

.scheduler-cell {
  padding: 0.75rem 0.5rem;
  flex: 1;
}

.scheduler-cell.status {
  flex: 0 0 100px;
  text-align: right;
}

.status.completed {
  color: #4caf50;
}

.status.in-progress {
  color: #ff9800;
}

.status.not-defined {
  color: #9e9e9e;
}

/* Calendar styles */
.calendar-grid {
  margin-bottom: 1rem;
}

.calendar-weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  text-align: center;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.weekday {
  padding: 0.5rem;
  font-size: 0.8rem;
  color: #666;
}

.calendar-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 0.5rem;
}

.day {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 2rem;
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
}

.day:hover {
  background-color: #f5f5f5;
}

.day.prev-month, .day.next-month {
  color: #bdbdbd;
}

.day.published {
  background-color: #e8f5e9;
  color: #4caf50;
  position: relative;
}

.day.published::after {
  content: '';
  position: absolute;
  bottom: 4px;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: #4caf50;
}

.day.upcoming {
  background-color: #fff8e1;
  color: #ff9800;
  position: relative;
}

.day.upcoming::after {
  content: '';
  position: absolute;
  bottom: 4px;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: #ff9800;
}

.day.delayed {
  background-color: #ffebee;
  color: #f44336;
  position: relative;
}

.day.delayed::after {
  content: '';
  position: absolute;
  bottom: 4px;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: #f44336;
}

.calendar-legend {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

.legend-item {
  display: flex;
  align-items: center;
  font-size: 0.8rem;
}

.legend-color {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 0.5rem;
}

.legend-color.published {
  background-color: #4caf50;
}

.legend-color.upcoming {
  background-color: #ff9800;
}

.legend-color.delayed {
  background-color: #f44336;
}

/* Responsive styles */
@media (max-width: 1200px) {
  .dashboard-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .dashboard-grid {
    grid-template-columns: 1fr;
  }
  
  .wide-card {
    grid-column: span 1;
  }
}
