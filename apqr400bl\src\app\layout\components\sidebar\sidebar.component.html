<div class="sidebar">
  <div class="logo-container">
    <div class="logo">
      <img src="assets/caliber-logo.svg" alt="Caliber Logo">
    </div>
    <div class="module-name">
      <span class="caliber">Caliber</span> <span class="product-name">aPQR</span>
    </div>
  </div>
  
  <div class="menu-container">
    <div class="main-menu">
      <div 
        *ngFor="let item of menuItems" 
        class="menu-item" 
        [class.active]="item.active"
        [class.bottom-menu]="item.position === 'bottom'"
        (click)="navigateTo(item)">
        <mat-icon>{{item.icon}}</mat-icon>
        <span class="menu-text">{{item.name}}</span>
      </div>
    </div>
  </div>
</div>
