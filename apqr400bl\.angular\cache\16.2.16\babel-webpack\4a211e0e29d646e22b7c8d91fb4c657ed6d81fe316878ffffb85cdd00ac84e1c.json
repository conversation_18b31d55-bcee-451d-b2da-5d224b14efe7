{"ast": null, "code": "import { asyncScheduler } from '../scheduler/async';\nimport { throttle } from './throttle';\nimport { timer } from '../observable/timer';\nexport function throttleTime(duration, scheduler = asyncScheduler, config) {\n  const duration$ = timer(duration, scheduler);\n  return throttle(() => duration$, config);\n}", "map": {"version": 3, "names": ["asyncScheduler", "throttle", "timer", "throttleTime", "duration", "scheduler", "config", "duration$"], "sources": ["C:/cal_wf/Augment/3-img/apqr400bl/node_modules/rxjs/dist/esm/internal/operators/throttleTime.js"], "sourcesContent": ["import { asyncScheduler } from '../scheduler/async';\nimport { throttle } from './throttle';\nimport { timer } from '../observable/timer';\nexport function throttleTime(duration, scheduler = asyncScheduler, config) {\n    const duration$ = timer(duration, scheduler);\n    return throttle(() => duration$, config);\n}\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,oBAAoB;AACnD,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,KAAK,QAAQ,qBAAqB;AAC3C,OAAO,SAASC,YAAYA,CAACC,QAAQ,EAAEC,SAAS,GAAGL,cAAc,EAAEM,MAAM,EAAE;EACvE,MAAMC,SAAS,GAAGL,KAAK,CAACE,QAAQ,EAAEC,SAAS,CAAC;EAC5C,OAAOJ,QAAQ,CAAC,MAAMM,SAAS,EAAED,MAAM,CAAC;AAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}