.topnav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.page-title {
  display: flex;
  align-items: center;
}

h1 {
  font-size: 1.5rem;
  font-weight: 500;
  margin: 0;
  display: flex;
  align-items: center;
}

.info-icon {
  font-size: 1.2rem;
  width: 1.2rem;
  height: 1.2rem;
  margin-left: 0.5rem;
  color: #2196f3;
  cursor: pointer;
}

.topnav-actions {
  display: flex;
  align-items: center;
}

.module-info {
  display: flex;
  margin-right: 2rem;
}

.module-badge, .plant-badge {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  background-color: #f5f5f5;
  border-radius: 4px;
  margin-right: 0.5rem;
  font-size: 0.9rem;
}

.module-badge mat-icon, .plant-badge mat-icon {
  font-size: 1rem;
  width: 1rem;
  height: 1rem;
  margin-right: 0.5rem;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 0.5rem;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-greeting {
  display: flex;
  align-items: center;
  color: #333;
  font-size: 0.9rem;
  cursor: pointer;
}
