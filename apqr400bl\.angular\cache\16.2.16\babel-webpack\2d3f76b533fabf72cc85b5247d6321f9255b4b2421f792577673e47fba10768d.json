{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  loadChildren: () => import('./module/module.module').then(m => m.ModuleModule)\n}, {\n  path: 'plantselection',\n  loadChildren: () => import('./plantselection/plantselection.module').then(m => m.PlantselectionModule)\n}, {\n  path: '**',\n  redirectTo: '',\n  pathMatch: 'full'\n}];\nexport class AppRoutingModule {\n  static {\n    this.ɵfac = function AppRoutingModule_Factory(t) {\n      return new (t || AppRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forRoot(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "routes", "path", "loadChildren", "then", "m", "ModuleModule", "PlantselectionModule", "redirectTo", "pathMatch", "AppRoutingModule", "forRoot", "imports", "i1", "exports"], "sources": ["C:\\cal_wf\\Augment\\4-img\\apqr400bl\\src\\app\\app-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\n\nconst routes: Routes = [\n  {\n    path: '',\n    loadChildren: () => import('./module/module.module').then(m => m.ModuleModule)\n  },\n  {\n    path: 'plantselection',\n    loadChildren: () => import('./plantselection/plantselection.module').then(m => m.PlantselectionModule)\n  },\n  {\n    path: '**',\n    redirectTo: '',\n    pathMatch: 'full'\n  }\n];\n\n@NgModule({\n  imports: [RouterModule.forRoot(routes)],\n  exports: [RouterModule]\n})\nexport class AppRoutingModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;;;AAEtD,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,wBAAwB,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,YAAY;CAC9E,EACD;EACEJ,IAAI,EAAE,gBAAgB;EACtBC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,wCAAwC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACE,oBAAoB;CACtG,EACD;EACEL,IAAI,EAAE,IAAI;EACVM,UAAU,EAAE,EAAE;EACdC,SAAS,EAAE;CACZ,CACF;AAMD,OAAM,MAAOC,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAHjBV,YAAY,CAACW,OAAO,CAACV,MAAM,CAAC,EAC5BD,YAAY;IAAA;EAAA;;;2EAEXU,gBAAgB;IAAAE,OAAA,GAAAC,EAAA,CAAAb,YAAA;IAAAc,OAAA,GAFjBd,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}