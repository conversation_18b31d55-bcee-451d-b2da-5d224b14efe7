import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { LayoutComponent } from './layout.component';
import { DashboardComponent } from './components/dashboard/dashboard.component';
import { ApqrFlowComponent } from './components/apqr-flow/apqr-flow.component';
import { ReportsComponent } from './components/reports/reports.component';
import { AnalyticsComponent } from './components/analytics/analytics.component';

const routes: Routes = [
  {
    path: '',
    component: LayoutComponent,
    children: [
      {
        path: '',
        component: DashboardComponent
      },
      {
        path: 'apqr-flow',
        component: ApqrFlowComponent
      },
      {
        path: 'reports',
        component: ReportsComponent
      },
      {
        path: 'analytics',
        component: AnalyticsComponent
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class LayoutRoutingModule { }
