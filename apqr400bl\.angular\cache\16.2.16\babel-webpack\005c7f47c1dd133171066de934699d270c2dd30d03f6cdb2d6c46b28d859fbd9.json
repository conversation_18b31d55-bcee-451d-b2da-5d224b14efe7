{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../shared/selection.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/material/icon\";\nfunction ModuleComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 13);\n    i0.ɵɵlistener(\"click\", function ModuleComponent_div_8_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r3);\n      const module_r1 = restoredCtx.$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onModuleSelect(module_r1.name));\n    });\n    i0.ɵɵelementStart(1, \"div\", 14)(2, \"div\", 15)(3, \"mat-icon\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 16)(6, \"span\", 17);\n    i0.ɵɵtext(7, \"Caliber\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 18);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const module_r1 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"color\", module_r1.color);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(module_r1.icon);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(module_r1.name.replace(\"Caliber \", \"\"));\n  }\n}\nexport class ModuleComponent {\n  constructor(router, selectionService) {\n    this.router = router;\n    this.selectionService = selectionService;\n    this.modules = [{\n      name: 'Caliber aPQR',\n      icon: 'analytics',\n      color: '#00a99d'\n    }, {\n      name: 'Caliber CPV',\n      icon: 'description',\n      color: '#00a99d'\n    }, {\n      name: 'Caliber Metrix',\n      icon: 'bubble_chart',\n      color: '#00a99d'\n    }, {\n      name: 'Caliber Pulse',\n      icon: 'trending_up',\n      color: '#00a99d'\n    }];\n  }\n  ngOnInit() {}\n  onModuleSelect(moduleName) {\n    alert(`You selected ${moduleName}`);\n    this.selectionService.setSelectedModule(moduleName);\n    this.router.navigate(['/plantselection']);\n  }\n  static {\n    this.ɵfac = function ModuleComponent_Factory(t) {\n      return new (t || ModuleComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.SelectionService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ModuleComponent,\n      selectors: [[\"app-module\"]],\n      decls: 18,\n      vars: 1,\n      consts: [[1, \"module-selection-container\"], [1, \"left-panel\"], [1, \"quari-logo\"], [1, \"logo-image\"], [\"src\", \"assets/quariDark.png\", \"alt\", \"QUARI Logo\"], [1, \"right-panel\"], [1, \"scrollable-content\"], [1, \"module-grid\"], [\"class\", \"module-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"footer\"], [1, \"footer-links\"], [\"href\", \"#\"], [1, \"language-icon\"], [1, \"module-card\", 3, \"click\"], [1, \"card-content\"], [1, \"icon-container\"], [1, \"module-name\"], [1, \"caliber\"], [1, \"product-name\"]],\n      template: function ModuleComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelement(4, \"img\", 4);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7);\n          i0.ɵɵtemplate(8, ModuleComponent_div_8_Template, 10, 4, \"div\", 8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 9)(10, \"div\", 10)(11, \"a\", 11);\n          i0.ɵɵtext(12, \"Contact Admin\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(13, \" | \");\n          i0.ɵɵelementStart(14, \"a\", 11)(15, \"mat-icon\", 12);\n          i0.ɵɵtext(16, \"language\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(17, \" Choose Language\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngForOf\", ctx.modules);\n        }\n      },\n      dependencies: [i3.NgForOf, i4.MatIcon],\n      styles: [\".module-selection-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  height: 100vh; \\n\\n  width: 100%;\\n  font-family: \\\"Roboto\\\", sans-serif;\\n  overflow: hidden; \\n\\n}\\n\\n.left-panel[_ngcontent-%COMP%] {\\n  width: 60%;\\n  background-color: #002333;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  padding: 0;\\n  color: white;\\n  overflow: hidden;\\n  height: 100%; \\n\\n}\\n\\n.quari-logo[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 100%;\\n  height: 100%;\\n}\\n\\n.logo-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.logo-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  object-position: center;\\n}\\n\\n.right-panel[_ngcontent-%COMP%] {\\n  width: 40%;\\n  display: flex;\\n  flex-direction: column;\\n  padding: 2rem;\\n  background-color: #f8f9fa;\\n  height: 100%; \\n\\n}\\n\\n.scrollable-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto; \\n\\n  padding-right: 0.5rem; \\n\\n  margin-bottom: 1rem; \\n\\n}\\n\\n.module-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(2, 1fr);\\n  gap: 1.5rem;\\n  max-width: 800px;\\n  margin: 0 auto;\\n}\\n\\n.module-card[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\\n  padding: 1.5rem;\\n  cursor: pointer;\\n  transition: transform 0.2s, box-shadow 0.2s;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  text-align: center;\\n  aspect-ratio: 1/1; \\n\\n}\\n\\n.module-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\\n}\\n\\n.card-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 100%;\\n}\\n\\n.icon-container[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n\\n.icon-container[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 3.15rem; \\n\\n  height: 3.15rem;\\n  width: 3.15rem;\\n}\\n\\n.module-name[_ngcontent-%COMP%] {\\n  font-size: 1.5rem; \\n\\n  font-weight: 400;\\n}\\n\\n.caliber[_ngcontent-%COMP%] {\\n  color: #00a99d;\\n  font-weight: 500;\\n}\\n\\n.product-name[_ngcontent-%COMP%] {\\n  color: #333;\\n}\\n\\n.footer[_ngcontent-%COMP%] {\\n  flex-shrink: 0; \\n\\n  text-align: right;\\n  padding-top: 1rem;\\n  border-top: 1px solid #eee;\\n}\\n\\n.footer-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #666;\\n  text-decoration: none;\\n  font-size: 0.9rem;\\n  display: inline-flex;\\n  align-items: center;\\n}\\n\\n.footer-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  color: #00a99d;\\n}\\n\\n.language-icon[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  height: 1.2rem;\\n  width: 1.2rem;\\n  margin-right: 0.2rem;\\n}\\n\\n\\n\\n@media (min-width: 1600px) {\\n  .logo-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    object-position: center;\\n  }\\n}\\n@media (max-width: 992px) {\\n  .module-selection-container[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .left-panel[_ngcontent-%COMP%], .right-panel[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .left-panel[_ngcontent-%COMP%] {\\n    height: 40vh; \\n\\n    padding: 0;\\n  }\\n  .quari-logo[_ngcontent-%COMP%] {\\n    height: 100%;\\n  }\\n  .right-panel[_ngcontent-%COMP%] {\\n    height: 60vh; \\n\\n  }\\n  .logo-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 100%;\\n    height: 100%;\\n    object-fit: cover;\\n    object-position: center;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .module-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .module-card[_ngcontent-%COMP%] {\\n    aspect-ratio: 1/1; \\n\\n    max-width: 200px; \\n\\n    margin: 0 auto; \\n\\n  }\\n  .icon-container[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n    font-size: 2.5rem;\\n    height: 2.5rem;\\n    width: 2.5rem;\\n  }\\n  .module-name[_ngcontent-%COMP%] {\\n    font-size: 1.2rem;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .left-panel[_ngcontent-%COMP%] {\\n    padding: 0;\\n  }\\n  .right-panel[_ngcontent-%COMP%] {\\n    padding: 1.5rem 1rem;\\n    min-height: auto; \\n\\n  }\\n  .footer-links[_ngcontent-%COMP%] {\\n    display: flex;\\n    flex-direction: column;\\n    align-items: flex-end;\\n    gap: 0.5rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵlistener", "ModuleComponent_div_8_Template_div_click_0_listener", "restoredCtx", "ɵɵrestoreView", "_r3", "module_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "onModuleSelect", "name", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵstyleProp", "color", "ɵɵtextInterpolate", "icon", "replace", "ModuleComponent", "constructor", "router", "selectionService", "modules", "ngOnInit", "moduleName", "alert", "setSelectedModule", "navigate", "ɵɵdirectiveInject", "i1", "Router", "i2", "SelectionService", "selectors", "decls", "vars", "consts", "template", "ModuleComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵtemplate", "ModuleComponent_div_8_Template", "ɵɵproperty"], "sources": ["C:\\cal_wf\\Augment\\4-img\\apqr400bl\\src\\app\\module\\module.component.ts", "C:\\cal_wf\\Augment\\4-img\\apqr400bl\\src\\app\\module\\module.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { SelectionService } from '../shared/selection.service';\n\n@Component({\n  selector: 'app-module',\n  templateUrl: './module.component.html',\n  styleUrls: ['./module.component.scss']\n})\nexport class ModuleComponent implements OnInit {\n  modules = [\n    {\n      name: 'Caliber aPQR',\n      icon: 'analytics',\n      color: '#00a99d'\n    },\n    {\n      name: 'Caliber CPV',\n      icon: 'description',\n      color: '#00a99d'\n    },\n    {\n      name: 'Caliber Metrix',\n      icon: 'bubble_chart',\n      color: '#00a99d'\n    },\n    {\n      name: 'Caliber Pulse',\n      icon: 'trending_up',\n      color: '#00a99d'\n    }\n  ];\n\n  constructor(\n    private router: Router,\n    private selectionService: SelectionService\n  ) { }\n\n  ngOnInit(): void {\n  }\n\n  onModuleSelect(moduleName: string): void {\n    alert(`You selected ${moduleName}`);\n    this.selectionService.setSelectedModule(moduleName);\n    this.router.navigate(['/plantselection']);\n  }\n}\n", "<div class=\"module-selection-container\">\n  <div class=\"left-panel\">\n    <div class=\"quari-logo\">\n      <div class=\"logo-image\">\n        <img src=\"assets/quariDark.png\" alt=\"QUARI Logo\">\n      </div>\n    </div>\n  </div>\n  <div class=\"right-panel\">\n    <div class=\"scrollable-content\">\n      <div class=\"module-grid\">\n        <div class=\"module-card\" *ngFor=\"let module of modules\" (click)=\"onModuleSelect(module.name)\">\n          <div class=\"card-content\">\n            <div class=\"icon-container\">\n              <mat-icon [style.color]=\"module.color\">{{module.icon}}</mat-icon>\n            </div>\n            <div class=\"module-name\">\n              <span class=\"caliber\">Caliber</span> <span class=\"product-name\">{{module.name.replace('Caliber ', '')}}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n    <div class=\"footer\">\n      <div class=\"footer-links\">\n        <a href=\"#\">Contact Admin</a> |\n        <a href=\"#\"><mat-icon class=\"language-icon\">language</mat-icon> Choose Language</a>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;;;;;;;ICWQA,EAAA,CAAAC,cAAA,cAA8F;IAAtCD,EAAA,CAAAE,UAAA,mBAAAC,oDAAA;MAAA,MAAAC,WAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,SAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAG,cAAA,CAAAL,SAAA,CAAAM,IAAA,CAA2B;IAAA,EAAC;IAC3Fb,EAAA,CAAAC,cAAA,cAA0B;IAEiBD,EAAA,CAAAc,MAAA,GAAe;IAAAd,EAAA,CAAAe,YAAA,EAAW;IAEnEf,EAAA,CAAAC,cAAA,cAAyB;IACDD,EAAA,CAAAc,MAAA,cAAO;IAAAd,EAAA,CAAAe,YAAA,EAAO;IAACf,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAc,MAAA,GAAuC;IAAAd,EAAA,CAAAe,YAAA,EAAO;;;;IAHpGf,EAAA,CAAAgB,SAAA,GAA4B;IAA5BhB,EAAA,CAAAiB,WAAA,UAAAV,SAAA,CAAAW,KAAA,CAA4B;IAAClB,EAAA,CAAAgB,SAAA,GAAe;IAAfhB,EAAA,CAAAmB,iBAAA,CAAAZ,SAAA,CAAAa,IAAA,CAAe;IAGUpB,EAAA,CAAAgB,SAAA,GAAuC;IAAvChB,EAAA,CAAAmB,iBAAA,CAAAZ,SAAA,CAAAM,IAAA,CAAAQ,OAAA,iBAAuC;;;ADRrH,OAAM,MAAOC,eAAe;EAwB1BC,YACUC,MAAc,EACdC,gBAAkC;IADlC,KAAAD,MAAM,GAANA,MAAM;IACN,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAzB1B,KAAAC,OAAO,GAAG,CACR;MACEb,IAAI,EAAE,cAAc;MACpBO,IAAI,EAAE,WAAW;MACjBF,KAAK,EAAE;KACR,EACD;MACEL,IAAI,EAAE,aAAa;MACnBO,IAAI,EAAE,aAAa;MACnBF,KAAK,EAAE;KACR,EACD;MACEL,IAAI,EAAE,gBAAgB;MACtBO,IAAI,EAAE,cAAc;MACpBF,KAAK,EAAE;KACR,EACD;MACEL,IAAI,EAAE,eAAe;MACrBO,IAAI,EAAE,aAAa;MACnBF,KAAK,EAAE;KACR,CACF;EAKG;EAEJS,QAAQA,CAAA,GACR;EAEAf,cAAcA,CAACgB,UAAkB;IAC/BC,KAAK,CAAC,gBAAgBD,UAAU,EAAE,CAAC;IACnC,IAAI,CAACH,gBAAgB,CAACK,iBAAiB,CAACF,UAAU,CAAC;IACnD,IAAI,CAACJ,MAAM,CAACO,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;EAC3C;;;uBApCWT,eAAe,EAAAtB,EAAA,CAAAgC,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAlC,EAAA,CAAAgC,iBAAA,CAAAG,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAAfd,eAAe;MAAAe,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCT5B3C,EAAA,CAAAC,cAAA,aAAwC;UAIhCD,EAAA,CAAA6C,SAAA,aAAiD;UACnD7C,EAAA,CAAAe,YAAA,EAAM;UAGVf,EAAA,CAAAC,cAAA,aAAyB;UAGnBD,EAAA,CAAA8C,UAAA,IAAAC,8BAAA,kBASM;UACR/C,EAAA,CAAAe,YAAA,EAAM;UAERf,EAAA,CAAAC,cAAA,aAAoB;UAEJD,EAAA,CAAAc,MAAA,qBAAa;UAAAd,EAAA,CAAAe,YAAA,EAAI;UAACf,EAAA,CAAAc,MAAA,WAC9B;UAAAd,EAAA,CAAAC,cAAA,aAAY;UAAgCD,EAAA,CAAAc,MAAA,gBAAQ;UAAAd,EAAA,CAAAe,YAAA,EAAW;UAACf,EAAA,CAAAc,MAAA,wBAAe;UAAAd,EAAA,CAAAe,YAAA,EAAI;;;UAfvCf,EAAA,CAAAgB,SAAA,GAAU;UAAVhB,EAAA,CAAAgD,UAAA,YAAAJ,GAAA,CAAAlB,OAAA,CAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}