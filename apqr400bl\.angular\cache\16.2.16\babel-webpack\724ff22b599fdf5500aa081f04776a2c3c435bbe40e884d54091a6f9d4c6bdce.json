{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { PlantselectionComponent } from './plantselection.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: PlantselectionComponent\n}];\nexport class PlantselectionRoutingModule {\n  static {\n    this.ɵfac = function PlantselectionRoutingModule_Factory(t) {\n      return new (t || PlantselectionRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: PlantselectionRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(PlantselectionRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "PlantselectionComponent", "routes", "path", "component", "PlantselectionRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["C:\\cal_wf\\Augment\\4-img\\apqr400bl\\src\\app\\plantselection\\plantselection-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { PlantselectionComponent } from './plantselection.component';\n\nconst routes: Routes = [\n  {\n    path: '',\n    component: PlantselectionComponent\n  }\n];\n\n@NgModule({\n  imports: [RouterModule.forChild(routes)],\n  exports: [RouterModule]\n})\nexport class PlantselectionRoutingModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,uBAAuB,QAAQ,4BAA4B;;;AAEpE,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH;CACZ,CACF;AAMD,OAAM,MAAOI,2BAA2B;;;uBAA3BA,2BAA2B;IAAA;EAAA;;;YAA3BA;IAA2B;EAAA;;;gBAH5BL,YAAY,CAACM,QAAQ,CAACJ,MAAM,CAAC,EAC7BF,YAAY;IAAA;EAAA;;;2EAEXK,2BAA2B;IAAAE,OAAA,GAAAC,EAAA,CAAAR,YAAA;IAAAS,OAAA,GAF5BT,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}