.module-selection-container {
  display: flex;
  min-height: 100vh;
  width: 100%;
  font-family: '<PERSON>o', sans-serif;
}

.left-panel {
  width: 60%;
  background-color: #002333;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0;
  color: white;
  overflow: hidden;
}

.quari-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.logo-image {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.logo-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.right-panel {
  width: 40%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 2rem;
  background-color: #f8f9fa;
  min-height: 100vh; /* Ensure it takes at least the full viewport height */
}

.module-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  max-width: 800px;
  margin: 0 auto 2rem;
}

.module-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 1.5rem;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  aspect-ratio: 1 / 1; /* Make the card square */
}

.module-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.icon-container {
  margin-bottom: 1rem;
}

.icon-container mat-icon {
  font-size: 3.15rem; /* Increased by 50% from 2.1rem */
  height: 3.15rem;
  width: 3.15rem;
}

.module-name {
  font-size: 1.5rem; /* Increased by 50% from 1rem */
  font-weight: 400;
}

.caliber {
  color: #00a99d;
  font-weight: 500;
}

.product-name {
  color: #333;
}

.footer {
  margin-top: auto;
  text-align: right;
  padding-top: 1rem;
  border-top: 1px solid #eee;
}

.footer-links a {
  color: #666;
  text-decoration: none;
  font-size: 0.9rem;
  display: inline-flex;
  align-items: center;
}

.footer-links a:hover {
  color: #00a99d;
}

.language-icon {
  font-size: 1.2rem;
  height: 1.2rem;
  width: 1.2rem;
  margin-right: 0.2rem;
}

/* Responsive styles */
@media (min-width: 1600px) {
  .logo-image img {
    object-position: center;
  }
}

@media (max-width: 992px) {
  .module-selection-container {
    flex-direction: column;
  }

  .left-panel, .right-panel {
    width: 100%;
  }

  .left-panel {
    min-height: 40vh;
    padding: 0;
  }

  .quari-logo {
    min-height: 40vh;
  }

  .right-panel {
    min-height: auto;
  }

  .logo-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
  }
}

@media (max-width: 768px) {
  .module-grid {
    grid-template-columns: 1fr;
  }

  .module-card {
    aspect-ratio: 1 / 1; /* Maintain square aspect ratio */
    max-width: 200px; /* Limit width on smaller screens */
    margin: 0 auto; /* Center the card */
  }

  .icon-container mat-icon {
    font-size: 2.5rem;
    height: 2.5rem;
    width: 2.5rem;
  }

  .module-name {
    font-size: 1.2rem;
  }
}

@media (max-width: 576px) {
  .left-panel {
    padding: 0;
  }

  .right-panel {
    padding: 1.5rem 1rem;
    min-height: auto; /* Allow it to size to content on small screens */
  }

  .footer-links {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.5rem;
  }
}
