{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../shared/selection.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/material/icon\";\nfunction SidebarComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵlistener(\"click\", function SidebarComponent_div_11_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r3);\n      const item_r1 = restoredCtx.$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.navigateTo(item_r1));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 11);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r1 = ctx.$implicit;\n    i0.ɵɵclassProp(\"active\", item_r1.active)(\"bottom-menu\", item_r1.position === \"bottom\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r1.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r1.name);\n  }\n}\nexport class SidebarComponent {\n  constructor(router, selectionService) {\n    this.router = router;\n    this.selectionService = selectionService;\n    this.menuItems = [{\n      name: 'aPQR Flow',\n      icon: 'assignment',\n      route: '/layout/apqr-flow',\n      active: false\n    }, {\n      name: 'Reports',\n      icon: 'summarize',\n      route: '/layout/reports',\n      active: false\n    }, {\n      name: 'Analytics',\n      icon: 'insights',\n      route: '/layout/analytics',\n      active: false\n    }, {\n      name: 'Info',\n      icon: 'info',\n      route: '/layout',\n      active: false,\n      position: 'bottom'\n    }, {\n      name: 'Notifications',\n      icon: 'notifications',\n      route: '/layout',\n      active: false,\n      position: 'bottom'\n    }];\n    this.selectedModule = '';\n  }\n  ngOnInit() {\n    this.selectionService.selectedModule$.subscribe(module => {\n      this.selectedModule = module;\n    });\n    // Set active menu based on current route\n    const currentRoute = this.router.url;\n    this.menuItems.forEach(item => {\n      item.active = currentRoute.includes(item.route) && item.route !== '/layout';\n    });\n    // If we're on the dashboard, don't highlight any menu\n    if (currentRoute === '/layout') {\n      this.menuItems.forEach(item => {\n        item.active = false;\n      });\n    }\n  }\n  navigateTo(item) {\n    this.menuItems.forEach(menuItem => {\n      menuItem.active = menuItem === item;\n    });\n    this.router.navigate([item.route]);\n  }\n  static {\n    this.ɵfac = function SidebarComponent_Factory(t) {\n      return new (t || SidebarComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.SelectionService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SidebarComponent,\n      selectors: [[\"app-sidebar\"]],\n      decls: 12,\n      vars: 1,\n      consts: [[1, \"sidebar\"], [1, \"logo-container\"], [1, \"logo\"], [\"src\", \"assets/caliber-logo.svg\", \"alt\", \"Caliber Logo\"], [1, \"module-name\"], [1, \"caliber\"], [1, \"product-name\"], [1, \"menu-container\"], [1, \"main-menu\"], [\"class\", \"menu-item\", 3, \"active\", \"bottom-menu\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"menu-item\", 3, \"click\"], [1, \"menu-text\"]],\n      template: function SidebarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelement(3, \"img\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"span\", 5);\n          i0.ɵɵtext(6, \"Caliber\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"span\", 6);\n          i0.ɵɵtext(8, \"aPQR\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(9, \"div\", 7)(10, \"div\", 8);\n          i0.ɵɵtemplate(11, SidebarComponent_div_11_Template, 5, 6, \"div\", 9);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngForOf\", ctx.menuItems);\n        }\n      },\n      dependencies: [i3.NgForOf, i4.MatIcon],\n      styles: [\".sidebar[_ngcontent-%COMP%] {\\n  width: 110px;\\n  height: 100%;\\n  background-color: #002333;\\n  color: white;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  padding: 1rem 0;\\n}\\n\\n.logo-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  margin-bottom: 2rem;\\n  text-align: center;\\n}\\n\\n.logo[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  margin-bottom: 0.5rem;\\n}\\n\\n.logo[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n}\\n\\n.module-name[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n}\\n\\n.caliber[_ngcontent-%COMP%] {\\n  color: #00a99d;\\n  font-weight: 500;\\n}\\n\\n.product-name[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n}\\n\\n.menu-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  width: 100%;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.main-menu[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  width: 100%;\\n  flex: 1;\\n  position: relative;\\n}\\n\\n.menu-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  width: 100%;\\n  padding: 1rem 0;\\n  cursor: pointer;\\n  transition: background-color 0.2s;\\n  position: relative;\\n}\\n\\n.menu-item[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 255, 255, 0.1);\\n}\\n\\n.menu-item.active[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.15);\\n}\\n\\n.menu-item.active[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  left: 0;\\n  top: 0;\\n  height: 100%;\\n  width: 4px;\\n  background-color: #00a99d;\\n}\\n\\n.menu-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  width: 24px;\\n  height: 24px;\\n  margin-bottom: 0.5rem;\\n}\\n\\n.menu-text[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  text-align: center;\\n}\\n\\n.bottom-menu[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n}\\n\\n.bottom-menu[_ngcontent-%COMP%]:nth-last-child(1) {\\n  bottom: 0;\\n}\\n\\n.bottom-menu[_ngcontent-%COMP%]:nth-last-child(2) {\\n  bottom: 70px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵlistener", "SidebarComponent_div_11_Template_div_click_0_listener", "restoredCtx", "ɵɵrestoreView", "_r3", "item_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "navigateTo", "ɵɵtext", "ɵɵelementEnd", "ɵɵclassProp", "active", "position", "ɵɵadvance", "ɵɵtextInterpolate", "icon", "name", "SidebarComponent", "constructor", "router", "selectionService", "menuItems", "route", "selectedModule", "ngOnInit", "selectedModule$", "subscribe", "module", "currentRoute", "url", "for<PERSON>ach", "item", "includes", "menuItem", "navigate", "ɵɵdirectiveInject", "i1", "Router", "i2", "SelectionService", "selectors", "decls", "vars", "consts", "template", "SidebarComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵtemplate", "SidebarComponent_div_11_Template", "ɵɵproperty"], "sources": ["C:\\cal_wf\\Augment\\4-img\\apqr400bl\\src\\app\\layout\\components\\sidebar\\sidebar.component.ts", "C:\\cal_wf\\Augment\\4-img\\apqr400bl\\src\\app\\layout\\components\\sidebar\\sidebar.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { SelectionService } from '../../../shared/selection.service';\n\n@Component({\n  selector: 'app-sidebar',\n  templateUrl: './sidebar.component.html',\n  styleUrls: ['./sidebar.component.scss']\n})\nexport class SidebarComponent implements OnInit {\n  menuItems = [\n    { \n      name: 'aPQR Flow', \n      icon: 'assignment', \n      route: '/layout/apqr-flow',\n      active: false\n    },\n    { \n      name: 'Reports', \n      icon: 'summarize', \n      route: '/layout/reports',\n      active: false\n    },\n    { \n      name: 'Analytics', \n      icon: 'insights', \n      route: '/layout/analytics',\n      active: false\n    },\n    { \n      name: 'Info', \n      icon: 'info', \n      route: '/layout',\n      active: false,\n      position: 'bottom'\n    },\n    { \n      name: 'Notifications', \n      icon: 'notifications', \n      route: '/layout',\n      active: false,\n      position: 'bottom'\n    }\n  ];\n\n  selectedModule = '';\n\n  constructor(\n    private router: Router,\n    private selectionService: SelectionService\n  ) { }\n\n  ngOnInit(): void {\n    this.selectionService.selectedModule$.subscribe(module => {\n      this.selectedModule = module;\n    });\n    \n    // Set active menu based on current route\n    const currentRoute = this.router.url;\n    this.menuItems.forEach(item => {\n      item.active = currentRoute.includes(item.route) && item.route !== '/layout';\n    });\n    \n    // If we're on the dashboard, don't highlight any menu\n    if (currentRoute === '/layout') {\n      this.menuItems.forEach(item => {\n        item.active = false;\n      });\n    }\n  }\n\n  navigateTo(item: any): void {\n    this.menuItems.forEach(menuItem => {\n      menuItem.active = menuItem === item;\n    });\n    this.router.navigate([item.route]);\n  }\n}\n", "<div class=\"sidebar\">\n  <div class=\"logo-container\">\n    <div class=\"logo\">\n      <img src=\"assets/caliber-logo.svg\" alt=\"Caliber Logo\">\n    </div>\n    <div class=\"module-name\">\n      <span class=\"caliber\">Caliber</span> <span class=\"product-name\">aPQR</span>\n    </div>\n  </div>\n  \n  <div class=\"menu-container\">\n    <div class=\"main-menu\">\n      <div \n        *ngFor=\"let item of menuItems\" \n        class=\"menu-item\" \n        [class.active]=\"item.active\"\n        [class.bottom-menu]=\"item.position === 'bottom'\"\n        (click)=\"navigateTo(item)\">\n        <mat-icon>{{item.icon}}</mat-icon>\n        <span class=\"menu-text\">{{item.name}}</span>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;;;;;;;ICYMA,EAAA,CAAAC,cAAA,cAK6B;IAA3BD,EAAA,CAAAE,UAAA,mBAAAC,sDAAA;MAAA,MAAAC,WAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,OAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAG,UAAA,CAAAL,OAAA,CAAgB;IAAA,EAAC;IAC1BP,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAa,MAAA,GAAa;IAAAb,EAAA,CAAAc,YAAA,EAAW;IAClCd,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAa,MAAA,GAAa;IAAAb,EAAA,CAAAc,YAAA,EAAO;;;;IAJ5Cd,EAAA,CAAAe,WAAA,WAAAR,OAAA,CAAAS,MAAA,CAA4B,gBAAAT,OAAA,CAAAU,QAAA;IAGlBjB,EAAA,CAAAkB,SAAA,GAAa;IAAblB,EAAA,CAAAmB,iBAAA,CAAAZ,OAAA,CAAAa,IAAA,CAAa;IACCpB,EAAA,CAAAkB,SAAA,GAAa;IAAblB,EAAA,CAAAmB,iBAAA,CAAAZ,OAAA,CAAAc,IAAA,CAAa;;;ADV7C,OAAM,MAAOC,gBAAgB;EAsC3BC,YACUC,MAAc,EACdC,gBAAkC;IADlC,KAAAD,MAAM,GAANA,MAAM;IACN,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAvC1B,KAAAC,SAAS,GAAG,CACV;MACEL,IAAI,EAAE,WAAW;MACjBD,IAAI,EAAE,YAAY;MAClBO,KAAK,EAAE,mBAAmB;MAC1BX,MAAM,EAAE;KACT,EACD;MACEK,IAAI,EAAE,SAAS;MACfD,IAAI,EAAE,WAAW;MACjBO,KAAK,EAAE,iBAAiB;MACxBX,MAAM,EAAE;KACT,EACD;MACEK,IAAI,EAAE,WAAW;MACjBD,IAAI,EAAE,UAAU;MAChBO,KAAK,EAAE,mBAAmB;MAC1BX,MAAM,EAAE;KACT,EACD;MACEK,IAAI,EAAE,MAAM;MACZD,IAAI,EAAE,MAAM;MACZO,KAAK,EAAE,SAAS;MAChBX,MAAM,EAAE,KAAK;MACbC,QAAQ,EAAE;KACX,EACD;MACEI,IAAI,EAAE,eAAe;MACrBD,IAAI,EAAE,eAAe;MACrBO,KAAK,EAAE,SAAS;MAChBX,MAAM,EAAE,KAAK;MACbC,QAAQ,EAAE;KACX,CACF;IAED,KAAAW,cAAc,GAAG,EAAE;EAKf;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACJ,gBAAgB,CAACK,eAAe,CAACC,SAAS,CAACC,MAAM,IAAG;MACvD,IAAI,CAACJ,cAAc,GAAGI,MAAM;IAC9B,CAAC,CAAC;IAEF;IACA,MAAMC,YAAY,GAAG,IAAI,CAACT,MAAM,CAACU,GAAG;IACpC,IAAI,CAACR,SAAS,CAACS,OAAO,CAACC,IAAI,IAAG;MAC5BA,IAAI,CAACpB,MAAM,GAAGiB,YAAY,CAACI,QAAQ,CAACD,IAAI,CAACT,KAAK,CAAC,IAAIS,IAAI,CAACT,KAAK,KAAK,SAAS;IAC7E,CAAC,CAAC;IAEF;IACA,IAAIM,YAAY,KAAK,SAAS,EAAE;MAC9B,IAAI,CAACP,SAAS,CAACS,OAAO,CAACC,IAAI,IAAG;QAC5BA,IAAI,CAACpB,MAAM,GAAG,KAAK;MACrB,CAAC,CAAC;;EAEN;EAEAJ,UAAUA,CAACwB,IAAS;IAClB,IAAI,CAACV,SAAS,CAACS,OAAO,CAACG,QAAQ,IAAG;MAChCA,QAAQ,CAACtB,MAAM,GAAGsB,QAAQ,KAAKF,IAAI;IACrC,CAAC,CAAC;IACF,IAAI,CAACZ,MAAM,CAACe,QAAQ,CAAC,CAACH,IAAI,CAACT,KAAK,CAAC,CAAC;EACpC;;;uBAnEWL,gBAAgB,EAAAtB,EAAA,CAAAwC,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAA1C,EAAA,CAAAwC,iBAAA,CAAAG,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAAhBtB,gBAAgB;MAAAuB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCT7BnD,EAAA,CAAAC,cAAA,aAAqB;UAGfD,EAAA,CAAAqD,SAAA,aAAsD;UACxDrD,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAC,cAAA,aAAyB;UACDD,EAAA,CAAAa,MAAA,cAAO;UAAAb,EAAA,CAAAc,YAAA,EAAO;UAACd,EAAA,CAAAC,cAAA,cAA2B;UAAAD,EAAA,CAAAa,MAAA,WAAI;UAAAb,EAAA,CAAAc,YAAA,EAAO;UAI/Ed,EAAA,CAAAC,cAAA,aAA4B;UAExBD,EAAA,CAAAsD,UAAA,KAAAC,gCAAA,iBAQM;UACRvD,EAAA,CAAAc,YAAA,EAAM;;;UARed,EAAA,CAAAkB,SAAA,IAAY;UAAZlB,EAAA,CAAAwD,UAAA,YAAAJ,GAAA,CAAA1B,SAAA,CAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}