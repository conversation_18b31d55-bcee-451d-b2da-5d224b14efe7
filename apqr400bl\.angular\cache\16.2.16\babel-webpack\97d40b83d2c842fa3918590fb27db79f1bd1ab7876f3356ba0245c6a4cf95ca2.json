{"ast": null, "code": "import { isFunction } from \"./isFunction\";\nexport function isPromise(value) {\n  return isFunction(value === null || value === void 0 ? void 0 : value.then);\n}", "map": {"version": 3, "names": ["isFunction", "isPromise", "value", "then"], "sources": ["C:/cal_wf/Augment/3-img/apqr400bl/node_modules/rxjs/dist/esm/internal/util/isPromise.js"], "sourcesContent": ["import { isFunction } from \"./isFunction\";\nexport function isPromise(value) {\n    return isFunction(value === null || value === void 0 ? void 0 : value.then);\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,cAAc;AACzC,OAAO,SAASC,SAASA,CAACC,KAAK,EAAE;EAC7B,OAAOF,UAAU,CAACE,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACC,IAAI,CAAC;AAC/E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}