import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class SelectionService {
  private selectedModuleSource = new BehaviorSubject<string>('Caliber aPQR');
  private selectedPlantSource = new BehaviorSubject<string>('CaliberPlant-1');

  selectedModule$ = this.selectedModuleSource.asObservable();
  selectedPlant$ = this.selectedPlantSource.asObservable();

  constructor() { }

  setSelectedModule(module: string): void {
    this.selectedModuleSource.next(module);
  }

  setSelectedPlant(plant: string): void {
    this.selectedPlantSource.next(plant);
  }
}
