import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LayoutRoutingModule } from './layout-routing.module';
import { LayoutComponent } from './layout.component';
import { SidebarComponent } from './components/sidebar/sidebar.component';
import { TopnavComponent } from './components/topnav/topnav.component';
import { DashboardComponent } from './components/dashboard/dashboard.component';
import { ApqrFlowComponent } from './components/apqr-flow/apqr-flow.component';
import { ReportsComponent } from './components/reports/reports.component';
import { AnalyticsComponent } from './components/analytics/analytics.component';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatBadgeModule } from '@angular/material/badge';

@NgModule({
  declarations: [
    LayoutComponent,
    SidebarComponent,
    TopnavComponent,
    DashboardComponent,
    Apqr<PERSON>lowComponent,
    ReportsComponent,
    AnalyticsComponent
  ],
  imports: [
    CommonModule,
    LayoutRoutingModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule,
    MatBadgeModule
  ]
})
export class LayoutModule { }
