{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/material/icon\";\nfunction PlantselectionComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵlistener(\"click\", function PlantselectionComponent_div_27_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r3);\n      const plant_r1 = restoredCtx.$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onPlantSelect(plant_r1.name));\n    });\n    i0.ɵɵelementStart(1, \"div\", 21);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"chevron_right\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const plant_r1 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(plant_r1.name);\n  }\n}\nexport class PlantselectionComponent {\n  constructor() {\n    this.plants = [{\n      name: 'CaliberPlant-1'\n    }, {\n      name: 'CaliberPlant-2'\n    }, {\n      name: 'CaliberPlant-3'\n    }, {\n      name: 'CaliberPlant-4'\n    }, {\n      name: 'CaliberPlant-5'\n    }];\n  }\n  ngOnInit() {}\n  onPlantSelect(plantName) {\n    alert(`You selected ${plantName}`);\n  }\n  static {\n    this.ɵfac = function PlantselectionComponent_Factory(t) {\n      return new (t || PlantselectionComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PlantselectionComponent,\n      selectors: [[\"app-plantselection\"]],\n      decls: 37,\n      vars: 1,\n      consts: [[1, \"plantselection-container\"], [1, \"left-panel\"], [1, \"quari-logo\"], [1, \"logo-image\"], [\"src\", \"assets/quariDark.png\", \"alt\", \"QUARI Logo\"], [1, \"logo-text\"], [1, \"by-caliber\"], [1, \"caliber\"], [1, \"right-panel\"], [1, \"user-info\"], [1, \"user-avatar\"], [\"src\", \"assets/user-avatar.png\", \"alt\", \"User Avatar\"], [1, \"user-greeting\"], [1, \"plant-selection-content\"], [1, \"plant-list\"], [\"class\", \"plant-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"footer\"], [1, \"footer-links\"], [\"href\", \"#\"], [1, \"language-icon\"], [1, \"plant-item\", 3, \"click\"], [1, \"plant-name\"]],\n      template: function PlantselectionComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelement(4, \"img\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 5)(6, \"h1\");\n          i0.ɵɵtext(7, \"QUARI\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"p\");\n          i0.ɵɵtext(9, \"Platform for Quality and Actionable Regulatory Insights.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 6)(11, \"span\");\n          i0.ɵɵtext(12, \"by\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"span\", 7);\n          i0.ɵɵtext(14, \"Caliber\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(15, \"div\", 8)(16, \"div\", 9)(17, \"div\", 10);\n          i0.ɵɵelement(18, \"img\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"div\", 12);\n          i0.ɵɵtext(20, \" Hello, Sumathi \");\n          i0.ɵɵelementStart(21, \"mat-icon\");\n          i0.ɵɵtext(22, \"arrow_drop_down\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(23, \"div\", 13)(24, \"h2\");\n          i0.ɵɵtext(25, \"Select a plant\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"div\", 14);\n          i0.ɵɵtemplate(27, PlantselectionComponent_div_27_Template, 5, 1, \"div\", 15);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"div\", 16)(29, \"div\", 17)(30, \"a\", 18);\n          i0.ɵɵtext(31, \"Contact Admin\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(32, \" | \");\n          i0.ɵɵelementStart(33, \"a\", 18)(34, \"mat-icon\", 19);\n          i0.ɵɵtext(35, \"language\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(36, \" Choose Language\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(27);\n          i0.ɵɵproperty(\"ngForOf\", ctx.plants);\n        }\n      },\n      dependencies: [i1.NgForOf, i2.MatIcon],\n      styles: [\".plantselection-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  min-height: 100vh;\\n  width: 100%;\\n  font-family: \\\"Roboto\\\", sans-serif;\\n}\\n\\n.left-panel[_ngcontent-%COMP%] {\\n  width: 70%;\\n  background-color: #002333;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  padding: 2rem;\\n  color: white;\\n}\\n\\n.quari-logo[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  text-align: center;\\n  width: 100%;\\n}\\n\\n.logo-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: flex;\\n  justify-content: center;\\n}\\n\\n.logo-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 400px;\\n  margin-bottom: 1rem;\\n}\\n\\n.logo-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 4rem;\\n  font-weight: 300;\\n  margin: 0;\\n  letter-spacing: 2px;\\n}\\n\\n.logo-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  margin-top: 0.5rem;\\n  max-width: 400px;\\n}\\n\\n.by-caliber[_ngcontent-%COMP%] {\\n  margin-top: 3rem;\\n  font-size: 1.2rem;\\n}\\n\\n.caliber[_ngcontent-%COMP%] {\\n  color: #00a99d;\\n  font-weight: 500;\\n}\\n\\n.right-panel[_ngcontent-%COMP%] {\\n  width: 30%;\\n  display: flex;\\n  flex-direction: column;\\n  padding: 1rem 2rem;\\n  background-color: #f8f9fa;\\n}\\n\\n.user-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  align-items: center;\\n  margin-bottom: 2rem;\\n  padding: 1rem 0;\\n}\\n\\n.user-avatar[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  overflow: hidden;\\n  margin-right: 0.5rem;\\n}\\n\\n.user-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n\\n.user-greeting[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  color: #333;\\n  font-size: 0.9rem;\\n}\\n\\n.plant-selection-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\nh2[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 500;\\n  color: #333;\\n  margin-bottom: 2rem;\\n}\\n\\n.plant-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.5rem;\\n}\\n\\n.plant-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 1rem 0;\\n  border-bottom: 1px solid #e0e0e0;\\n  cursor: pointer;\\n  transition: background-color 0.2s;\\n}\\n\\n.plant-item[_ngcontent-%COMP%]:hover {\\n  background-color: #f0f0f0;\\n}\\n\\n.plant-name[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: #333;\\n}\\n\\n.footer[_ngcontent-%COMP%] {\\n  margin-top: auto;\\n  text-align: right;\\n  padding-top: 1rem;\\n  border-top: 1px solid #eee;\\n}\\n\\n.footer-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #666;\\n  text-decoration: none;\\n  font-size: 0.9rem;\\n  display: inline-flex;\\n  align-items: center;\\n}\\n\\n.footer-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  color: #00a99d;\\n}\\n\\n.language-icon[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  height: 1.2rem;\\n  width: 1.2rem;\\n  margin-right: 0.2rem;\\n}\\n\\n\\n\\n@media (max-width: 1200px) {\\n  .left-panel[_ngcontent-%COMP%] {\\n    width: 60%;\\n  }\\n  .right-panel[_ngcontent-%COMP%] {\\n    width: 40%;\\n  }\\n}\\n@media (max-width: 992px) {\\n  .plantselection-container[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .left-panel[_ngcontent-%COMP%], .right-panel[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .left-panel[_ngcontent-%COMP%] {\\n    padding: 3rem 2rem;\\n  }\\n  .logo-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    max-width: 300px;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .left-panel[_ngcontent-%COMP%] {\\n    padding: 2rem 1rem;\\n  }\\n  .right-panel[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .logo-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 3rem;\\n  }\\n  .footer-links[_ngcontent-%COMP%] {\\n    display: flex;\\n    flex-direction: column;\\n    align-items: flex-end;\\n    gap: 0.5rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵlistener", "PlantselectionComponent_div_27_Template_div_click_0_listener", "restoredCtx", "ɵɵrestoreView", "_r3", "plant_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "onPlantSelect", "name", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "PlantselectionComponent", "constructor", "plants", "ngOnInit", "plantName", "alert", "selectors", "decls", "vars", "consts", "template", "PlantselectionComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵtemplate", "PlantselectionComponent_div_27_Template", "ɵɵproperty"], "sources": ["C:\\cal_wf\\Augment\\4-img\\apqr400bl\\src\\app\\plantselection\\plantselection.component.ts", "C:\\cal_wf\\Augment\\4-img\\apqr400bl\\src\\app\\plantselection\\plantselection.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\n\n@Component({\n  selector: 'app-plantselection',\n  templateUrl: './plantselection.component.html',\n  styleUrls: ['./plantselection.component.scss']\n})\nexport class PlantselectionComponent implements OnInit {\n  plants = [\n    { name: 'CaliberPlant-1' },\n    { name: 'CaliberPlant-2' },\n    { name: 'CaliberPlant-3' },\n    { name: 'CaliberPlant-4' },\n    { name: 'CaliberPlant-5' }\n  ];\n\n  constructor() { }\n\n  ngOnInit(): void {\n  }\n\n  onPlantSelect(plantName: string): void {\n    alert(`You selected ${plantName}`);\n  }\n}\n", "<div class=\"plantselection-container\">\n  <div class=\"left-panel\">\n    <div class=\"quari-logo\">\n      <div class=\"logo-image\">\n        <img src=\"assets/quariDark.png\" alt=\"QUARI Logo\">\n      </div>\n      <div class=\"logo-text\">\n        <h1>QUARI</h1>\n        <p>Platform for Quality and Actionable Regulatory Insights.</p>\n      </div>\n      <div class=\"by-caliber\">\n        <span>by</span> <span class=\"caliber\">Caliber</span>\n      </div>\n    </div>\n  </div>\n  <div class=\"right-panel\">\n    <div class=\"user-info\">\n      <div class=\"user-avatar\">\n        <img src=\"assets/user-avatar.png\" alt=\"User Avatar\">\n      </div>\n      <div class=\"user-greeting\">\n        Hello, Sumathi <mat-icon>arrow_drop_down</mat-icon>\n      </div>\n    </div>\n    \n    <div class=\"plant-selection-content\">\n      <h2>Select a plant</h2>\n      \n      <div class=\"plant-list\">\n        <div class=\"plant-item\" *ngFor=\"let plant of plants\" (click)=\"onPlantSelect(plant.name)\">\n          <div class=\"plant-name\">{{ plant.name }}</div>\n          <mat-icon>chevron_right</mat-icon>\n        </div>\n      </div>\n    </div>\n    \n    <div class=\"footer\">\n      <div class=\"footer-links\">\n        <a href=\"#\">Contact Admin</a> | \n        <a href=\"#\"><mat-icon class=\"language-icon\">language</mat-icon> Choose Language</a>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;;;;;IC6BQA,EAAA,CAAAC,cAAA,cAAyF;IAApCD,EAAA,CAAAE,UAAA,mBAAAC,6DAAA;MAAA,MAAAC,WAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,QAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAG,aAAA,CAAAL,QAAA,CAAAM,IAAA,CAAyB;IAAA,EAAC;IACtFb,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAc,MAAA,GAAgB;IAAAd,EAAA,CAAAe,YAAA,EAAM;IAC9Cf,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAc,MAAA,oBAAa;IAAAd,EAAA,CAAAe,YAAA,EAAW;;;;IADVf,EAAA,CAAAgB,SAAA,GAAgB;IAAhBhB,EAAA,CAAAiB,iBAAA,CAAAV,QAAA,CAAAM,IAAA,CAAgB;;;ADvBlD,OAAM,MAAOK,uBAAuB;EASlCC,YAAA;IARA,KAAAC,MAAM,GAAG,CACP;MAAEP,IAAI,EAAE;IAAgB,CAAE,EAC1B;MAAEA,IAAI,EAAE;IAAgB,CAAE,EAC1B;MAAEA,IAAI,EAAE;IAAgB,CAAE,EAC1B;MAAEA,IAAI,EAAE;IAAgB,CAAE,EAC1B;MAAEA,IAAI,EAAE;IAAgB,CAAE,CAC3B;EAEe;EAEhBQ,QAAQA,CAAA,GACR;EAEAT,aAAaA,CAACU,SAAiB;IAC7BC,KAAK,CAAC,gBAAgBD,SAAS,EAAE,CAAC;EACpC;;;uBAhBWJ,uBAAuB;IAAA;EAAA;;;YAAvBA,uBAAuB;MAAAM,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPpC9B,EAAA,CAAAC,cAAA,aAAsC;UAI9BD,EAAA,CAAAgC,SAAA,aAAiD;UACnDhC,EAAA,CAAAe,YAAA,EAAM;UACNf,EAAA,CAAAC,cAAA,aAAuB;UACjBD,EAAA,CAAAc,MAAA,YAAK;UAAAd,EAAA,CAAAe,YAAA,EAAK;UACdf,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAc,MAAA,+DAAwD;UAAAd,EAAA,CAAAe,YAAA,EAAI;UAEjEf,EAAA,CAAAC,cAAA,cAAwB;UAChBD,EAAA,CAAAc,MAAA,UAAE;UAAAd,EAAA,CAAAe,YAAA,EAAO;UAACf,EAAA,CAAAC,cAAA,eAAsB;UAAAD,EAAA,CAAAc,MAAA,eAAO;UAAAd,EAAA,CAAAe,YAAA,EAAO;UAI1Df,EAAA,CAAAC,cAAA,cAAyB;UAGnBD,EAAA,CAAAgC,SAAA,eAAoD;UACtDhC,EAAA,CAAAe,YAAA,EAAM;UACNf,EAAA,CAAAC,cAAA,eAA2B;UACzBD,EAAA,CAAAc,MAAA,wBAAe;UAAAd,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAc,MAAA,uBAAe;UAAAd,EAAA,CAAAe,YAAA,EAAW;UAIvDf,EAAA,CAAAC,cAAA,eAAqC;UAC/BD,EAAA,CAAAc,MAAA,sBAAc;UAAAd,EAAA,CAAAe,YAAA,EAAK;UAEvBf,EAAA,CAAAC,cAAA,eAAwB;UACtBD,EAAA,CAAAiC,UAAA,KAAAC,uCAAA,kBAGM;UACRlC,EAAA,CAAAe,YAAA,EAAM;UAGRf,EAAA,CAAAC,cAAA,eAAoB;UAEJD,EAAA,CAAAc,MAAA,qBAAa;UAAAd,EAAA,CAAAe,YAAA,EAAI;UAACf,EAAA,CAAAc,MAAA,WAC9B;UAAAd,EAAA,CAAAC,cAAA,aAAY;UAAgCD,EAAA,CAAAc,MAAA,gBAAQ;UAAAd,EAAA,CAAAe,YAAA,EAAW;UAACf,EAAA,CAAAc,MAAA,wBAAe;UAAAd,EAAA,CAAAe,YAAA,EAAI;;;UAVzCf,EAAA,CAAAgB,SAAA,IAAS;UAAThB,EAAA,CAAAmC,UAAA,YAAAJ,GAAA,CAAAX,MAAA,CAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}