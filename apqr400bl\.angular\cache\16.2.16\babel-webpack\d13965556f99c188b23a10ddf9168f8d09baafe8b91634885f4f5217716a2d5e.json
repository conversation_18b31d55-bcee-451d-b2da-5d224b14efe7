{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../shared/selection.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/material/icon\";\nfunction ModuleComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵlistener(\"click\", function ModuleComponent_div_7_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r3);\n      const module_r1 = restoredCtx.$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onModuleSelect(module_r1.name));\n    });\n    i0.ɵɵelementStart(1, \"div\", 13)(2, \"div\", 14)(3, \"mat-icon\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 15)(6, \"span\", 16);\n    i0.ɵɵtext(7, \"Caliber\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 17);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const module_r1 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"color\", module_r1.color);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(module_r1.icon);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(module_r1.name.replace(\"Caliber \", \"\"));\n  }\n}\nexport class ModuleComponent {\n  constructor(router, selectionService) {\n    this.router = router;\n    this.selectionService = selectionService;\n    this.modules = [{\n      name: 'Caliber aPQR',\n      icon: 'analytics',\n      color: '#00a99d'\n    }, {\n      name: 'Caliber CPV',\n      icon: 'description',\n      color: '#00a99d'\n    }, {\n      name: 'Caliber Metrix',\n      icon: 'bubble_chart',\n      color: '#00a99d'\n    }, {\n      name: 'Caliber Pulse',\n      icon: 'trending_up',\n      color: '#00a99d'\n    }];\n  }\n  ngOnInit() {}\n  onModuleSelect(moduleName) {\n    alert(`You selected ${moduleName}`);\n    this.selectionService.setSelectedModule(moduleName);\n    this.router.navigate(['/plantselection']);\n  }\n  static {\n    this.ɵfac = function ModuleComponent_Factory(t) {\n      return new (t || ModuleComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.SelectionService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ModuleComponent,\n      selectors: [[\"app-module\"]],\n      decls: 17,\n      vars: 1,\n      consts: [[1, \"module-selection-container\"], [1, \"left-panel\"], [1, \"quari-logo\"], [1, \"logo-image\"], [\"src\", \"assets/quariDark.png\", \"alt\", \"QUARI Logo\"], [1, \"right-panel\"], [1, \"module-grid\"], [\"class\", \"module-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"footer\"], [1, \"footer-links\"], [\"href\", \"#\"], [1, \"language-icon\"], [1, \"module-card\", 3, \"click\"], [1, \"card-content\"], [1, \"icon-container\"], [1, \"module-name\"], [1, \"caliber\"], [1, \"product-name\"]],\n      template: function ModuleComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelement(4, \"img\", 4);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(5, \"div\", 5)(6, \"div\", 6);\n          i0.ɵɵtemplate(7, ModuleComponent_div_7_Template, 10, 4, \"div\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 8)(9, \"div\", 9)(10, \"a\", 10);\n          i0.ɵɵtext(11, \"Contact Admin\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(12, \" | \");\n          i0.ɵɵelementStart(13, \"a\", 10)(14, \"mat-icon\", 11);\n          i0.ɵɵtext(15, \"language\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(16, \" Choose Language\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngForOf\", ctx.modules);\n        }\n      },\n      dependencies: [i3.NgForOf, i4.MatIcon],\n      styles: [\".module-selection-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  min-height: 100vh;\\n  width: 100%;\\n  font-family: \\\"Roboto\\\", sans-serif;\\n}\\n\\n.left-panel[_ngcontent-%COMP%] {\\n  width: 60%;\\n  background-color: #002333;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  padding: 0;\\n  color: white;\\n  overflow: hidden;\\n}\\n\\n.quari-logo[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 100%;\\n  height: 100%;\\n}\\n\\n.logo-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.logo-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  object-position: center;\\n}\\n\\n.right-panel[_ngcontent-%COMP%] {\\n  width: 40%;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  padding: 2rem;\\n  background-color: #f8f9fa;\\n}\\n\\n.module-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(2, 1fr);\\n  gap: 1.5rem;\\n  max-width: 800px;\\n  margin: 0 auto 2rem;\\n}\\n\\n.module-card[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\\n  padding: 1.5rem;\\n  cursor: pointer;\\n  transition: transform 0.2s, box-shadow 0.2s;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  text-align: center;\\n  aspect-ratio: 1/1; \\n\\n  max-width: 100%; \\n\\n}\\n\\n.module-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\\n}\\n\\n.card-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 100%;\\n}\\n\\n.icon-container[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n\\n.icon-container[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 3.15rem; \\n\\n  height: 3.15rem;\\n  width: 3.15rem;\\n}\\n\\n.module-name[_ngcontent-%COMP%] {\\n  font-size: 1.5rem; \\n\\n  font-weight: 400;\\n}\\n\\n.caliber[_ngcontent-%COMP%] {\\n  color: #00a99d;\\n  font-weight: 500;\\n}\\n\\n.product-name[_ngcontent-%COMP%] {\\n  color: #333;\\n}\\n\\n.footer[_ngcontent-%COMP%] {\\n  margin-top: 1rem;\\n  text-align: right;\\n  padding-top: 1rem;\\n  border-top: 1px solid #eee;\\n  flex-shrink: 0; \\n\\n}\\n\\n.footer-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #666;\\n  text-decoration: none;\\n  font-size: 0.9rem;\\n  display: inline-flex;\\n  align-items: center;\\n}\\n\\n.footer-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  color: #00a99d;\\n}\\n\\n.language-icon[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  height: 1.2rem;\\n  width: 1.2rem;\\n  margin-right: 0.2rem;\\n}\\n\\n\\n\\n@media (min-width: 1600px) {\\n  .logo-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    object-position: center;\\n  }\\n}\\n@media (max-width: 992px) {\\n  .module-selection-container[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .left-panel[_ngcontent-%COMP%], .right-panel[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .left-panel[_ngcontent-%COMP%] {\\n    height: 40vh; \\n\\n    padding: 0;\\n  }\\n  .logo-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 100%;\\n    height: 100%;\\n    object-fit: cover;\\n    object-position: center;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .module-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .module-card[_ngcontent-%COMP%] {\\n    aspect-ratio: 1/1; \\n\\n  }\\n  \\n\\n}\\n@media (max-width: 576px) {\\n  .left-panel[_ngcontent-%COMP%] {\\n    padding: 0;\\n  }\\n  .right-panel[_ngcontent-%COMP%] {\\n    padding: 1.5rem 1rem;\\n  }\\n  .footer-links[_ngcontent-%COMP%] {\\n    display: flex;\\n    flex-direction: column;\\n    align-items: flex-end;\\n    gap: 0.5rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵlistener", "ModuleComponent_div_7_Template_div_click_0_listener", "restoredCtx", "ɵɵrestoreView", "_r3", "module_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "onModuleSelect", "name", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵstyleProp", "color", "ɵɵtextInterpolate", "icon", "replace", "ModuleComponent", "constructor", "router", "selectionService", "modules", "ngOnInit", "moduleName", "alert", "setSelectedModule", "navigate", "ɵɵdirectiveInject", "i1", "Router", "i2", "SelectionService", "selectors", "decls", "vars", "consts", "template", "ModuleComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵtemplate", "ModuleComponent_div_7_Template", "ɵɵproperty"], "sources": ["C:\\cal_wf\\Augment\\4-img\\apqr400bl\\src\\app\\module\\module.component.ts", "C:\\cal_wf\\Augment\\4-img\\apqr400bl\\src\\app\\module\\module.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { SelectionService } from '../shared/selection.service';\n\n@Component({\n  selector: 'app-module',\n  templateUrl: './module.component.html',\n  styleUrls: ['./module.component.scss']\n})\nexport class ModuleComponent implements OnInit {\n  modules = [\n    {\n      name: 'Caliber aPQR',\n      icon: 'analytics',\n      color: '#00a99d'\n    },\n    {\n      name: 'Caliber CPV',\n      icon: 'description',\n      color: '#00a99d'\n    },\n    {\n      name: 'Caliber Metrix',\n      icon: 'bubble_chart',\n      color: '#00a99d'\n    },\n    {\n      name: 'Caliber Pulse',\n      icon: 'trending_up',\n      color: '#00a99d'\n    }\n  ];\n\n  constructor(\n    private router: Router,\n    private selectionService: SelectionService\n  ) { }\n\n  ngOnInit(): void {\n  }\n\n  onModuleSelect(moduleName: string): void {\n    alert(`You selected ${moduleName}`);\n    this.selectionService.setSelectedModule(moduleName);\n    this.router.navigate(['/plantselection']);\n  }\n}\n", "<div class=\"module-selection-container\">\n  <div class=\"left-panel\">\n    <div class=\"quari-logo\">\n      <div class=\"logo-image\">\n        <img src=\"assets/quariDark.png\" alt=\"QUARI Logo\">\n      </div>\n    </div>\n  </div>\n  <div class=\"right-panel\">\n    <div class=\"module-grid\">\n      <div class=\"module-card\" *ngFor=\"let module of modules\" (click)=\"onModuleSelect(module.name)\">\n        <div class=\"card-content\">\n          <div class=\"icon-container\">\n            <mat-icon [style.color]=\"module.color\">{{module.icon}}</mat-icon>\n          </div>\n          <div class=\"module-name\">\n            <span class=\"caliber\">Caliber</span> <span class=\"product-name\">{{module.name.replace('Caliber ', '')}}</span>\n          </div>\n        </div>\n      </div>\n    </div>\n    <div class=\"footer\">\n      <div class=\"footer-links\">\n        <a href=\"#\">Contact Admin</a> |\n        <a href=\"#\"><mat-icon class=\"language-icon\">language</mat-icon> Choose Language</a>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;;;;;;;ICUMA,EAAA,CAAAC,cAAA,cAA8F;IAAtCD,EAAA,CAAAE,UAAA,mBAAAC,oDAAA;MAAA,MAAAC,WAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,SAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAG,cAAA,CAAAL,SAAA,CAAAM,IAAA,CAA2B;IAAA,EAAC;IAC3Fb,EAAA,CAAAC,cAAA,cAA0B;IAEiBD,EAAA,CAAAc,MAAA,GAAe;IAAAd,EAAA,CAAAe,YAAA,EAAW;IAEnEf,EAAA,CAAAC,cAAA,cAAyB;IACDD,EAAA,CAAAc,MAAA,cAAO;IAAAd,EAAA,CAAAe,YAAA,EAAO;IAACf,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAc,MAAA,GAAuC;IAAAd,EAAA,CAAAe,YAAA,EAAO;;;;IAHpGf,EAAA,CAAAgB,SAAA,GAA4B;IAA5BhB,EAAA,CAAAiB,WAAA,UAAAV,SAAA,CAAAW,KAAA,CAA4B;IAAClB,EAAA,CAAAgB,SAAA,GAAe;IAAfhB,EAAA,CAAAmB,iBAAA,CAAAZ,SAAA,CAAAa,IAAA,CAAe;IAGUpB,EAAA,CAAAgB,SAAA,GAAuC;IAAvChB,EAAA,CAAAmB,iBAAA,CAAAZ,SAAA,CAAAM,IAAA,CAAAQ,OAAA,iBAAuC;;;ADPnH,OAAM,MAAOC,eAAe;EAwB1BC,YACUC,MAAc,EACdC,gBAAkC;IADlC,KAAAD,MAAM,GAANA,MAAM;IACN,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAzB1B,KAAAC,OAAO,GAAG,CACR;MACEb,IAAI,EAAE,cAAc;MACpBO,IAAI,EAAE,WAAW;MACjBF,KAAK,EAAE;KACR,EACD;MACEL,IAAI,EAAE,aAAa;MACnBO,IAAI,EAAE,aAAa;MACnBF,KAAK,EAAE;KACR,EACD;MACEL,IAAI,EAAE,gBAAgB;MACtBO,IAAI,EAAE,cAAc;MACpBF,KAAK,EAAE;KACR,EACD;MACEL,IAAI,EAAE,eAAe;MACrBO,IAAI,EAAE,aAAa;MACnBF,KAAK,EAAE;KACR,CACF;EAKG;EAEJS,QAAQA,CAAA,GACR;EAEAf,cAAcA,CAACgB,UAAkB;IAC/BC,KAAK,CAAC,gBAAgBD,UAAU,EAAE,CAAC;IACnC,IAAI,CAACH,gBAAgB,CAACK,iBAAiB,CAACF,UAAU,CAAC;IACnD,IAAI,CAACJ,MAAM,CAACO,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;EAC3C;;;uBApCWT,eAAe,EAAAtB,EAAA,CAAAgC,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAlC,EAAA,CAAAgC,iBAAA,CAAAG,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAAfd,eAAe;MAAAe,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCT5B3C,EAAA,CAAAC,cAAA,aAAwC;UAIhCD,EAAA,CAAA6C,SAAA,aAAiD;UACnD7C,EAAA,CAAAe,YAAA,EAAM;UAGVf,EAAA,CAAAC,cAAA,aAAyB;UAErBD,EAAA,CAAA8C,UAAA,IAAAC,8BAAA,kBASM;UACR/C,EAAA,CAAAe,YAAA,EAAM;UACNf,EAAA,CAAAC,cAAA,aAAoB;UAEJD,EAAA,CAAAc,MAAA,qBAAa;UAAAd,EAAA,CAAAe,YAAA,EAAI;UAACf,EAAA,CAAAc,MAAA,WAC9B;UAAAd,EAAA,CAAAC,cAAA,aAAY;UAAgCD,EAAA,CAAAc,MAAA,gBAAQ;UAAAd,EAAA,CAAAe,YAAA,EAAW;UAACf,EAAA,CAAAc,MAAA,wBAAe;UAAAd,EAAA,CAAAe,YAAA,EAAI;;;UAdzCf,EAAA,CAAAgB,SAAA,GAAU;UAAVhB,EAAA,CAAAgD,UAAA,YAAAJ,GAAA,CAAAlB,OAAA,CAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}