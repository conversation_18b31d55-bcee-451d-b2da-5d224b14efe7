{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../shared/selection.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/material/icon\";\nfunction PlantselectionComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵlistener(\"click\", function PlantselectionComponent_div_17_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r3);\n      const plant_r1 = restoredCtx.$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onPlantSelect(plant_r1.name));\n    });\n    i0.ɵɵelementStart(1, \"div\", 18);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"chevron_right\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const plant_r1 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(plant_r1.name);\n  }\n}\nexport class PlantselectionComponent {\n  constructor(router, selectionService) {\n    this.router = router;\n    this.selectionService = selectionService;\n    this.plants = [{\n      name: 'CaliberPlant-1'\n    }, {\n      name: 'CaliberPlant-2'\n    }, {\n      name: 'CaliberPlant-3'\n    }, {\n      name: 'CaliberPlant-4'\n    }, {\n      name: 'CaliberPlant-5'\n    }];\n  }\n  ngOnInit() {}\n  onPlantSelect(plantName) {\n    alert(`You selected ${plantName}`);\n    this.selectionService.setSelectedPlant(plantName);\n    this.router.navigate(['/layout']);\n  }\n  static {\n    this.ɵfac = function PlantselectionComponent_Factory(t) {\n      return new (t || PlantselectionComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.SelectionService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PlantselectionComponent,\n      selectors: [[\"app-plantselection\"]],\n      decls: 27,\n      vars: 1,\n      consts: [[1, \"plantselection-container\"], [1, \"left-panel\"], [1, \"quari-logo\"], [1, \"logo-image\"], [\"src\", \"assets/quariDark.png\", \"alt\", \"QUARI Logo\"], [1, \"right-panel\"], [1, \"user-info\"], [1, \"user-avatar\"], [\"src\", \"assets/user-avatar.svg\", \"alt\", \"User Avatar\"], [1, \"user-greeting\"], [1, \"plant-selection-content\"], [1, \"plant-list\"], [\"class\", \"plant-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"footer\"], [1, \"footer-links\"], [\"href\", \"#\"], [1, \"language-icon\"], [1, \"plant-item\", 3, \"click\"], [1, \"plant-name\"]],\n      template: function PlantselectionComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelement(4, \"img\", 4);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7);\n          i0.ɵɵelement(8, \"img\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"div\", 9);\n          i0.ɵɵtext(10, \" Hello, Sumathi \");\n          i0.ɵɵelementStart(11, \"mat-icon\");\n          i0.ɵɵtext(12, \"arrow_drop_down\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 10)(14, \"h2\");\n          i0.ɵɵtext(15, \"Select a plant\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"div\", 11);\n          i0.ɵɵtemplate(17, PlantselectionComponent_div_17_Template, 5, 1, \"div\", 12);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"div\", 13)(19, \"div\", 14)(20, \"a\", 15);\n          i0.ɵɵtext(21, \"Contact Admin\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(22, \" | \");\n          i0.ɵɵelementStart(23, \"a\", 15)(24, \"mat-icon\", 16);\n          i0.ɵɵtext(25, \"language\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(26, \" Choose Language\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(17);\n          i0.ɵɵproperty(\"ngForOf\", ctx.plants);\n        }\n      },\n      dependencies: [i3.NgForOf, i4.MatIcon],\n      styles: [\".plantselection-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  min-height: 100vh;\\n  width: 100%;\\n  font-family: \\\"Roboto\\\", sans-serif;\\n}\\n\\n.left-panel[_ngcontent-%COMP%] {\\n  width: 60%;\\n  background-color: #002333;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  padding: 0;\\n  color: white;\\n  overflow: hidden;\\n}\\n\\n.quari-logo[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 100%;\\n  height: 100%;\\n}\\n\\n.logo-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.logo-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  object-position: center;\\n}\\n\\n.caliber[_ngcontent-%COMP%] {\\n  color: #00a99d;\\n  font-weight: 500;\\n}\\n\\n.right-panel[_ngcontent-%COMP%] {\\n  width: 40%;\\n  display: flex;\\n  flex-direction: column;\\n  padding: 2rem;\\n  background-color: #f8f9fa;\\n}\\n\\n.user-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  align-items: center;\\n  margin-bottom: 2rem;\\n  padding: 1rem 0;\\n}\\n\\n.user-avatar[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  overflow: hidden;\\n  margin-right: 0.5rem;\\n}\\n\\n.user-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n\\n.user-greeting[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  color: #333;\\n  font-size: 0.9rem;\\n}\\n\\n.plant-selection-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\nh2[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 500;\\n  color: #333;\\n  margin-bottom: 2rem;\\n}\\n\\n.plant-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.5rem;\\n}\\n\\n.plant-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 1rem 0;\\n  border-bottom: 1px solid #e0e0e0;\\n  cursor: pointer;\\n  transition: background-color 0.2s;\\n}\\n\\n.plant-item[_ngcontent-%COMP%]:hover {\\n  background-color: #f0f0f0;\\n}\\n\\n.plant-name[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: #333;\\n}\\n\\n.footer[_ngcontent-%COMP%] {\\n  margin-top: auto;\\n  text-align: right;\\n  padding-top: 1rem;\\n  border-top: 1px solid #eee;\\n}\\n\\n.footer-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #666;\\n  text-decoration: none;\\n  font-size: 0.9rem;\\n  display: inline-flex;\\n  align-items: center;\\n}\\n\\n.footer-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  color: #00a99d;\\n}\\n\\n.language-icon[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  height: 1.2rem;\\n  width: 1.2rem;\\n  margin-right: 0.2rem;\\n}\\n\\n\\n\\n@media (min-width: 1600px) {\\n  .logo-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    object-position: center;\\n  }\\n}\\n@media (max-width: 992px) {\\n  .plantselection-container[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .left-panel[_ngcontent-%COMP%], .right-panel[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .left-panel[_ngcontent-%COMP%] {\\n    min-height: 40vh;\\n    padding: 0;\\n  }\\n  .quari-logo[_ngcontent-%COMP%] {\\n    min-height: 40vh;\\n  }\\n  .right-panel[_ngcontent-%COMP%] {\\n    min-height: auto;\\n  }\\n  .logo-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 100%;\\n    height: 100%;\\n    object-fit: cover;\\n    object-position: center;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .left-panel[_ngcontent-%COMP%] {\\n    padding: 0;\\n  }\\n  .right-panel[_ngcontent-%COMP%] {\\n    padding: 1.5rem 1rem;\\n    min-height: auto; \\n\\n  }\\n  .footer-links[_ngcontent-%COMP%] {\\n    display: flex;\\n    flex-direction: column;\\n    align-items: flex-end;\\n    gap: 0.5rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵlistener", "PlantselectionComponent_div_17_Template_div_click_0_listener", "restoredCtx", "ɵɵrestoreView", "_r3", "plant_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "onPlantSelect", "name", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "PlantselectionComponent", "constructor", "router", "selectionService", "plants", "ngOnInit", "plantName", "alert", "setSelectedPlant", "navigate", "ɵɵdirectiveInject", "i1", "Router", "i2", "SelectionService", "selectors", "decls", "vars", "consts", "template", "PlantselectionComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵtemplate", "PlantselectionComponent_div_17_Template", "ɵɵproperty"], "sources": ["C:\\cal_wf\\Augment\\4-img\\apqr400bl\\src\\app\\plantselection\\plantselection.component.ts", "C:\\cal_wf\\Augment\\4-img\\apqr400bl\\src\\app\\plantselection\\plantselection.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { SelectionService } from '../shared/selection.service';\n\n@Component({\n  selector: 'app-plantselection',\n  templateUrl: './plantselection.component.html',\n  styleUrls: ['./plantselection.component.scss']\n})\nexport class PlantselectionComponent implements OnInit {\n  plants = [\n    { name: 'CaliberPlant-1' },\n    { name: 'CaliberPlant-2' },\n    { name: 'CaliberPlant-3' },\n    { name: 'CaliberPlant-4' },\n    { name: 'CaliberPlant-5' }\n  ];\n\n  constructor(\n    private router: Router,\n    private selectionService: SelectionService\n  ) { }\n\n  ngOnInit(): void {\n  }\n\n  onPlantSelect(plantName: string): void {\n    alert(`You selected ${plantName}`);\n    this.selectionService.setSelectedPlant(plantName);\n    this.router.navigate(['/layout']);\n  }\n}\n", "<div class=\"plantselection-container\">\n  <div class=\"left-panel\">\n    <div class=\"quari-logo\">\n      <div class=\"logo-image\">\n        <img src=\"assets/quariDark.png\" alt=\"QUARI Logo\">\n      </div>\n    </div>\n  </div>\n  <div class=\"right-panel\">\n    <div class=\"user-info\">\n      <div class=\"user-avatar\">\n        <img src=\"assets/user-avatar.svg\" alt=\"User Avatar\">\n      </div>\n      <div class=\"user-greeting\">\n        Hello, Sumathi <mat-icon>arrow_drop_down</mat-icon>\n      </div>\n    </div>\n\n    <div class=\"plant-selection-content\">\n      <h2>Select a plant</h2>\n\n      <div class=\"plant-list\">\n        <div class=\"plant-item\" *ngFor=\"let plant of plants\" (click)=\"onPlantSelect(plant.name)\">\n          <div class=\"plant-name\">{{ plant.name }}</div>\n          <mat-icon>chevron_right</mat-icon>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"footer\">\n      <div class=\"footer-links\">\n        <a href=\"#\">Contact Admin</a> |\n        <a href=\"#\"><mat-icon class=\"language-icon\">language</mat-icon> Choose Language</a>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;;;;;;;ICsBQA,EAAA,CAAAC,cAAA,cAAyF;IAApCD,EAAA,CAAAE,UAAA,mBAAAC,6DAAA;MAAA,MAAAC,WAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,QAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAG,aAAA,CAAAL,QAAA,CAAAM,IAAA,CAAyB;IAAA,EAAC;IACtFb,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAc,MAAA,GAAgB;IAAAd,EAAA,CAAAe,YAAA,EAAM;IAC9Cf,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAc,MAAA,oBAAa;IAAAd,EAAA,CAAAe,YAAA,EAAW;;;;IADVf,EAAA,CAAAgB,SAAA,GAAgB;IAAhBhB,EAAA,CAAAiB,iBAAA,CAAAV,QAAA,CAAAM,IAAA,CAAgB;;;ADdlD,OAAM,MAAOK,uBAAuB;EASlCC,YACUC,MAAc,EACdC,gBAAkC;IADlC,KAAAD,MAAM,GAANA,MAAM;IACN,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAV1B,KAAAC,MAAM,GAAG,CACP;MAAET,IAAI,EAAE;IAAgB,CAAE,EAC1B;MAAEA,IAAI,EAAE;IAAgB,CAAE,EAC1B;MAAEA,IAAI,EAAE;IAAgB,CAAE,EAC1B;MAAEA,IAAI,EAAE;IAAgB,CAAE,EAC1B;MAAEA,IAAI,EAAE;IAAgB,CAAE,CAC3B;EAKG;EAEJU,QAAQA,CAAA,GACR;EAEAX,aAAaA,CAACY,SAAiB;IAC7BC,KAAK,CAAC,gBAAgBD,SAAS,EAAE,CAAC;IAClC,IAAI,CAACH,gBAAgB,CAACK,gBAAgB,CAACF,SAAS,CAAC;IACjD,IAAI,CAACJ,MAAM,CAACO,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;EACnC;;;uBArBWT,uBAAuB,EAAAlB,EAAA,CAAA4B,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAA9B,EAAA,CAAA4B,iBAAA,CAAAG,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAAvBd,uBAAuB;MAAAe,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCTpCvC,EAAA,CAAAC,cAAA,aAAsC;UAI9BD,EAAA,CAAAyC,SAAA,aAAiD;UACnDzC,EAAA,CAAAe,YAAA,EAAM;UAGVf,EAAA,CAAAC,cAAA,aAAyB;UAGnBD,EAAA,CAAAyC,SAAA,aAAoD;UACtDzC,EAAA,CAAAe,YAAA,EAAM;UACNf,EAAA,CAAAC,cAAA,aAA2B;UACzBD,EAAA,CAAAc,MAAA,wBAAe;UAAAd,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAc,MAAA,uBAAe;UAAAd,EAAA,CAAAe,YAAA,EAAW;UAIvDf,EAAA,CAAAC,cAAA,eAAqC;UAC/BD,EAAA,CAAAc,MAAA,sBAAc;UAAAd,EAAA,CAAAe,YAAA,EAAK;UAEvBf,EAAA,CAAAC,cAAA,eAAwB;UACtBD,EAAA,CAAA0C,UAAA,KAAAC,uCAAA,kBAGM;UACR3C,EAAA,CAAAe,YAAA,EAAM;UAGRf,EAAA,CAAAC,cAAA,eAAoB;UAEJD,EAAA,CAAAc,MAAA,qBAAa;UAAAd,EAAA,CAAAe,YAAA,EAAI;UAACf,EAAA,CAAAc,MAAA,WAC9B;UAAAd,EAAA,CAAAC,cAAA,aAAY;UAAgCD,EAAA,CAAAc,MAAA,gBAAQ;UAAAd,EAAA,CAAAe,YAAA,EAAW;UAACf,EAAA,CAAAc,MAAA,wBAAe;UAAAd,EAAA,CAAAe,YAAA,EAAI;;;UAVzCf,EAAA,CAAAgB,SAAA,IAAS;UAAThB,EAAA,CAAA4C,UAAA,YAAAJ,GAAA,CAAAlB,MAAA,CAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}