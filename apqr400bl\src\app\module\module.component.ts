import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { SelectionService } from '../shared/selection.service';

@Component({
  selector: 'app-module',
  templateUrl: './module.component.html',
  styleUrls: ['./module.component.scss']
})
export class ModuleComponent implements OnInit {
  modules = [
    {
      name: 'Caliber aPQR',
      icon: 'analytics',
      color: '#00a99d'
    },
    {
      name: 'Caliber CPV',
      icon: 'description',
      color: '#00a99d'
    },
    {
      name: 'Caliber Metrix',
      icon: 'bubble_chart',
      color: '#00a99d'
    },
    {
      name: 'Caliber Pulse',
      icon: 'trending_up',
      color: '#00a99d'
    }
  ];

  constructor(
    private router: Router,
    private selectionService: SelectionService
  ) { }

  ngOnInit(): void {
  }

  onModuleSelect(moduleName: string): void {
    alert(`You selected ${moduleName}`);
    this.selectionService.setSelectedModule(moduleName);
    this.router.navigate(['/plantselection']);
  }
}
