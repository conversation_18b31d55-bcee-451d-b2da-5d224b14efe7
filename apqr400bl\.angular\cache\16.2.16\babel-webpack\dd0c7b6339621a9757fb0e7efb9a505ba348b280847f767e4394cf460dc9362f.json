{"ast": null, "code": "import { ElementRef } from '@angular/core';\n\n/** Coerces a data-bound value (typically a string) to a boolean. */\nfunction coerceBooleanProperty(value) {\n  return value != null && `${value}` !== 'false';\n}\nfunction coerceNumberProperty(value, fallbackValue = 0) {\n  return _isNumberValue(value) ? Number(value) : fallbackValue;\n}\n/**\n * Whether the provided value is considered a number.\n * @docs-private\n */\nfunction _isNumberValue(value) {\n  // parseFloat(value) handles most of the cases we're interested in (it treats null, empty string,\n  // and other non-number values as NaN, where Number just uses 0) but it considers the string\n  // '123hello' to be a valid number. Therefore we also check if Number(value) is NaN.\n  return !isNaN(parseFloat(value)) && !isNaN(Number(value));\n}\nfunction coerceArray(value) {\n  return Array.isArray(value) ? value : [value];\n}\n\n/** Coerces a value to a CSS pixel value. */\nfunction coerceCssPixelValue(value) {\n  if (value == null) {\n    return '';\n  }\n  return typeof value === 'string' ? value : `${value}px`;\n}\n\n/**\n * Coerces an ElementRef or an Element into an element.\n * Useful for APIs that can accept either a ref or the native element itself.\n */\nfunction coerceElement(elementOrRef) {\n  return elementOrRef instanceof ElementRef ? elementOrRef.nativeElement : elementOrRef;\n}\n\n/**\n * Coerces a value to an array of trimmed non-empty strings.\n * Any input that is not an array, `null` or `undefined` will be turned into a string\n * via `toString()` and subsequently split with the given separator.\n * `null` and `undefined` will result in an empty array.\n * This results in the following outcomes:\n * - `null` -&gt; `[]`\n * - `[null]` -&gt; `[\"null\"]`\n * - `[\"a\", \"b \", \" \"]` -&gt; `[\"a\", \"b\"]`\n * - `[1, [2, 3]]` -&gt; `[\"1\", \"2,3\"]`\n * - `[{ a: 0 }]` -&gt; `[\"[object Object]\"]`\n * - `{ a: 0 }` -&gt; `[\"[object\", \"Object]\"]`\n *\n * Useful for defining CSS classes or table columns.\n * @param value the value to coerce into an array of strings\n * @param separator split-separator if value isn't an array\n */\nfunction coerceStringArray(value, separator = /\\s+/) {\n  const result = [];\n  if (value != null) {\n    const sourceValues = Array.isArray(value) ? value : `${value}`.split(separator);\n    for (const sourceValue of sourceValues) {\n      const trimmedString = `${sourceValue}`.trim();\n      if (trimmedString) {\n        result.push(trimmedString);\n      }\n    }\n  }\n  return result;\n}\nexport { _isNumberValue, coerceArray, coerceBooleanProperty, coerceCssPixelValue, coerceElement, coerceNumberProperty, coerceStringArray };", "map": {"version": 3, "names": ["ElementRef", "coerceBooleanProperty", "value", "coerceNumberProperty", "fallback<PERSON><PERSON><PERSON>", "_isNumberValue", "Number", "isNaN", "parseFloat", "coerce<PERSON><PERSON><PERSON>", "Array", "isArray", "coerceCssPixelValue", "coerceElement", "elementOrRef", "nativeElement", "coerce<PERSON><PERSON><PERSON><PERSON><PERSON>", "separator", "result", "sourceValues", "split", "sourceValue", "trimmedString", "trim", "push"], "sources": ["C:/cal_wf/Augment/4-img/apqr400bl/node_modules/@angular/cdk/fesm2022/coercion.mjs"], "sourcesContent": ["import { ElementRef } from '@angular/core';\n\n/** Coerces a data-bound value (typically a string) to a boolean. */\nfunction coerceBooleanProperty(value) {\n    return value != null && `${value}` !== 'false';\n}\n\nfunction coerceNumberProperty(value, fallbackValue = 0) {\n    return _isNumberValue(value) ? Number(value) : fallbackValue;\n}\n/**\n * Whether the provided value is considered a number.\n * @docs-private\n */\nfunction _isNumberValue(value) {\n    // parseFloat(value) handles most of the cases we're interested in (it treats null, empty string,\n    // and other non-number values as NaN, where Number just uses 0) but it considers the string\n    // '123hello' to be a valid number. Therefore we also check if Number(value) is NaN.\n    return !isNaN(parseFloat(value)) && !isNaN(Number(value));\n}\n\nfunction coerceArray(value) {\n    return Array.isArray(value) ? value : [value];\n}\n\n/** Coerces a value to a CSS pixel value. */\nfunction coerceCssPixelValue(value) {\n    if (value == null) {\n        return '';\n    }\n    return typeof value === 'string' ? value : `${value}px`;\n}\n\n/**\n * Coerces an ElementRef or an Element into an element.\n * Useful for APIs that can accept either a ref or the native element itself.\n */\nfunction coerceElement(elementOrRef) {\n    return elementOrRef instanceof ElementRef ? elementOrRef.nativeElement : elementOrRef;\n}\n\n/**\n * Coerces a value to an array of trimmed non-empty strings.\n * Any input that is not an array, `null` or `undefined` will be turned into a string\n * via `toString()` and subsequently split with the given separator.\n * `null` and `undefined` will result in an empty array.\n * This results in the following outcomes:\n * - `null` -&gt; `[]`\n * - `[null]` -&gt; `[\"null\"]`\n * - `[\"a\", \"b \", \" \"]` -&gt; `[\"a\", \"b\"]`\n * - `[1, [2, 3]]` -&gt; `[\"1\", \"2,3\"]`\n * - `[{ a: 0 }]` -&gt; `[\"[object Object]\"]`\n * - `{ a: 0 }` -&gt; `[\"[object\", \"Object]\"]`\n *\n * Useful for defining CSS classes or table columns.\n * @param value the value to coerce into an array of strings\n * @param separator split-separator if value isn't an array\n */\nfunction coerceStringArray(value, separator = /\\s+/) {\n    const result = [];\n    if (value != null) {\n        const sourceValues = Array.isArray(value) ? value : `${value}`.split(separator);\n        for (const sourceValue of sourceValues) {\n            const trimmedString = `${sourceValue}`.trim();\n            if (trimmedString) {\n                result.push(trimmedString);\n            }\n        }\n    }\n    return result;\n}\n\nexport { _isNumberValue, coerceArray, coerceBooleanProperty, coerceCssPixelValue, coerceElement, coerceNumberProperty, coerceStringArray };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;;AAE1C;AACA,SAASC,qBAAqBA,CAACC,KAAK,EAAE;EAClC,OAAOA,KAAK,IAAI,IAAI,IAAK,GAAEA,KAAM,EAAC,KAAK,OAAO;AAClD;AAEA,SAASC,oBAAoBA,CAACD,KAAK,EAAEE,aAAa,GAAG,CAAC,EAAE;EACpD,OAAOC,cAAc,CAACH,KAAK,CAAC,GAAGI,MAAM,CAACJ,KAAK,CAAC,GAAGE,aAAa;AAChE;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACH,KAAK,EAAE;EAC3B;EACA;EACA;EACA,OAAO,CAACK,KAAK,CAACC,UAAU,CAACN,KAAK,CAAC,CAAC,IAAI,CAACK,KAAK,CAACD,MAAM,CAACJ,KAAK,CAAC,CAAC;AAC7D;AAEA,SAASO,WAAWA,CAACP,KAAK,EAAE;EACxB,OAAOQ,KAAK,CAACC,OAAO,CAACT,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACA,KAAK,CAAC;AACjD;;AAEA;AACA,SAASU,mBAAmBA,CAACV,KAAK,EAAE;EAChC,IAAIA,KAAK,IAAI,IAAI,EAAE;IACf,OAAO,EAAE;EACb;EACA,OAAO,OAAOA,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAI,GAAEA,KAAM,IAAG;AAC3D;;AAEA;AACA;AACA;AACA;AACA,SAASW,aAAaA,CAACC,YAAY,EAAE;EACjC,OAAOA,YAAY,YAAYd,UAAU,GAAGc,YAAY,CAACC,aAAa,GAAGD,YAAY;AACzF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,iBAAiBA,CAACd,KAAK,EAAEe,SAAS,GAAG,KAAK,EAAE;EACjD,MAAMC,MAAM,GAAG,EAAE;EACjB,IAAIhB,KAAK,IAAI,IAAI,EAAE;IACf,MAAMiB,YAAY,GAAGT,KAAK,CAACC,OAAO,CAACT,KAAK,CAAC,GAAGA,KAAK,GAAI,GAAEA,KAAM,EAAC,CAACkB,KAAK,CAACH,SAAS,CAAC;IAC/E,KAAK,MAAMI,WAAW,IAAIF,YAAY,EAAE;MACpC,MAAMG,aAAa,GAAI,GAAED,WAAY,EAAC,CAACE,IAAI,CAAC,CAAC;MAC7C,IAAID,aAAa,EAAE;QACfJ,MAAM,CAACM,IAAI,CAACF,aAAa,CAAC;MAC9B;IACJ;EACJ;EACA,OAAOJ,MAAM;AACjB;AAEA,SAASb,cAAc,EAAEI,WAAW,EAAER,qBAAqB,EAAEW,mBAAmB,EAAEC,aAAa,EAAEV,oBAAoB,EAAEa,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}