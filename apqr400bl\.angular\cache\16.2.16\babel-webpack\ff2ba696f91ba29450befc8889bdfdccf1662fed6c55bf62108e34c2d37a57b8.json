{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../shared/selection.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/material/icon\";\nfunction ModuleComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵlistener(\"click\", function ModuleComponent_div_7_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r3);\n      const module_r1 = restoredCtx.$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onModuleSelect(module_r1.name));\n    });\n    i0.ɵɵelementStart(1, \"div\", 13)(2, \"div\", 14)(3, \"mat-icon\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 15)(6, \"span\", 16);\n    i0.ɵɵtext(7, \"Caliber\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 17);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const module_r1 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"color\", module_r1.color);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(module_r1.icon);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(module_r1.name.replace(\"Caliber \", \"\"));\n  }\n}\nexport class ModuleComponent {\n  constructor(router, selectionService) {\n    this.router = router;\n    this.selectionService = selectionService;\n    this.modules = [{\n      name: 'Caliber aPQR',\n      icon: 'analytics',\n      color: '#00a99d'\n    }, {\n      name: 'Caliber CPV',\n      icon: 'description',\n      color: '#00a99d'\n    }, {\n      name: 'Caliber Metrix',\n      icon: 'bubble_chart',\n      color: '#00a99d'\n    }, {\n      name: 'Caliber Pulse',\n      icon: 'trending_up',\n      color: '#00a99d'\n    }];\n  }\n  ngOnInit() {}\n  onModuleSelect(moduleName) {\n    alert(`You selected ${moduleName}`);\n    this.selectionService.setSelectedModule(moduleName);\n    this.router.navigate(['/plantselection']);\n  }\n  static {\n    this.ɵfac = function ModuleComponent_Factory(t) {\n      return new (t || ModuleComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.SelectionService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ModuleComponent,\n      selectors: [[\"app-module\"]],\n      decls: 17,\n      vars: 1,\n      consts: [[1, \"module-selection-container\"], [1, \"left-panel\"], [1, \"quari-logo\"], [1, \"logo-image\"], [\"src\", \"assets/quariDark.png\", \"alt\", \"QUARI Logo\"], [1, \"right-panel\"], [1, \"module-grid\"], [\"class\", \"module-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"footer\"], [1, \"footer-links\"], [\"href\", \"#\"], [1, \"language-icon\"], [1, \"module-card\", 3, \"click\"], [1, \"card-content\"], [1, \"icon-container\"], [1, \"module-name\"], [1, \"caliber\"], [1, \"product-name\"]],\n      template: function ModuleComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelement(4, \"img\", 4);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(5, \"div\", 5)(6, \"div\", 6);\n          i0.ɵɵtemplate(7, ModuleComponent_div_7_Template, 10, 4, \"div\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 8)(9, \"div\", 9)(10, \"a\", 10);\n          i0.ɵɵtext(11, \"Contact Admin\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(12, \" | \");\n          i0.ɵɵelementStart(13, \"a\", 10)(14, \"mat-icon\", 11);\n          i0.ɵɵtext(15, \"language\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(16, \" Choose Language\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngForOf\", ctx.modules);\n        }\n      },\n      dependencies: [i3.NgForOf, i4.MatIcon],\n      styles: [\".module-selection-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  min-height: 100vh;\\n  width: 100%;\\n  font-family: \\\"Roboto\\\", sans-serif;\\n}\\n\\n.left-panel[_ngcontent-%COMP%] {\\n  width: 60%;\\n  background-color: #002333;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  padding: 0;\\n  color: white;\\n  overflow: hidden;\\n  height: 100%; \\n\\n}\\n\\n.quari-logo[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 100%;\\n  height: 100%;\\n}\\n\\n.logo-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.logo-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  object-position: center;\\n}\\n\\n.right-panel[_ngcontent-%COMP%] {\\n  width: 40%;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  padding: 2rem;\\n  background-color: #f8f9fa;\\n  height: 100%;\\n  overflow-y: auto; \\n\\n}\\n\\n.module-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(2, 1fr);\\n  gap: 1.5rem;\\n  max-width: 800px;\\n  margin: 0 auto;\\n  flex: 1; \\n\\n}\\n\\n.module-card[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\\n  padding: 1.5rem;\\n  cursor: pointer;\\n  transition: transform 0.2s, box-shadow 0.2s;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  text-align: center;\\n  aspect-ratio: 1/1; \\n\\n  max-width: 100%; \\n\\n}\\n\\n.module-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\\n}\\n\\n.card-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 100%;\\n}\\n\\n.icon-container[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n\\n.icon-container[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 3.15rem; \\n\\n  height: 3.15rem;\\n  width: 3.15rem;\\n}\\n\\n.module-name[_ngcontent-%COMP%] {\\n  font-size: 1.5rem; \\n\\n  font-weight: 400;\\n}\\n\\n.caliber[_ngcontent-%COMP%] {\\n  color: #00a99d;\\n  font-weight: 500;\\n}\\n\\n.product-name[_ngcontent-%COMP%] {\\n  color: #333;\\n}\\n\\n.footer[_ngcontent-%COMP%] {\\n  margin-top: 1rem;\\n  text-align: right;\\n  padding-top: 1rem;\\n  border-top: 1px solid #eee;\\n  flex-shrink: 0; \\n\\n}\\n\\n.footer-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #666;\\n  text-decoration: none;\\n  font-size: 0.9rem;\\n  display: inline-flex;\\n  align-items: center;\\n}\\n\\n.footer-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  color: #00a99d;\\n}\\n\\n.language-icon[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  height: 1.2rem;\\n  width: 1.2rem;\\n  margin-right: 0.2rem;\\n}\\n\\n\\n\\n@media (min-width: 1600px) {\\n  .logo-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    object-position: center;\\n  }\\n}\\n@media (max-width: 992px) {\\n  .module-selection-container[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .left-panel[_ngcontent-%COMP%], .right-panel[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .left-panel[_ngcontent-%COMP%] {\\n    height: 40vh; \\n\\n    padding: 0;\\n  }\\n  .logo-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 100%;\\n    height: 100%;\\n    object-fit: cover;\\n    object-position: center;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .module-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .module-card[_ngcontent-%COMP%] {\\n    aspect-ratio: 1/1; \\n\\n  }\\n  \\n\\n}\\n@media (max-width: 576px) {\\n  .left-panel[_ngcontent-%COMP%] {\\n    padding: 0;\\n  }\\n  .right-panel[_ngcontent-%COMP%] {\\n    padding: 1.5rem 1rem;\\n  }\\n  .footer-links[_ngcontent-%COMP%] {\\n    display: flex;\\n    flex-direction: column;\\n    align-items: flex-end;\\n    gap: 0.5rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbW9kdWxlL21vZHVsZS5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGFBQUE7RUFDQSxpQkFBQTtFQUNBLFdBQUE7RUFDQSxpQ0FBQTtBQUNGOztBQUVBO0VBQ0UsVUFBQTtFQUNBLHlCQUFBO0VBQ0EsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsdUJBQUE7RUFDQSxtQkFBQTtFQUNBLFVBQUE7RUFDQSxZQUFBO0VBQ0EsZ0JBQUE7RUFDQSxZQUFBLEVBQUEscUNBQUE7QUFDRjs7QUFFQTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7QUFDRjs7QUFFQTtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0EsYUFBQTtFQUNBLHVCQUFBO0VBQ0EsbUJBQUE7QUFDRjs7QUFFQTtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0EsaUJBQUE7RUFDQSx1QkFBQTtBQUNGOztBQUVBO0VBQ0UsVUFBQTtFQUNBLGFBQUE7RUFDQSxzQkFBQTtFQUNBLHVCQUFBO0VBQ0EsYUFBQTtFQUNBLHlCQUFBO0VBQ0EsWUFBQTtFQUNBLGdCQUFBLEVBQUEsOEJBQUE7QUFDRjs7QUFFQTtFQUNFLGFBQUE7RUFDQSxxQ0FBQTtFQUNBLFdBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxPQUFBLEVBQUEseUJBQUE7QUFDRjs7QUFFQTtFQUNFLHVCQUFBO0VBQ0Esa0JBQUE7RUFDQSwwQ0FBQTtFQUNBLGVBQUE7RUFDQSxlQUFBO0VBQ0EsMkNBQUE7RUFDQSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0Esa0JBQUE7RUFDQSxpQkFBQSxFQUFBLHlCQUFBO0VBQ0EsZUFBQSxFQUFBLCtCQUFBO0FBQ0Y7O0FBRUE7RUFDRSwyQkFBQTtFQUNBLHlDQUFBO0FBQ0Y7O0FBRUE7RUFDRSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0EsWUFBQTtBQUNGOztBQUVBO0VBQ0UsbUJBQUE7QUFDRjs7QUFFQTtFQUNFLGtCQUFBLEVBQUEsaUNBQUE7RUFDQSxlQUFBO0VBQ0EsY0FBQTtBQUNGOztBQUVBO0VBQ0UsaUJBQUEsRUFBQSwrQkFBQTtFQUNBLGdCQUFBO0FBQ0Y7O0FBRUE7RUFDRSxjQUFBO0VBQ0EsZ0JBQUE7QUFDRjs7QUFFQTtFQUNFLFdBQUE7QUFDRjs7QUFFQTtFQUNFLGdCQUFBO0VBQ0EsaUJBQUE7RUFDQSxpQkFBQTtFQUNBLDBCQUFBO0VBQ0EsY0FBQSxFQUFBLGtDQUFBO0FBQ0Y7O0FBRUE7RUFDRSxXQUFBO0VBQ0EscUJBQUE7RUFDQSxpQkFBQTtFQUNBLG9CQUFBO0VBQ0EsbUJBQUE7QUFDRjs7QUFFQTtFQUNFLGNBQUE7QUFDRjs7QUFFQTtFQUNFLGlCQUFBO0VBQ0EsY0FBQTtFQUNBLGFBQUE7RUFDQSxvQkFBQTtBQUNGOztBQUVBLHNCQUFBO0FBQ0E7RUFDRTtJQUNFLHVCQUFBO0VBQ0Y7QUFDRjtBQUVBO0VBQ0U7SUFDRSxzQkFBQTtFQUFGO0VBR0E7SUFDRSxXQUFBO0VBREY7RUFJQTtJQUNFLFlBQUEsRUFBQSwyQkFBQTtJQUNBLFVBQUE7RUFGRjtFQUtBO0lBQ0UsV0FBQTtJQUNBLFlBQUE7SUFDQSxpQkFBQTtJQUNBLHVCQUFBO0VBSEY7QUFDRjtBQU1BO0VBQ0U7SUFDRSwwQkFBQTtFQUpGO0VBT0E7SUFDRSxpQkFBQSxFQUFBLGlDQUFBO0VBTEY7RUFRQSw2QkFBQTtBQU5GO0FBU0E7RUFDRTtJQUNFLFVBQUE7RUFQRjtFQVVBO0lBQ0Usb0JBQUE7RUFSRjtFQVdBO0lBQ0UsYUFBQTtJQUNBLHNCQUFBO0lBQ0EscUJBQUE7SUFDQSxXQUFBO0VBVEY7QUFDRiIsInNvdXJjZXNDb250ZW50IjpbIi5tb2R1bGUtc2VsZWN0aW9uLWNvbnRhaW5lciB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIG1pbi1oZWlnaHQ6IDEwMHZoO1xuICB3aWR0aDogMTAwJTtcbiAgZm9udC1mYW1pbHk6ICdSb2JvdG8nLCBzYW5zLXNlcmlmO1xufVxuXG4ubGVmdC1wYW5lbCB7XG4gIHdpZHRoOiA2MCU7XG4gIGJhY2tncm91bmQtY29sb3I6ICMwMDIzMzM7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBwYWRkaW5nOiAwO1xuICBjb2xvcjogd2hpdGU7XG4gIG92ZXJmbG93OiBoaWRkZW47XG4gIGhlaWdodDogMTAwJTsgLyogVXNlIGhlaWdodCBpbnN0ZWFkIG9mIG1pbi1oZWlnaHQgKi9cbn1cblxuLnF1YXJpLWxvZ28ge1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgd2lkdGg6IDEwMCU7XG4gIGhlaWdodDogMTAwJTtcbn1cblxuLmxvZ28taW1hZ2Uge1xuICB3aWR0aDogMTAwJTtcbiAgaGVpZ2h0OiAxMDAlO1xuICBkaXNwbGF5OiBmbGV4O1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbn1cblxuLmxvZ28taW1hZ2UgaW1nIHtcbiAgd2lkdGg6IDEwMCU7XG4gIGhlaWdodDogMTAwJTtcbiAgb2JqZWN0LWZpdDogY292ZXI7XG4gIG9iamVjdC1wb3NpdGlvbjogY2VudGVyO1xufVxuXG4ucmlnaHQtcGFuZWwge1xuICB3aWR0aDogNDAlO1xuICBkaXNwbGF5OiBmbGV4O1xuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgcGFkZGluZzogMnJlbTtcbiAgYmFja2dyb3VuZC1jb2xvcjogI2Y4ZjlmYTtcbiAgaGVpZ2h0OiAxMDAlO1xuICBvdmVyZmxvdy15OiBhdXRvOyAvKiBBbGxvdyBzY3JvbGxpbmcgaWYgbmVlZGVkICovXG59XG5cbi5tb2R1bGUtZ3JpZCB7XG4gIGRpc3BsYXk6IGdyaWQ7XG4gIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KDIsIDFmcik7XG4gIGdhcDogMS41cmVtO1xuICBtYXgtd2lkdGg6IDgwMHB4O1xuICBtYXJnaW46IDAgYXV0bztcbiAgZmxleDogMTsgLyogVGFrZSBhdmFpbGFibGUgc3BhY2UgKi9cbn1cblxuLm1vZHVsZS1jYXJkIHtcbiAgYmFja2dyb3VuZC1jb2xvcjogd2hpdGU7XG4gIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgYm94LXNoYWRvdzogMCAycHggMTBweCByZ2JhKDAsIDAsIDAsIDAuMDUpO1xuICBwYWRkaW5nOiAxLjVyZW07XG4gIGN1cnNvcjogcG9pbnRlcjtcbiAgdHJhbnNpdGlvbjogdHJhbnNmb3JtIDAuMnMsIGJveC1zaGFkb3cgMC4ycztcbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgYXNwZWN0LXJhdGlvOiAxIC8gMTsgLyogTWFrZSB0aGUgY2FyZCBzcXVhcmUgKi9cbiAgbWF4LXdpZHRoOiAxMDAlOyAvKiBFbnN1cmUgaXQgZG9lc24ndCBvdmVyZmxvdyAqL1xufVxuXG4ubW9kdWxlLWNhcmQ6aG92ZXIge1xuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTVweCk7XG4gIGJveC1zaGFkb3c6IDAgNXB4IDE1cHggcmdiYSgwLCAwLCAwLCAwLjEpO1xufVxuXG4uY2FyZC1jb250ZW50IHtcbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gIGhlaWdodDogMTAwJTtcbn1cblxuLmljb24tY29udGFpbmVyIHtcbiAgbWFyZ2luLWJvdHRvbTogMXJlbTtcbn1cblxuLmljb24tY29udGFpbmVyIG1hdC1pY29uIHtcbiAgZm9udC1zaXplOiAzLjE1cmVtOyAvKiBJbmNyZWFzZWQgYnkgNTAlIGZyb20gMi4xcmVtICovXG4gIGhlaWdodDogMy4xNXJlbTtcbiAgd2lkdGg6IDMuMTVyZW07XG59XG5cbi5tb2R1bGUtbmFtZSB7XG4gIGZvbnQtc2l6ZTogMS41cmVtOyAvKiBJbmNyZWFzZWQgYnkgNTAlIGZyb20gMXJlbSAqL1xuICBmb250LXdlaWdodDogNDAwO1xufVxuXG4uY2FsaWJlciB7XG4gIGNvbG9yOiAjMDBhOTlkO1xuICBmb250LXdlaWdodDogNTAwO1xufVxuXG4ucHJvZHVjdC1uYW1lIHtcbiAgY29sb3I6ICMzMzM7XG59XG5cbi5mb290ZXIge1xuICBtYXJnaW4tdG9wOiAxcmVtO1xuICB0ZXh0LWFsaWduOiByaWdodDtcbiAgcGFkZGluZy10b3A6IDFyZW07XG4gIGJvcmRlci10b3A6IDFweCBzb2xpZCAjZWVlO1xuICBmbGV4LXNocmluazogMDsgLyogUHJldmVudCBmb290ZXIgZnJvbSBzaHJpbmtpbmcgKi9cbn1cblxuLmZvb3Rlci1saW5rcyBhIHtcbiAgY29sb3I6ICM2NjY7XG4gIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcbiAgZm9udC1zaXplOiAwLjlyZW07XG4gIGRpc3BsYXk6IGlubGluZS1mbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xufVxuXG4uZm9vdGVyLWxpbmtzIGE6aG92ZXIge1xuICBjb2xvcjogIzAwYTk5ZDtcbn1cblxuLmxhbmd1YWdlLWljb24ge1xuICBmb250LXNpemU6IDEuMnJlbTtcbiAgaGVpZ2h0OiAxLjJyZW07XG4gIHdpZHRoOiAxLjJyZW07XG4gIG1hcmdpbi1yaWdodDogMC4ycmVtO1xufVxuXG4vKiBSZXNwb25zaXZlIHN0eWxlcyAqL1xuQG1lZGlhIChtaW4td2lkdGg6IDE2MDBweCkge1xuICAubG9nby1pbWFnZSBpbWcge1xuICAgIG9iamVjdC1wb3NpdGlvbjogY2VudGVyO1xuICB9XG59XG5cbkBtZWRpYSAobWF4LXdpZHRoOiA5OTJweCkge1xuICAubW9kdWxlLXNlbGVjdGlvbi1jb250YWluZXIge1xuICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gIH1cblxuICAubGVmdC1wYW5lbCwgLnJpZ2h0LXBhbmVsIHtcbiAgICB3aWR0aDogMTAwJTtcbiAgfVxuXG4gIC5sZWZ0LXBhbmVsIHtcbiAgICBoZWlnaHQ6IDQwdmg7IC8qIEZpeGVkIGhlaWdodCBvbiBtb2JpbGUgKi9cbiAgICBwYWRkaW5nOiAwO1xuICB9XG5cbiAgLmxvZ28taW1hZ2UgaW1nIHtcbiAgICB3aWR0aDogMTAwJTtcbiAgICBoZWlnaHQ6IDEwMCU7XG4gICAgb2JqZWN0LWZpdDogY292ZXI7XG4gICAgb2JqZWN0LXBvc2l0aW9uOiBjZW50ZXI7XG4gIH1cbn1cblxuQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XG4gIC5tb2R1bGUtZ3JpZCB7XG4gICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiAxZnI7XG4gIH1cblxuICAubW9kdWxlLWNhcmQge1xuICAgIGFzcGVjdC1yYXRpbzogMSAvIDE7IC8qIE1haW50YWluIHNxdWFyZSBhc3BlY3QgcmF0aW8gKi9cbiAgfVxuXG4gIC8qIFJlbW92ZWQgbG9nby10ZXh0IHN0eWxlcyAqL1xufVxuXG5AbWVkaWEgKG1heC13aWR0aDogNTc2cHgpIHtcbiAgLmxlZnQtcGFuZWwge1xuICAgIHBhZGRpbmc6IDA7XG4gIH1cblxuICAucmlnaHQtcGFuZWwge1xuICAgIHBhZGRpbmc6IDEuNXJlbSAxcmVtO1xuICB9XG5cbiAgLmZvb3Rlci1saW5rcyB7XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgIGFsaWduLWl0ZW1zOiBmbGV4LWVuZDtcbiAgICBnYXA6IDAuNXJlbTtcbiAgfVxufVxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵlistener", "ModuleComponent_div_7_Template_div_click_0_listener", "restoredCtx", "ɵɵrestoreView", "_r3", "module_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "onModuleSelect", "name", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵstyleProp", "color", "ɵɵtextInterpolate", "icon", "replace", "ModuleComponent", "constructor", "router", "selectionService", "modules", "ngOnInit", "moduleName", "alert", "setSelectedModule", "navigate", "ɵɵdirectiveInject", "i1", "Router", "i2", "SelectionService", "selectors", "decls", "vars", "consts", "template", "ModuleComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵtemplate", "ModuleComponent_div_7_Template", "ɵɵproperty"], "sources": ["C:\\cal_wf\\Augment\\4-img\\apqr400bl\\src\\app\\module\\module.component.ts", "C:\\cal_wf\\Augment\\4-img\\apqr400bl\\src\\app\\module\\module.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { SelectionService } from '../shared/selection.service';\n\n@Component({\n  selector: 'app-module',\n  templateUrl: './module.component.html',\n  styleUrls: ['./module.component.scss']\n})\nexport class ModuleComponent implements OnInit {\n  modules = [\n    {\n      name: 'Caliber aPQR',\n      icon: 'analytics',\n      color: '#00a99d'\n    },\n    {\n      name: 'Caliber CPV',\n      icon: 'description',\n      color: '#00a99d'\n    },\n    {\n      name: 'Caliber Metrix',\n      icon: 'bubble_chart',\n      color: '#00a99d'\n    },\n    {\n      name: 'Caliber Pulse',\n      icon: 'trending_up',\n      color: '#00a99d'\n    }\n  ];\n\n  constructor(\n    private router: Router,\n    private selectionService: SelectionService\n  ) { }\n\n  ngOnInit(): void {\n  }\n\n  onModuleSelect(moduleName: string): void {\n    alert(`You selected ${moduleName}`);\n    this.selectionService.setSelectedModule(moduleName);\n    this.router.navigate(['/plantselection']);\n  }\n}\n", "<div class=\"module-selection-container\">\n  <div class=\"left-panel\">\n    <div class=\"quari-logo\">\n      <div class=\"logo-image\">\n        <img src=\"assets/quariDark.png\" alt=\"QUARI Logo\">\n      </div>\n    </div>\n  </div>\n  <div class=\"right-panel\">\n    <div class=\"module-grid\">\n      <div class=\"module-card\" *ngFor=\"let module of modules\" (click)=\"onModuleSelect(module.name)\">\n        <div class=\"card-content\">\n          <div class=\"icon-container\">\n            <mat-icon [style.color]=\"module.color\">{{module.icon}}</mat-icon>\n          </div>\n          <div class=\"module-name\">\n            <span class=\"caliber\">Caliber</span> <span class=\"product-name\">{{module.name.replace('Caliber ', '')}}</span>\n          </div>\n        </div>\n      </div>\n    </div>\n    <div class=\"footer\">\n      <div class=\"footer-links\">\n        <a href=\"#\">Contact Admin</a> |\n        <a href=\"#\"><mat-icon class=\"language-icon\">language</mat-icon> Choose Language</a>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;;;;;;;ICUMA,EAAA,CAAAC,cAAA,cAA8F;IAAtCD,EAAA,CAAAE,UAAA,mBAAAC,oDAAA;MAAA,MAAAC,WAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,SAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAG,cAAA,CAAAL,SAAA,CAAAM,IAAA,CAA2B;IAAA,EAAC;IAC3Fb,EAAA,CAAAC,cAAA,cAA0B;IAEiBD,EAAA,CAAAc,MAAA,GAAe;IAAAd,EAAA,CAAAe,YAAA,EAAW;IAEnEf,EAAA,CAAAC,cAAA,cAAyB;IACDD,EAAA,CAAAc,MAAA,cAAO;IAAAd,EAAA,CAAAe,YAAA,EAAO;IAACf,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAc,MAAA,GAAuC;IAAAd,EAAA,CAAAe,YAAA,EAAO;;;;IAHpGf,EAAA,CAAAgB,SAAA,GAA4B;IAA5BhB,EAAA,CAAAiB,WAAA,UAAAV,SAAA,CAAAW,KAAA,CAA4B;IAAClB,EAAA,CAAAgB,SAAA,GAAe;IAAfhB,EAAA,CAAAmB,iBAAA,CAAAZ,SAAA,CAAAa,IAAA,CAAe;IAGUpB,EAAA,CAAAgB,SAAA,GAAuC;IAAvChB,EAAA,CAAAmB,iBAAA,CAAAZ,SAAA,CAAAM,IAAA,CAAAQ,OAAA,iBAAuC;;;ADPnH,OAAM,MAAOC,eAAe;EAwB1BC,YACUC,MAAc,EACdC,gBAAkC;IADlC,KAAAD,MAAM,GAANA,MAAM;IACN,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAzB1B,KAAAC,OAAO,GAAG,CACR;MACEb,IAAI,EAAE,cAAc;MACpBO,IAAI,EAAE,WAAW;MACjBF,KAAK,EAAE;KACR,EACD;MACEL,IAAI,EAAE,aAAa;MACnBO,IAAI,EAAE,aAAa;MACnBF,KAAK,EAAE;KACR,EACD;MACEL,IAAI,EAAE,gBAAgB;MACtBO,IAAI,EAAE,cAAc;MACpBF,KAAK,EAAE;KACR,EACD;MACEL,IAAI,EAAE,eAAe;MACrBO,IAAI,EAAE,aAAa;MACnBF,KAAK,EAAE;KACR,CACF;EAKG;EAEJS,QAAQA,CAAA,GACR;EAEAf,cAAcA,CAACgB,UAAkB;IAC/BC,KAAK,CAAC,gBAAgBD,UAAU,EAAE,CAAC;IACnC,IAAI,CAACH,gBAAgB,CAACK,iBAAiB,CAACF,UAAU,CAAC;IACnD,IAAI,CAACJ,MAAM,CAACO,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;EAC3C;;;uBApCWT,eAAe,EAAAtB,EAAA,CAAAgC,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAlC,EAAA,CAAAgC,iBAAA,CAAAG,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAAfd,eAAe;MAAAe,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCT5B3C,EAAA,CAAAC,cAAA,aAAwC;UAIhCD,EAAA,CAAA6C,SAAA,aAAiD;UACnD7C,EAAA,CAAAe,YAAA,EAAM;UAGVf,EAAA,CAAAC,cAAA,aAAyB;UAErBD,EAAA,CAAA8C,UAAA,IAAAC,8BAAA,kBASM;UACR/C,EAAA,CAAAe,YAAA,EAAM;UACNf,EAAA,CAAAC,cAAA,aAAoB;UAEJD,EAAA,CAAAc,MAAA,qBAAa;UAAAd,EAAA,CAAAe,YAAA,EAAI;UAACf,EAAA,CAAAc,MAAA,WAC9B;UAAAd,EAAA,CAAAC,cAAA,aAAY;UAAgCD,EAAA,CAAAc,MAAA,gBAAQ;UAAAd,EAAA,CAAAe,YAAA,EAAW;UAACf,EAAA,CAAAc,MAAA,wBAAe;UAAAd,EAAA,CAAAe,YAAA,EAAI;;;UAdzCf,EAAA,CAAAgB,SAAA,GAAU;UAAVhB,EAAA,CAAAgD,UAAA,YAAAJ,GAAA,CAAAlB,OAAA,CAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}