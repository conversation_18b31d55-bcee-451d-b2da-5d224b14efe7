import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { SelectionService } from '../shared/selection.service';

@Component({
  selector: 'app-plantselection',
  templateUrl: './plantselection.component.html',
  styleUrls: ['./plantselection.component.scss']
})
export class PlantselectionComponent implements OnInit {
  plants = [
    { name: 'CaliberPlant-1' },
    { name: 'CaliberPlant-2' },
    { name: 'CaliberPlant-3' },
    { name: 'CaliberPlant-4' },
    { name: 'CaliberPlant-5' }
  ];

  constructor(
    private router: Router,
    private selectionService: SelectionService
  ) { }

  ngOnInit(): void {
  }

  onPlantSelect(plantName: string): void {
    alert(`You selected ${plantName}`);
    this.selectionService.setSelectedPlant(plantName);
    this.router.navigate(['/layout']);
  }
}
