{"ast": null, "code": "/**\n * @license Angular v16.2.12\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { ɵglobal, ɵRuntimeError, Injectable, InjectionToken, Inject, APP_ID, CSP_NONCE, PLATFORM_ID, Optional, ViewEncapsulation, RendererStyleFlags2, ɵinternalCreateApplication, ErrorHandler, ɵsetDocument, PLATFORM_INITIALIZER, createPlatformFactory, platformCore, ɵTESTABILITY_GETTER, ɵTESTABILITY, Testability, NgZone, TestabilityRegistry, ɵINJECTOR_SCOPE, RendererFactory2, ApplicationModule, NgModule, SkipSelf, ɵɵinject, ApplicationRef, ɵConsole, forwardRef, ɵXSS_SECURITY_URL, SecurityContext, ɵallowSanitizationBypassAndThrow, ɵunwrapSafeValue, ɵ_sanitizeUrl, ɵ_sanitizeHtml, ɵbypassSanitizationTrustHtml, ɵbypassSanitizationTrustStyle, ɵbypassSanitizationTrustScript, ɵbypassSanitizationTrustUrl, ɵbypassSanitizationTrustResourceUrl, Injector, ENVIRONMENT_INITIALIZER, inject, ɵformatRuntimeError, makeEnvironmentProviders, ɵwithDomHydration, Version, makeStateKey as makeStateKey$1, TransferState as TransferState$1 } from '@angular/core';\nimport { ɵDomAdapter, ɵsetRootDomAdapter, ɵparseCookieValue, ɵgetDOM, isPlatformServer, DOCUMENT, ɵPLATFORM_BROWSER_ID, XhrFactory, CommonModule } from '@angular/common';\nexport { ɵgetDOM } from '@angular/common';\nimport { ɵwithHttpTransferCache } from '@angular/common/http';\n\n/**\n * Provides DOM operations in any browser environment.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\nclass GenericBrowserDomAdapter extends ɵDomAdapter {\n  constructor() {\n    super(...arguments);\n    this.supportsDOMEvents = true;\n  }\n}\n\n/**\n * A `DomAdapter` powered by full browser DOM APIs.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\n/* tslint:disable:requireParameterType no-console */\nclass BrowserDomAdapter extends GenericBrowserDomAdapter {\n  static makeCurrent() {\n    ɵsetRootDomAdapter(new BrowserDomAdapter());\n  }\n  onAndCancel(el, evt, listener) {\n    el.addEventListener(evt, listener);\n    return () => {\n      el.removeEventListener(evt, listener);\n    };\n  }\n  dispatchEvent(el, evt) {\n    el.dispatchEvent(evt);\n  }\n  remove(node) {\n    if (node.parentNode) {\n      node.parentNode.removeChild(node);\n    }\n  }\n  createElement(tagName, doc) {\n    doc = doc || this.getDefaultDocument();\n    return doc.createElement(tagName);\n  }\n  createHtmlDocument() {\n    return document.implementation.createHTMLDocument('fakeTitle');\n  }\n  getDefaultDocument() {\n    return document;\n  }\n  isElementNode(node) {\n    return node.nodeType === Node.ELEMENT_NODE;\n  }\n  isShadowRoot(node) {\n    return node instanceof DocumentFragment;\n  }\n  /** @deprecated No longer being used in Ivy code. To be removed in version 14. */\n  getGlobalEventTarget(doc, target) {\n    if (target === 'window') {\n      return window;\n    }\n    if (target === 'document') {\n      return doc;\n    }\n    if (target === 'body') {\n      return doc.body;\n    }\n    return null;\n  }\n  getBaseHref(doc) {\n    const href = getBaseElementHref();\n    return href == null ? null : relativePath(href);\n  }\n  resetBaseElement() {\n    baseElement = null;\n  }\n  getUserAgent() {\n    return window.navigator.userAgent;\n  }\n  getCookie(name) {\n    return ɵparseCookieValue(document.cookie, name);\n  }\n}\nlet baseElement = null;\nfunction getBaseElementHref() {\n  baseElement = baseElement || document.querySelector('base');\n  return baseElement ? baseElement.getAttribute('href') : null;\n}\n// based on urlUtils.js in AngularJS 1\nlet urlParsingNode;\nfunction relativePath(url) {\n  urlParsingNode = urlParsingNode || document.createElement('a');\n  urlParsingNode.setAttribute('href', url);\n  const pathName = urlParsingNode.pathname;\n  return pathName.charAt(0) === '/' ? pathName : `/${pathName}`;\n}\nclass BrowserGetTestability {\n  addToWindow(registry) {\n    ɵglobal['getAngularTestability'] = (elem, findInAncestors = true) => {\n      const testability = registry.findTestabilityInTree(elem, findInAncestors);\n      if (testability == null) {\n        throw new ɵRuntimeError(5103 /* RuntimeErrorCode.TESTABILITY_NOT_FOUND */, (typeof ngDevMode === 'undefined' || ngDevMode) && 'Could not find testability for element.');\n      }\n      return testability;\n    };\n    ɵglobal['getAllAngularTestabilities'] = () => registry.getAllTestabilities();\n    ɵglobal['getAllAngularRootElements'] = () => registry.getAllRootElements();\n    const whenAllStable = callback => {\n      const testabilities = ɵglobal['getAllAngularTestabilities']();\n      let count = testabilities.length;\n      let didWork = false;\n      const decrement = function (didWork_) {\n        didWork = didWork || didWork_;\n        count--;\n        if (count == 0) {\n          callback(didWork);\n        }\n      };\n      testabilities.forEach(testability => {\n        testability.whenStable(decrement);\n      });\n    };\n    if (!ɵglobal['frameworkStabilizers']) {\n      ɵglobal['frameworkStabilizers'] = [];\n    }\n    ɵglobal['frameworkStabilizers'].push(whenAllStable);\n  }\n  findTestabilityInTree(registry, elem, findInAncestors) {\n    if (elem == null) {\n      return null;\n    }\n    const t = registry.getTestability(elem);\n    if (t != null) {\n      return t;\n    } else if (!findInAncestors) {\n      return null;\n    }\n    if (ɵgetDOM().isShadowRoot(elem)) {\n      return this.findTestabilityInTree(registry, elem.host, true);\n    }\n    return this.findTestabilityInTree(registry, elem.parentElement, true);\n  }\n}\n\n/**\n * A factory for `HttpXhrBackend` that uses the `XMLHttpRequest` browser API.\n */\nclass BrowserXhr {\n  build() {\n    return new XMLHttpRequest();\n  }\n  static {\n    this.ɵfac = function BrowserXhr_Factory(t) {\n      return new (t || BrowserXhr)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: BrowserXhr,\n      factory: BrowserXhr.ɵfac\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserXhr, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/**\n * The injection token for the event-manager plug-in service.\n *\n * @publicApi\n */\nconst EVENT_MANAGER_PLUGINS = new InjectionToken('EventManagerPlugins');\n/**\n * An injectable service that provides event management for Angular\n * through a browser plug-in.\n *\n * @publicApi\n */\nclass EventManager {\n  /**\n   * Initializes an instance of the event-manager service.\n   */\n  constructor(plugins, _zone) {\n    this._zone = _zone;\n    this._eventNameToPlugin = new Map();\n    plugins.forEach(plugin => {\n      plugin.manager = this;\n    });\n    this._plugins = plugins.slice().reverse();\n  }\n  /**\n   * Registers a handler for a specific element and event.\n   *\n   * @param element The HTML element to receive event notifications.\n   * @param eventName The name of the event to listen for.\n   * @param handler A function to call when the notification occurs. Receives the\n   * event object as an argument.\n   * @returns  A callback function that can be used to remove the handler.\n   */\n  addEventListener(element, eventName, handler) {\n    const plugin = this._findPluginFor(eventName);\n    return plugin.addEventListener(element, eventName, handler);\n  }\n  /**\n   * Retrieves the compilation zone in which event listeners are registered.\n   */\n  getZone() {\n    return this._zone;\n  }\n  /** @internal */\n  _findPluginFor(eventName) {\n    let plugin = this._eventNameToPlugin.get(eventName);\n    if (plugin) {\n      return plugin;\n    }\n    const plugins = this._plugins;\n    plugin = plugins.find(plugin => plugin.supports(eventName));\n    if (!plugin) {\n      throw new ɵRuntimeError(5101 /* RuntimeErrorCode.NO_PLUGIN_FOR_EVENT */, (typeof ngDevMode === 'undefined' || ngDevMode) && `No event manager plugin found for event ${eventName}`);\n    }\n    this._eventNameToPlugin.set(eventName, plugin);\n    return plugin;\n  }\n  static {\n    this.ɵfac = function EventManager_Factory(t) {\n      return new (t || EventManager)(i0.ɵɵinject(EVENT_MANAGER_PLUGINS), i0.ɵɵinject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: EventManager,\n      factory: EventManager.ɵfac\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(EventManager, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [EVENT_MANAGER_PLUGINS]\n      }]\n    }, {\n      type: i0.NgZone\n    }];\n  }, null);\n})();\nclass EventManagerPlugin {\n  constructor(_doc) {\n    this._doc = _doc;\n  }\n}\n\n/** The style elements attribute name used to set value of `APP_ID` token. */\nconst APP_ID_ATTRIBUTE_NAME = 'ng-app-id';\nclass SharedStylesHost {\n  constructor(doc, appId, nonce, platformId = {}) {\n    this.doc = doc;\n    this.appId = appId;\n    this.nonce = nonce;\n    this.platformId = platformId;\n    // Maps all registered host nodes to a list of style nodes that have been added to the host node.\n    this.styleRef = new Map();\n    this.hostNodes = new Set();\n    this.styleNodesInDOM = this.collectServerRenderedStyles();\n    this.platformIsServer = isPlatformServer(platformId);\n    this.resetHostNodes();\n  }\n  addStyles(styles) {\n    for (const style of styles) {\n      const usageCount = this.changeUsageCount(style, 1);\n      if (usageCount === 1) {\n        this.onStyleAdded(style);\n      }\n    }\n  }\n  removeStyles(styles) {\n    for (const style of styles) {\n      const usageCount = this.changeUsageCount(style, -1);\n      if (usageCount <= 0) {\n        this.onStyleRemoved(style);\n      }\n    }\n  }\n  ngOnDestroy() {\n    const styleNodesInDOM = this.styleNodesInDOM;\n    if (styleNodesInDOM) {\n      styleNodesInDOM.forEach(node => node.remove());\n      styleNodesInDOM.clear();\n    }\n    for (const style of this.getAllStyles()) {\n      this.onStyleRemoved(style);\n    }\n    this.resetHostNodes();\n  }\n  addHost(hostNode) {\n    this.hostNodes.add(hostNode);\n    for (const style of this.getAllStyles()) {\n      this.addStyleToHost(hostNode, style);\n    }\n  }\n  removeHost(hostNode) {\n    this.hostNodes.delete(hostNode);\n  }\n  getAllStyles() {\n    return this.styleRef.keys();\n  }\n  onStyleAdded(style) {\n    for (const host of this.hostNodes) {\n      this.addStyleToHost(host, style);\n    }\n  }\n  onStyleRemoved(style) {\n    const styleRef = this.styleRef;\n    styleRef.get(style)?.elements?.forEach(node => node.remove());\n    styleRef.delete(style);\n  }\n  collectServerRenderedStyles() {\n    const styles = this.doc.head?.querySelectorAll(`style[${APP_ID_ATTRIBUTE_NAME}=\"${this.appId}\"]`);\n    if (styles?.length) {\n      const styleMap = new Map();\n      styles.forEach(style => {\n        if (style.textContent != null) {\n          styleMap.set(style.textContent, style);\n        }\n      });\n      return styleMap;\n    }\n    return null;\n  }\n  changeUsageCount(style, delta) {\n    const map = this.styleRef;\n    if (map.has(style)) {\n      const styleRefValue = map.get(style);\n      styleRefValue.usage += delta;\n      return styleRefValue.usage;\n    }\n    map.set(style, {\n      usage: delta,\n      elements: []\n    });\n    return delta;\n  }\n  getStyleElement(host, style) {\n    const styleNodesInDOM = this.styleNodesInDOM;\n    const styleEl = styleNodesInDOM?.get(style);\n    if (styleEl?.parentNode === host) {\n      // `styleNodesInDOM` cannot be undefined due to the above `styleNodesInDOM?.get`.\n      styleNodesInDOM.delete(style);\n      styleEl.removeAttribute(APP_ID_ATTRIBUTE_NAME);\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        // This attribute is solely used for debugging purposes.\n        styleEl.setAttribute('ng-style-reused', '');\n      }\n      return styleEl;\n    } else {\n      const styleEl = this.doc.createElement('style');\n      if (this.nonce) {\n        styleEl.setAttribute('nonce', this.nonce);\n      }\n      styleEl.textContent = style;\n      if (this.platformIsServer) {\n        styleEl.setAttribute(APP_ID_ATTRIBUTE_NAME, this.appId);\n      }\n      return styleEl;\n    }\n  }\n  addStyleToHost(host, style) {\n    const styleEl = this.getStyleElement(host, style);\n    host.appendChild(styleEl);\n    const styleRef = this.styleRef;\n    const styleElRef = styleRef.get(style)?.elements;\n    if (styleElRef) {\n      styleElRef.push(styleEl);\n    } else {\n      styleRef.set(style, {\n        elements: [styleEl],\n        usage: 1\n      });\n    }\n  }\n  resetHostNodes() {\n    const hostNodes = this.hostNodes;\n    hostNodes.clear();\n    // Re-add the head element back since this is the default host.\n    hostNodes.add(this.doc.head);\n  }\n  static {\n    this.ɵfac = function SharedStylesHost_Factory(t) {\n      return new (t || SharedStylesHost)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(APP_ID), i0.ɵɵinject(CSP_NONCE, 8), i0.ɵɵinject(PLATFORM_ID));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: SharedStylesHost,\n      factory: SharedStylesHost.ɵfac\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SharedStylesHost, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: Document,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [APP_ID]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [CSP_NONCE]\n      }, {\n        type: Optional\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [PLATFORM_ID]\n      }]\n    }];\n  }, null);\n})();\nconst NAMESPACE_URIS = {\n  'svg': 'http://www.w3.org/2000/svg',\n  'xhtml': 'http://www.w3.org/1999/xhtml',\n  'xlink': 'http://www.w3.org/1999/xlink',\n  'xml': 'http://www.w3.org/XML/1998/namespace',\n  'xmlns': 'http://www.w3.org/2000/xmlns/',\n  'math': 'http://www.w3.org/1998/MathML/'\n};\nconst COMPONENT_REGEX = /%COMP%/g;\nconst COMPONENT_VARIABLE = '%COMP%';\nconst HOST_ATTR = `_nghost-${COMPONENT_VARIABLE}`;\nconst CONTENT_ATTR = `_ngcontent-${COMPONENT_VARIABLE}`;\n/**\n * The default value for the `REMOVE_STYLES_ON_COMPONENT_DESTROY` DI token.\n */\nconst REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT = false;\n/**\n * A [DI token](guide/glossary#di-token \"DI token definition\") that indicates whether styles\n * of destroyed components should be removed from DOM.\n *\n * By default, the value is set to `false`. This will be changed in the next major version.\n * @publicApi\n */\nconst REMOVE_STYLES_ON_COMPONENT_DESTROY = new InjectionToken('RemoveStylesOnCompDestroy', {\n  providedIn: 'root',\n  factory: () => REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT\n});\nfunction shimContentAttribute(componentShortId) {\n  return CONTENT_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\nfunction shimHostAttribute(componentShortId) {\n  return HOST_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\nfunction shimStylesContent(compId, styles) {\n  return styles.map(s => s.replace(COMPONENT_REGEX, compId));\n}\nclass DomRendererFactory2 {\n  constructor(eventManager, sharedStylesHost, appId, removeStylesOnCompDestroy, doc, platformId, ngZone, nonce = null) {\n    this.eventManager = eventManager;\n    this.sharedStylesHost = sharedStylesHost;\n    this.appId = appId;\n    this.removeStylesOnCompDestroy = removeStylesOnCompDestroy;\n    this.doc = doc;\n    this.platformId = platformId;\n    this.ngZone = ngZone;\n    this.nonce = nonce;\n    this.rendererByCompId = new Map();\n    this.platformIsServer = isPlatformServer(platformId);\n    this.defaultRenderer = new DefaultDomRenderer2(eventManager, doc, ngZone, this.platformIsServer);\n  }\n  createRenderer(element, type) {\n    if (!element || !type) {\n      return this.defaultRenderer;\n    }\n    if (this.platformIsServer && type.encapsulation === ViewEncapsulation.ShadowDom) {\n      // Domino does not support shadow DOM.\n      type = {\n        ...type,\n        encapsulation: ViewEncapsulation.Emulated\n      };\n    }\n    const renderer = this.getOrCreateRenderer(element, type);\n    // Renderers have different logic due to different encapsulation behaviours.\n    // Ex: for emulated, an attribute is added to the element.\n    if (renderer instanceof EmulatedEncapsulationDomRenderer2) {\n      renderer.applyToHost(element);\n    } else if (renderer instanceof NoneEncapsulationDomRenderer) {\n      renderer.applyStyles();\n    }\n    return renderer;\n  }\n  getOrCreateRenderer(element, type) {\n    const rendererByCompId = this.rendererByCompId;\n    let renderer = rendererByCompId.get(type.id);\n    if (!renderer) {\n      const doc = this.doc;\n      const ngZone = this.ngZone;\n      const eventManager = this.eventManager;\n      const sharedStylesHost = this.sharedStylesHost;\n      const removeStylesOnCompDestroy = this.removeStylesOnCompDestroy;\n      const platformIsServer = this.platformIsServer;\n      switch (type.encapsulation) {\n        case ViewEncapsulation.Emulated:\n          renderer = new EmulatedEncapsulationDomRenderer2(eventManager, sharedStylesHost, type, this.appId, removeStylesOnCompDestroy, doc, ngZone, platformIsServer);\n          break;\n        case ViewEncapsulation.ShadowDom:\n          return new ShadowDomRenderer(eventManager, sharedStylesHost, element, type, doc, ngZone, this.nonce, platformIsServer);\n        default:\n          renderer = new NoneEncapsulationDomRenderer(eventManager, sharedStylesHost, type, removeStylesOnCompDestroy, doc, ngZone, platformIsServer);\n          break;\n      }\n      rendererByCompId.set(type.id, renderer);\n    }\n    return renderer;\n  }\n  ngOnDestroy() {\n    this.rendererByCompId.clear();\n  }\n  static {\n    this.ɵfac = function DomRendererFactory2_Factory(t) {\n      return new (t || DomRendererFactory2)(i0.ɵɵinject(EventManager), i0.ɵɵinject(SharedStylesHost), i0.ɵɵinject(APP_ID), i0.ɵɵinject(REMOVE_STYLES_ON_COMPONENT_DESTROY), i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(PLATFORM_ID), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(CSP_NONCE));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: DomRendererFactory2,\n      factory: DomRendererFactory2.ɵfac\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomRendererFactory2, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: EventManager\n    }, {\n      type: SharedStylesHost\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [APP_ID]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [REMOVE_STYLES_ON_COMPONENT_DESTROY]\n      }]\n    }, {\n      type: Document,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: Object,\n      decorators: [{\n        type: Inject,\n        args: [PLATFORM_ID]\n      }]\n    }, {\n      type: i0.NgZone\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [CSP_NONCE]\n      }]\n    }];\n  }, null);\n})();\nclass DefaultDomRenderer2 {\n  constructor(eventManager, doc, ngZone, platformIsServer) {\n    this.eventManager = eventManager;\n    this.doc = doc;\n    this.ngZone = ngZone;\n    this.platformIsServer = platformIsServer;\n    this.data = Object.create(null);\n    this.destroyNode = null;\n  }\n  destroy() {}\n  createElement(name, namespace) {\n    if (namespace) {\n      // TODO: `|| namespace` was added in\n      // https://github.com/angular/angular/commit/2b9cc8503d48173492c29f5a271b61126104fbdb to\n      // support how Ivy passed around the namespace URI rather than short name at the time. It did\n      // not, however extend the support to other parts of the system (setAttribute, setAttribute,\n      // and the ServerRenderer). We should decide what exactly the semantics for dealing with\n      // namespaces should be and make it consistent.\n      // Related issues:\n      // https://github.com/angular/angular/issues/44028\n      // https://github.com/angular/angular/issues/44883\n      return this.doc.createElementNS(NAMESPACE_URIS[namespace] || namespace, name);\n    }\n    return this.doc.createElement(name);\n  }\n  createComment(value) {\n    return this.doc.createComment(value);\n  }\n  createText(value) {\n    return this.doc.createTextNode(value);\n  }\n  appendChild(parent, newChild) {\n    const targetParent = isTemplateNode(parent) ? parent.content : parent;\n    targetParent.appendChild(newChild);\n  }\n  insertBefore(parent, newChild, refChild) {\n    if (parent) {\n      const targetParent = isTemplateNode(parent) ? parent.content : parent;\n      targetParent.insertBefore(newChild, refChild);\n    }\n  }\n  removeChild(parent, oldChild) {\n    if (parent) {\n      parent.removeChild(oldChild);\n    }\n  }\n  selectRootElement(selectorOrNode, preserveContent) {\n    let el = typeof selectorOrNode === 'string' ? this.doc.querySelector(selectorOrNode) : selectorOrNode;\n    if (!el) {\n      throw new ɵRuntimeError(-5104 /* RuntimeErrorCode.ROOT_NODE_NOT_FOUND */, (typeof ngDevMode === 'undefined' || ngDevMode) && `The selector \"${selectorOrNode}\" did not match any elements`);\n    }\n    if (!preserveContent) {\n      el.textContent = '';\n    }\n    return el;\n  }\n  parentNode(node) {\n    return node.parentNode;\n  }\n  nextSibling(node) {\n    return node.nextSibling;\n  }\n  setAttribute(el, name, value, namespace) {\n    if (namespace) {\n      name = namespace + ':' + name;\n      const namespaceUri = NAMESPACE_URIS[namespace];\n      if (namespaceUri) {\n        el.setAttributeNS(namespaceUri, name, value);\n      } else {\n        el.setAttribute(name, value);\n      }\n    } else {\n      el.setAttribute(name, value);\n    }\n  }\n  removeAttribute(el, name, namespace) {\n    if (namespace) {\n      const namespaceUri = NAMESPACE_URIS[namespace];\n      if (namespaceUri) {\n        el.removeAttributeNS(namespaceUri, name);\n      } else {\n        el.removeAttribute(`${namespace}:${name}`);\n      }\n    } else {\n      el.removeAttribute(name);\n    }\n  }\n  addClass(el, name) {\n    el.classList.add(name);\n  }\n  removeClass(el, name) {\n    el.classList.remove(name);\n  }\n  setStyle(el, style, value, flags) {\n    if (flags & (RendererStyleFlags2.DashCase | RendererStyleFlags2.Important)) {\n      el.style.setProperty(style, value, flags & RendererStyleFlags2.Important ? 'important' : '');\n    } else {\n      el.style[style] = value;\n    }\n  }\n  removeStyle(el, style, flags) {\n    if (flags & RendererStyleFlags2.DashCase) {\n      // removeProperty has no effect when used on camelCased properties.\n      el.style.removeProperty(style);\n    } else {\n      el.style[style] = '';\n    }\n  }\n  setProperty(el, name, value) {\n    (typeof ngDevMode === 'undefined' || ngDevMode) && checkNoSyntheticProp(name, 'property');\n    el[name] = value;\n  }\n  setValue(node, value) {\n    node.nodeValue = value;\n  }\n  listen(target, event, callback) {\n    (typeof ngDevMode === 'undefined' || ngDevMode) && checkNoSyntheticProp(event, 'listener');\n    if (typeof target === 'string') {\n      target = ɵgetDOM().getGlobalEventTarget(this.doc, target);\n      if (!target) {\n        throw new Error(`Unsupported event target ${target} for event ${event}`);\n      }\n    }\n    return this.eventManager.addEventListener(target, event, this.decoratePreventDefault(callback));\n  }\n  decoratePreventDefault(eventHandler) {\n    // `DebugNode.triggerEventHandler` needs to know if the listener was created with\n    // decoratePreventDefault or is a listener added outside the Angular context so it can handle\n    // the two differently. In the first case, the special '__ngUnwrap__' token is passed to the\n    // unwrap the listener (see below).\n    return event => {\n      // Ivy uses '__ngUnwrap__' as a special token that allows us to unwrap the function\n      // so that it can be invoked programmatically by `DebugNode.triggerEventHandler`. The\n      // debug_node can inspect the listener toString contents for the existence of this special\n      // token. Because the token is a string literal, it is ensured to not be modified by compiled\n      // code.\n      if (event === '__ngUnwrap__') {\n        return eventHandler;\n      }\n      // Run the event handler inside the ngZone because event handlers are not patched\n      // by Zone on the server. This is required only for tests.\n      const allowDefaultBehavior = this.platformIsServer ? this.ngZone.runGuarded(() => eventHandler(event)) : eventHandler(event);\n      if (allowDefaultBehavior === false) {\n        event.preventDefault();\n      }\n      return undefined;\n    };\n  }\n}\nconst AT_CHARCODE = (() => '@'.charCodeAt(0))();\nfunction checkNoSyntheticProp(name, nameKind) {\n  if (name.charCodeAt(0) === AT_CHARCODE) {\n    throw new ɵRuntimeError(5105 /* RuntimeErrorCode.UNEXPECTED_SYNTHETIC_PROPERTY */, `Unexpected synthetic ${nameKind} ${name} found. Please make sure that:\n  - Either \\`BrowserAnimationsModule\\` or \\`NoopAnimationsModule\\` are imported in your application.\n  - There is corresponding configuration for the animation named \\`${name}\\` defined in the \\`animations\\` field of the \\`@Component\\` decorator (see https://angular.io/api/core/Component#animations).`);\n  }\n}\nfunction isTemplateNode(node) {\n  return node.tagName === 'TEMPLATE' && node.content !== undefined;\n}\nclass ShadowDomRenderer extends DefaultDomRenderer2 {\n  constructor(eventManager, sharedStylesHost, hostEl, component, doc, ngZone, nonce, platformIsServer) {\n    super(eventManager, doc, ngZone, platformIsServer);\n    this.sharedStylesHost = sharedStylesHost;\n    this.hostEl = hostEl;\n    this.shadowRoot = hostEl.attachShadow({\n      mode: 'open'\n    });\n    this.sharedStylesHost.addHost(this.shadowRoot);\n    const styles = shimStylesContent(component.id, component.styles);\n    for (const style of styles) {\n      const styleEl = document.createElement('style');\n      if (nonce) {\n        styleEl.setAttribute('nonce', nonce);\n      }\n      styleEl.textContent = style;\n      this.shadowRoot.appendChild(styleEl);\n    }\n  }\n  nodeOrShadowRoot(node) {\n    return node === this.hostEl ? this.shadowRoot : node;\n  }\n  appendChild(parent, newChild) {\n    return super.appendChild(this.nodeOrShadowRoot(parent), newChild);\n  }\n  insertBefore(parent, newChild, refChild) {\n    return super.insertBefore(this.nodeOrShadowRoot(parent), newChild, refChild);\n  }\n  removeChild(parent, oldChild) {\n    return super.removeChild(this.nodeOrShadowRoot(parent), oldChild);\n  }\n  parentNode(node) {\n    return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(node)));\n  }\n  destroy() {\n    this.sharedStylesHost.removeHost(this.shadowRoot);\n  }\n}\nclass NoneEncapsulationDomRenderer extends DefaultDomRenderer2 {\n  constructor(eventManager, sharedStylesHost, component, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, compId) {\n    super(eventManager, doc, ngZone, platformIsServer);\n    this.sharedStylesHost = sharedStylesHost;\n    this.removeStylesOnCompDestroy = removeStylesOnCompDestroy;\n    this.styles = compId ? shimStylesContent(compId, component.styles) : component.styles;\n  }\n  applyStyles() {\n    this.sharedStylesHost.addStyles(this.styles);\n  }\n  destroy() {\n    if (!this.removeStylesOnCompDestroy) {\n      return;\n    }\n    this.sharedStylesHost.removeStyles(this.styles);\n  }\n}\nclass EmulatedEncapsulationDomRenderer2 extends NoneEncapsulationDomRenderer {\n  constructor(eventManager, sharedStylesHost, component, appId, removeStylesOnCompDestroy, doc, ngZone, platformIsServer) {\n    const compId = appId + '-' + component.id;\n    super(eventManager, sharedStylesHost, component, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, compId);\n    this.contentAttr = shimContentAttribute(compId);\n    this.hostAttr = shimHostAttribute(compId);\n  }\n  applyToHost(element) {\n    this.applyStyles();\n    this.setAttribute(element, this.hostAttr, '');\n  }\n  createElement(parent, name) {\n    const el = super.createElement(parent, name);\n    super.setAttribute(el, this.contentAttr, '');\n    return el;\n  }\n}\nclass DomEventsPlugin extends EventManagerPlugin {\n  constructor(doc) {\n    super(doc);\n  }\n  // This plugin should come last in the list of plugins, because it accepts all\n  // events.\n  supports(eventName) {\n    return true;\n  }\n  addEventListener(element, eventName, handler) {\n    element.addEventListener(eventName, handler, false);\n    return () => this.removeEventListener(element, eventName, handler);\n  }\n  removeEventListener(target, eventName, callback) {\n    return target.removeEventListener(eventName, callback);\n  }\n  static {\n    this.ɵfac = function DomEventsPlugin_Factory(t) {\n      return new (t || DomEventsPlugin)(i0.ɵɵinject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: DomEventsPlugin,\n      factory: DomEventsPlugin.ɵfac\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomEventsPlugin, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n\n/**\n * Defines supported modifiers for key events.\n */\nconst MODIFIER_KEYS = ['alt', 'control', 'meta', 'shift'];\n// The following values are here for cross-browser compatibility and to match the W3C standard\n// cf https://www.w3.org/TR/DOM-Level-3-Events-key/\nconst _keyMap = {\n  '\\b': 'Backspace',\n  '\\t': 'Tab',\n  '\\x7F': 'Delete',\n  '\\x1B': 'Escape',\n  'Del': 'Delete',\n  'Esc': 'Escape',\n  'Left': 'ArrowLeft',\n  'Right': 'ArrowRight',\n  'Up': 'ArrowUp',\n  'Down': 'ArrowDown',\n  'Menu': 'ContextMenu',\n  'Scroll': 'ScrollLock',\n  'Win': 'OS'\n};\n/**\n * Retrieves modifiers from key-event objects.\n */\nconst MODIFIER_KEY_GETTERS = {\n  'alt': event => event.altKey,\n  'control': event => event.ctrlKey,\n  'meta': event => event.metaKey,\n  'shift': event => event.shiftKey\n};\n/**\n * @publicApi\n * A browser plug-in that provides support for handling of key events in Angular.\n */\nclass KeyEventsPlugin extends EventManagerPlugin {\n  /**\n   * Initializes an instance of the browser plug-in.\n   * @param doc The document in which key events will be detected.\n   */\n  constructor(doc) {\n    super(doc);\n  }\n  /**\n   * Reports whether a named key event is supported.\n   * @param eventName The event name to query.\n   * @return True if the named key event is supported.\n   */\n  supports(eventName) {\n    return KeyEventsPlugin.parseEventName(eventName) != null;\n  }\n  /**\n   * Registers a handler for a specific element and key event.\n   * @param element The HTML element to receive event notifications.\n   * @param eventName The name of the key event to listen for.\n   * @param handler A function to call when the notification occurs. Receives the\n   * event object as an argument.\n   * @returns The key event that was registered.\n   */\n  addEventListener(element, eventName, handler) {\n    const parsedEvent = KeyEventsPlugin.parseEventName(eventName);\n    const outsideHandler = KeyEventsPlugin.eventCallback(parsedEvent['fullKey'], handler, this.manager.getZone());\n    return this.manager.getZone().runOutsideAngular(() => {\n      return ɵgetDOM().onAndCancel(element, parsedEvent['domEventName'], outsideHandler);\n    });\n  }\n  /**\n   * Parses the user provided full keyboard event definition and normalizes it for\n   * later internal use. It ensures the string is all lowercase, converts special\n   * characters to a standard spelling, and orders all the values consistently.\n   *\n   * @param eventName The name of the key event to listen for.\n   * @returns an object with the full, normalized string, and the dom event name\n   * or null in the case when the event doesn't match a keyboard event.\n   */\n  static parseEventName(eventName) {\n    const parts = eventName.toLowerCase().split('.');\n    const domEventName = parts.shift();\n    if (parts.length === 0 || !(domEventName === 'keydown' || domEventName === 'keyup')) {\n      return null;\n    }\n    const key = KeyEventsPlugin._normalizeKey(parts.pop());\n    let fullKey = '';\n    let codeIX = parts.indexOf('code');\n    if (codeIX > -1) {\n      parts.splice(codeIX, 1);\n      fullKey = 'code.';\n    }\n    MODIFIER_KEYS.forEach(modifierName => {\n      const index = parts.indexOf(modifierName);\n      if (index > -1) {\n        parts.splice(index, 1);\n        fullKey += modifierName + '.';\n      }\n    });\n    fullKey += key;\n    if (parts.length != 0 || key.length === 0) {\n      // returning null instead of throwing to let another plugin process the event\n      return null;\n    }\n    // NOTE: Please don't rewrite this as so, as it will break JSCompiler property renaming.\n    //       The code must remain in the `result['domEventName']` form.\n    // return {domEventName, fullKey};\n    const result = {};\n    result['domEventName'] = domEventName;\n    result['fullKey'] = fullKey;\n    return result;\n  }\n  /**\n   * Determines whether the actual keys pressed match the configured key code string.\n   * The `fullKeyCode` event is normalized in the `parseEventName` method when the\n   * event is attached to the DOM during the `addEventListener` call. This is unseen\n   * by the end user and is normalized for internal consistency and parsing.\n   *\n   * @param event The keyboard event.\n   * @param fullKeyCode The normalized user defined expected key event string\n   * @returns boolean.\n   */\n  static matchEventFullKeyCode(event, fullKeyCode) {\n    let keycode = _keyMap[event.key] || event.key;\n    let key = '';\n    if (fullKeyCode.indexOf('code.') > -1) {\n      keycode = event.code;\n      key = 'code.';\n    }\n    // the keycode could be unidentified so we have to check here\n    if (keycode == null || !keycode) return false;\n    keycode = keycode.toLowerCase();\n    if (keycode === ' ') {\n      keycode = 'space'; // for readability\n    } else if (keycode === '.') {\n      keycode = 'dot'; // because '.' is used as a separator in event names\n    }\n\n    MODIFIER_KEYS.forEach(modifierName => {\n      if (modifierName !== keycode) {\n        const modifierGetter = MODIFIER_KEY_GETTERS[modifierName];\n        if (modifierGetter(event)) {\n          key += modifierName + '.';\n        }\n      }\n    });\n    key += keycode;\n    return key === fullKeyCode;\n  }\n  /**\n   * Configures a handler callback for a key event.\n   * @param fullKey The event name that combines all simultaneous keystrokes.\n   * @param handler The function that responds to the key event.\n   * @param zone The zone in which the event occurred.\n   * @returns A callback function.\n   */\n  static eventCallback(fullKey, handler, zone) {\n    return event => {\n      if (KeyEventsPlugin.matchEventFullKeyCode(event, fullKey)) {\n        zone.runGuarded(() => handler(event));\n      }\n    };\n  }\n  /** @internal */\n  static _normalizeKey(keyName) {\n    // TODO: switch to a Map if the mapping grows too much\n    switch (keyName) {\n      case 'esc':\n        return 'escape';\n      default:\n        return keyName;\n    }\n  }\n  static {\n    this.ɵfac = function KeyEventsPlugin_Factory(t) {\n      return new (t || KeyEventsPlugin)(i0.ɵɵinject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: KeyEventsPlugin,\n      factory: KeyEventsPlugin.ɵfac\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(KeyEventsPlugin, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n\n/**\n * Bootstraps an instance of an Angular application and renders a standalone component as the\n * application's root component. More information about standalone components can be found in [this\n * guide](guide/standalone-components).\n *\n * @usageNotes\n * The root component passed into this function *must* be a standalone one (should have the\n * `standalone: true` flag in the `@Component` decorator config).\n *\n * ```typescript\n * @Component({\n *   standalone: true,\n *   template: 'Hello world!'\n * })\n * class RootComponent {}\n *\n * const appRef: ApplicationRef = await bootstrapApplication(RootComponent);\n * ```\n *\n * You can add the list of providers that should be available in the application injector by\n * specifying the `providers` field in an object passed as the second argument:\n *\n * ```typescript\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     {provide: BACKEND_URL, useValue: 'https://yourdomain.com/api'}\n *   ]\n * });\n * ```\n *\n * The `importProvidersFrom` helper method can be used to collect all providers from any\n * existing NgModule (and transitively from all NgModules that it imports):\n *\n * ```typescript\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     importProvidersFrom(SomeNgModule)\n *   ]\n * });\n * ```\n *\n * Note: the `bootstrapApplication` method doesn't include [Testability](api/core/Testability) by\n * default. You can add [Testability](api/core/Testability) by getting the list of necessary\n * providers using `provideProtractorTestingSupport()` function and adding them into the `providers`\n * array, for example:\n *\n * ```typescript\n * import {provideProtractorTestingSupport} from '@angular/platform-browser';\n *\n * await bootstrapApplication(RootComponent, {providers: [provideProtractorTestingSupport()]});\n * ```\n *\n * @param rootComponent A reference to a standalone component that should be rendered.\n * @param options Extra configuration for the bootstrap operation, see `ApplicationConfig` for\n *     additional info.\n * @returns A promise that returns an `ApplicationRef` instance once resolved.\n *\n * @publicApi\n */\nfunction bootstrapApplication(rootComponent, options) {\n  return ɵinternalCreateApplication({\n    rootComponent,\n    ...createProvidersConfig(options)\n  });\n}\n/**\n * Create an instance of an Angular application without bootstrapping any components. This is useful\n * for the situation where one wants to decouple application environment creation (a platform and\n * associated injectors) from rendering components on a screen. Components can be subsequently\n * bootstrapped on the returned `ApplicationRef`.\n *\n * @param options Extra configuration for the application environment, see `ApplicationConfig` for\n *     additional info.\n * @returns A promise that returns an `ApplicationRef` instance once resolved.\n *\n * @publicApi\n */\nfunction createApplication(options) {\n  return ɵinternalCreateApplication(createProvidersConfig(options));\n}\nfunction createProvidersConfig(options) {\n  return {\n    appProviders: [...BROWSER_MODULE_PROVIDERS, ...(options?.providers ?? [])],\n    platformProviders: INTERNAL_BROWSER_PLATFORM_PROVIDERS\n  };\n}\n/**\n * Returns a set of providers required to setup [Testability](api/core/Testability) for an\n * application bootstrapped using the `bootstrapApplication` function. The set of providers is\n * needed to support testing an application with Protractor (which relies on the Testability APIs\n * to be present).\n *\n * @returns An array of providers required to setup Testability for an application and make it\n *     available for testing using Protractor.\n *\n * @publicApi\n */\nfunction provideProtractorTestingSupport() {\n  // Return a copy to prevent changes to the original array in case any in-place\n  // alterations are performed to the `provideProtractorTestingSupport` call results in app\n  // code.\n  return [...TESTABILITY_PROVIDERS];\n}\nfunction initDomAdapter() {\n  BrowserDomAdapter.makeCurrent();\n}\nfunction errorHandler() {\n  return new ErrorHandler();\n}\nfunction _document() {\n  // Tell ivy about the global document\n  ɵsetDocument(document);\n  return document;\n}\nconst INTERNAL_BROWSER_PLATFORM_PROVIDERS = [{\n  provide: PLATFORM_ID,\n  useValue: ɵPLATFORM_BROWSER_ID\n}, {\n  provide: PLATFORM_INITIALIZER,\n  useValue: initDomAdapter,\n  multi: true\n}, {\n  provide: DOCUMENT,\n  useFactory: _document,\n  deps: []\n}];\n/**\n * A factory function that returns a `PlatformRef` instance associated with browser service\n * providers.\n *\n * @publicApi\n */\nconst platformBrowser = createPlatformFactory(platformCore, 'browser', INTERNAL_BROWSER_PLATFORM_PROVIDERS);\n/**\n * Internal marker to signal whether providers from the `BrowserModule` are already present in DI.\n * This is needed to avoid loading `BrowserModule` providers twice. We can't rely on the\n * `BrowserModule` presence itself, since the standalone-based bootstrap just imports\n * `BrowserModule` providers without referencing the module itself.\n */\nconst BROWSER_MODULE_PROVIDERS_MARKER = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'BrowserModule Providers Marker' : '');\nconst TESTABILITY_PROVIDERS = [{\n  provide: ɵTESTABILITY_GETTER,\n  useClass: BrowserGetTestability,\n  deps: []\n}, {\n  provide: ɵTESTABILITY,\n  useClass: Testability,\n  deps: [NgZone, TestabilityRegistry, ɵTESTABILITY_GETTER]\n}, {\n  provide: Testability,\n  useClass: Testability,\n  deps: [NgZone, TestabilityRegistry, ɵTESTABILITY_GETTER]\n}];\nconst BROWSER_MODULE_PROVIDERS = [{\n  provide: ɵINJECTOR_SCOPE,\n  useValue: 'root'\n}, {\n  provide: ErrorHandler,\n  useFactory: errorHandler,\n  deps: []\n}, {\n  provide: EVENT_MANAGER_PLUGINS,\n  useClass: DomEventsPlugin,\n  multi: true,\n  deps: [DOCUMENT, NgZone, PLATFORM_ID]\n}, {\n  provide: EVENT_MANAGER_PLUGINS,\n  useClass: KeyEventsPlugin,\n  multi: true,\n  deps: [DOCUMENT]\n}, DomRendererFactory2, SharedStylesHost, EventManager, {\n  provide: RendererFactory2,\n  useExisting: DomRendererFactory2\n}, {\n  provide: XhrFactory,\n  useClass: BrowserXhr,\n  deps: []\n}, typeof ngDevMode === 'undefined' || ngDevMode ? {\n  provide: BROWSER_MODULE_PROVIDERS_MARKER,\n  useValue: true\n} : []];\n/**\n * Exports required infrastructure for all Angular apps.\n * Included by default in all Angular apps created with the CLI\n * `new` command.\n * Re-exports `CommonModule` and `ApplicationModule`, making their\n * exports and providers available to all apps.\n *\n * @publicApi\n */\nclass BrowserModule {\n  constructor(providersAlreadyPresent) {\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && providersAlreadyPresent) {\n      throw new ɵRuntimeError(5100 /* RuntimeErrorCode.BROWER_MODULE_ALREADY_LOADED */, `Providers from the \\`BrowserModule\\` have already been loaded. If you need access ` + `to common directives such as NgIf and NgFor, import the \\`CommonModule\\` instead.`);\n    }\n  }\n  /**\n   * Configures a browser-based app to transition from a server-rendered app, if\n   * one is present on the page.\n   *\n   * @param params An object containing an identifier for the app to transition.\n   * The ID must match between the client and server versions of the app.\n   * @returns The reconfigured `BrowserModule` to import into the app's root `AppModule`.\n   *\n   * @deprecated Use {@link APP_ID} instead to set the application ID.\n   */\n  static withServerTransition(params) {\n    return {\n      ngModule: BrowserModule,\n      providers: [{\n        provide: APP_ID,\n        useValue: params.appId\n      }]\n    };\n  }\n  static {\n    this.ɵfac = function BrowserModule_Factory(t) {\n      return new (t || BrowserModule)(i0.ɵɵinject(BROWSER_MODULE_PROVIDERS_MARKER, 12));\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: BrowserModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [...BROWSER_MODULE_PROVIDERS, ...TESTABILITY_PROVIDERS],\n      imports: [CommonModule, ApplicationModule]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserModule, [{\n    type: NgModule,\n    args: [{\n      providers: [...BROWSER_MODULE_PROVIDERS, ...TESTABILITY_PROVIDERS],\n      exports: [CommonModule, ApplicationModule]\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: SkipSelf\n      }, {\n        type: Inject,\n        args: [BROWSER_MODULE_PROVIDERS_MARKER]\n      }]\n    }];\n  }, null);\n})();\n\n/**\n * Factory to create a `Meta` service instance for the current DOM document.\n */\nfunction createMeta() {\n  return new Meta(ɵɵinject(DOCUMENT));\n}\n/**\n * A service for managing HTML `<meta>` tags.\n *\n * Properties of the `MetaDefinition` object match the attributes of the\n * HTML `<meta>` tag. These tags define document metadata that is important for\n * things like configuring a Content Security Policy, defining browser compatibility\n * and security settings, setting HTTP Headers, defining rich content for social sharing,\n * and Search Engine Optimization (SEO).\n *\n * To identify specific `<meta>` tags in a document, use an attribute selection\n * string in the format `\"tag_attribute='value string'\"`.\n * For example, an `attrSelector` value of `\"name='description'\"` matches a tag\n * whose `name` attribute has the value `\"description\"`.\n * Selectors are used with the `querySelector()` Document method,\n * in the format `meta[{attrSelector}]`.\n *\n * @see [HTML meta tag](https://developer.mozilla.org/docs/Web/HTML/Element/meta)\n * @see [Document.querySelector()](https://developer.mozilla.org/docs/Web/API/Document/querySelector)\n *\n *\n * @publicApi\n */\nclass Meta {\n  constructor(_doc) {\n    this._doc = _doc;\n    this._dom = ɵgetDOM();\n  }\n  /**\n   * Retrieves or creates a specific `<meta>` tag element in the current HTML document.\n   * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n   * values in the provided tag definition, and verifies that all other attribute values are equal.\n   * If an existing element is found, it is returned and is not modified in any way.\n   * @param tag The definition of a `<meta>` element to match or create.\n   * @param forceCreation True to create a new element without checking whether one already exists.\n   * @returns The existing element with the same attributes and values if found,\n   * the new element if no match is found, or `null` if the tag parameter is not defined.\n   */\n  addTag(tag, forceCreation = false) {\n    if (!tag) return null;\n    return this._getOrCreateElement(tag, forceCreation);\n  }\n  /**\n   * Retrieves or creates a set of `<meta>` tag elements in the current HTML document.\n   * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n   * values in the provided tag definition, and verifies that all other attribute values are equal.\n   * @param tags An array of tag definitions to match or create.\n   * @param forceCreation True to create new elements without checking whether they already exist.\n   * @returns The matching elements if found, or the new elements.\n   */\n  addTags(tags, forceCreation = false) {\n    if (!tags) return [];\n    return tags.reduce((result, tag) => {\n      if (tag) {\n        result.push(this._getOrCreateElement(tag, forceCreation));\n      }\n      return result;\n    }, []);\n  }\n  /**\n   * Retrieves a `<meta>` tag element in the current HTML document.\n   * @param attrSelector The tag attribute and value to match against, in the format\n   * `\"tag_attribute='value string'\"`.\n   * @returns The matching element, if any.\n   */\n  getTag(attrSelector) {\n    if (!attrSelector) return null;\n    return this._doc.querySelector(`meta[${attrSelector}]`) || null;\n  }\n  /**\n   * Retrieves a set of `<meta>` tag elements in the current HTML document.\n   * @param attrSelector The tag attribute and value to match against, in the format\n   * `\"tag_attribute='value string'\"`.\n   * @returns The matching elements, if any.\n   */\n  getTags(attrSelector) {\n    if (!attrSelector) return [];\n    const list /*NodeList*/ = this._doc.querySelectorAll(`meta[${attrSelector}]`);\n    return list ? [].slice.call(list) : [];\n  }\n  /**\n   * Modifies an existing `<meta>` tag element in the current HTML document.\n   * @param tag The tag description with which to replace the existing tag content.\n   * @param selector A tag attribute and value to match against, to identify\n   * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n   * If not supplied, matches a tag with the same `name` or `property` attribute value as the\n   * replacement tag.\n   * @return The modified element.\n   */\n  updateTag(tag, selector) {\n    if (!tag) return null;\n    selector = selector || this._parseSelector(tag);\n    const meta = this.getTag(selector);\n    if (meta) {\n      return this._setMetaElementAttributes(tag, meta);\n    }\n    return this._getOrCreateElement(tag, true);\n  }\n  /**\n   * Removes an existing `<meta>` tag element from the current HTML document.\n   * @param attrSelector A tag attribute and value to match against, to identify\n   * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n   */\n  removeTag(attrSelector) {\n    this.removeTagElement(this.getTag(attrSelector));\n  }\n  /**\n   * Removes an existing `<meta>` tag element from the current HTML document.\n   * @param meta The tag definition to match against to identify an existing tag.\n   */\n  removeTagElement(meta) {\n    if (meta) {\n      this._dom.remove(meta);\n    }\n  }\n  _getOrCreateElement(meta, forceCreation = false) {\n    if (!forceCreation) {\n      const selector = this._parseSelector(meta);\n      // It's allowed to have multiple elements with the same name so it's not enough to\n      // just check that element with the same name already present on the page. We also need to\n      // check if element has tag attributes\n      const elem = this.getTags(selector).filter(elem => this._containsAttributes(meta, elem))[0];\n      if (elem !== undefined) return elem;\n    }\n    const element = this._dom.createElement('meta');\n    this._setMetaElementAttributes(meta, element);\n    const head = this._doc.getElementsByTagName('head')[0];\n    head.appendChild(element);\n    return element;\n  }\n  _setMetaElementAttributes(tag, el) {\n    Object.keys(tag).forEach(prop => el.setAttribute(this._getMetaKeyMap(prop), tag[prop]));\n    return el;\n  }\n  _parseSelector(tag) {\n    const attr = tag.name ? 'name' : 'property';\n    return `${attr}=\"${tag[attr]}\"`;\n  }\n  _containsAttributes(tag, elem) {\n    return Object.keys(tag).every(key => elem.getAttribute(this._getMetaKeyMap(key)) === tag[key]);\n  }\n  _getMetaKeyMap(prop) {\n    return META_KEYS_MAP[prop] || prop;\n  }\n  static {\n    this.ɵfac = function Meta_Factory(t) {\n      return new (t || Meta)(i0.ɵɵinject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: Meta,\n      factory: function Meta_Factory(t) {\n        let r = null;\n        if (t) {\n          r = new t();\n        } else {\n          r = createMeta();\n        }\n        return r;\n      },\n      providedIn: 'root'\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Meta, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useFactory: createMeta,\n      deps: []\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n/**\n * Mapping for MetaDefinition properties with their correct meta attribute names\n */\nconst META_KEYS_MAP = {\n  httpEquiv: 'http-equiv'\n};\n\n/**\n * Factory to create Title service.\n */\nfunction createTitle() {\n  return new Title(ɵɵinject(DOCUMENT));\n}\n/**\n * A service that can be used to get and set the title of a current HTML document.\n *\n * Since an Angular application can't be bootstrapped on the entire HTML document (`<html>` tag)\n * it is not possible to bind to the `text` property of the `HTMLTitleElement` elements\n * (representing the `<title>` tag). Instead, this service can be used to set and get the current\n * title value.\n *\n * @publicApi\n */\nclass Title {\n  constructor(_doc) {\n    this._doc = _doc;\n  }\n  /**\n   * Get the title of the current HTML document.\n   */\n  getTitle() {\n    return this._doc.title;\n  }\n  /**\n   * Set the title of the current HTML document.\n   * @param newTitle\n   */\n  setTitle(newTitle) {\n    this._doc.title = newTitle || '';\n  }\n  static {\n    this.ɵfac = function Title_Factory(t) {\n      return new (t || Title)(i0.ɵɵinject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: Title,\n      factory: function Title_Factory(t) {\n        let r = null;\n        if (t) {\n          r = new t();\n        } else {\n          r = createTitle();\n        }\n        return r;\n      },\n      providedIn: 'root'\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Title, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useFactory: createTitle,\n      deps: []\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n\n/**\n * Exports the value under a given `name` in the global property `ng`. For example `ng.probe` if\n * `name` is `'probe'`.\n * @param name Name under which it will be exported. Keep in mind this will be a property of the\n * global `ng` object.\n * @param value The value to export.\n */\nfunction exportNgVar(name, value) {\n  if (typeof COMPILED === 'undefined' || !COMPILED) {\n    // Note: we can't export `ng` when using closure enhanced optimization as:\n    // - closure declares globals itself for minified names, which sometimes clobber our `ng` global\n    // - we can't declare a closure extern as the namespace `ng` is already used within Google\n    //   for typings for angularJS (via `goog.provide('ng....')`).\n    const ng = ɵglobal['ng'] = ɵglobal['ng'] || {};\n    ng[name] = value;\n  }\n}\nconst win = typeof window !== 'undefined' && window || {};\nclass ChangeDetectionPerfRecord {\n  constructor(msPerTick, numTicks) {\n    this.msPerTick = msPerTick;\n    this.numTicks = numTicks;\n  }\n}\n/**\n * Entry point for all Angular profiling-related debug tools. This object\n * corresponds to the `ng.profiler` in the dev console.\n */\nclass AngularProfiler {\n  constructor(ref) {\n    this.appRef = ref.injector.get(ApplicationRef);\n  }\n  // tslint:disable:no-console\n  /**\n   * Exercises change detection in a loop and then prints the average amount of\n   * time in milliseconds how long a single round of change detection takes for\n   * the current state of the UI. It runs a minimum of 5 rounds for a minimum\n   * of 500 milliseconds.\n   *\n   * Optionally, a user may pass a `config` parameter containing a map of\n   * options. Supported options are:\n   *\n   * `record` (boolean) - causes the profiler to record a CPU profile while\n   * it exercises the change detector. Example:\n   *\n   * ```\n   * ng.profiler.timeChangeDetection({record: true})\n   * ```\n   */\n  timeChangeDetection(config) {\n    const record = config && config['record'];\n    const profileName = 'Change Detection';\n    // Profiler is not available in Android browsers without dev tools opened\n    const isProfilerAvailable = win.console.profile != null;\n    if (record && isProfilerAvailable) {\n      win.console.profile(profileName);\n    }\n    const start = performanceNow();\n    let numTicks = 0;\n    while (numTicks < 5 || performanceNow() - start < 500) {\n      this.appRef.tick();\n      numTicks++;\n    }\n    const end = performanceNow();\n    if (record && isProfilerAvailable) {\n      win.console.profileEnd(profileName);\n    }\n    const msPerTick = (end - start) / numTicks;\n    win.console.log(`ran ${numTicks} change detection cycles`);\n    win.console.log(`${msPerTick.toFixed(2)} ms per check`);\n    return new ChangeDetectionPerfRecord(msPerTick, numTicks);\n  }\n}\nfunction performanceNow() {\n  return win.performance && win.performance.now ? win.performance.now() : new Date().getTime();\n}\nconst PROFILER_GLOBAL_NAME = 'profiler';\n/**\n * Enabled Angular debug tools that are accessible via your browser's\n * developer console.\n *\n * Usage:\n *\n * 1. Open developer console (e.g. in Chrome Ctrl + Shift + j)\n * 1. Type `ng.` (usually the console will show auto-complete suggestion)\n * 1. Try the change detection profiler `ng.profiler.timeChangeDetection()`\n *    then hit Enter.\n *\n * @publicApi\n */\nfunction enableDebugTools(ref) {\n  exportNgVar(PROFILER_GLOBAL_NAME, new AngularProfiler(ref));\n  return ref;\n}\n/**\n * Disables Angular tools.\n *\n * @publicApi\n */\nfunction disableDebugTools() {\n  exportNgVar(PROFILER_GLOBAL_NAME, null);\n}\n\n/**\n * Predicates for use with {@link DebugElement}'s query functions.\n *\n * @publicApi\n */\nclass By {\n  /**\n   * Match all nodes.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_all'}\n   */\n  static all() {\n    return () => true;\n  }\n  /**\n   * Match elements by the given CSS selector.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_css'}\n   */\n  static css(selector) {\n    return debugElement => {\n      return debugElement.nativeElement != null ? elementMatches(debugElement.nativeElement, selector) : false;\n    };\n  }\n  /**\n   * Match nodes that have the given directive present.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_directive'}\n   */\n  static directive(type) {\n    return debugNode => debugNode.providerTokens.indexOf(type) !== -1;\n  }\n}\nfunction elementMatches(n, selector) {\n  if (ɵgetDOM().isElementNode(n)) {\n    return n.matches && n.matches(selector) || n.msMatchesSelector && n.msMatchesSelector(selector) || n.webkitMatchesSelector && n.webkitMatchesSelector(selector);\n  }\n  return false;\n}\n\n/**\n * Supported HammerJS recognizer event names.\n */\nconst EVENT_NAMES = {\n  // pan\n  'pan': true,\n  'panstart': true,\n  'panmove': true,\n  'panend': true,\n  'pancancel': true,\n  'panleft': true,\n  'panright': true,\n  'panup': true,\n  'pandown': true,\n  // pinch\n  'pinch': true,\n  'pinchstart': true,\n  'pinchmove': true,\n  'pinchend': true,\n  'pinchcancel': true,\n  'pinchin': true,\n  'pinchout': true,\n  // press\n  'press': true,\n  'pressup': true,\n  // rotate\n  'rotate': true,\n  'rotatestart': true,\n  'rotatemove': true,\n  'rotateend': true,\n  'rotatecancel': true,\n  // swipe\n  'swipe': true,\n  'swipeleft': true,\n  'swiperight': true,\n  'swipeup': true,\n  'swipedown': true,\n  // tap\n  'tap': true,\n  'doubletap': true\n};\n/**\n * DI token for providing [HammerJS](https://hammerjs.github.io/) support to Angular.\n * @see {@link HammerGestureConfig}\n *\n * @ngModule HammerModule\n * @publicApi\n */\nconst HAMMER_GESTURE_CONFIG = new InjectionToken('HammerGestureConfig');\n/**\n * Injection token used to provide a {@link HammerLoader} to Angular.\n *\n * @publicApi\n */\nconst HAMMER_LOADER = new InjectionToken('HammerLoader');\n/**\n * An injectable [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n * for gesture recognition. Configures specific event recognition.\n * @publicApi\n */\nclass HammerGestureConfig {\n  constructor() {\n    /**\n     * A set of supported event names for gestures to be used in Angular.\n     * Angular supports all built-in recognizers, as listed in\n     * [HammerJS documentation](https://hammerjs.github.io/).\n     */\n    this.events = [];\n    /**\n     * Maps gesture event names to a set of configuration options\n     * that specify overrides to the default values for specific properties.\n     *\n     * The key is a supported event name to be configured,\n     * and the options object contains a set of properties, with override values\n     * to be applied to the named recognizer event.\n     * For example, to disable recognition of the rotate event, specify\n     *  `{\"rotate\": {\"enable\": false}}`.\n     *\n     * Properties that are not present take the HammerJS default values.\n     * For information about which properties are supported for which events,\n     * and their allowed and default values, see\n     * [HammerJS documentation](https://hammerjs.github.io/).\n     *\n     */\n    this.overrides = {};\n  }\n  /**\n   * Creates a [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n   * and attaches it to a given HTML element.\n   * @param element The element that will recognize gestures.\n   * @returns A HammerJS event-manager object.\n   */\n  buildHammer(element) {\n    const mc = new Hammer(element, this.options);\n    mc.get('pinch').set({\n      enable: true\n    });\n    mc.get('rotate').set({\n      enable: true\n    });\n    for (const eventName in this.overrides) {\n      mc.get(eventName).set(this.overrides[eventName]);\n    }\n    return mc;\n  }\n  static {\n    this.ɵfac = function HammerGestureConfig_Factory(t) {\n      return new (t || HammerGestureConfig)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: HammerGestureConfig,\n      factory: HammerGestureConfig.ɵfac\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HammerGestureConfig, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n * Event plugin that adds Hammer support to an application.\n *\n * @ngModule HammerModule\n */\nclass HammerGesturesPlugin extends EventManagerPlugin {\n  constructor(doc, _config, console, loader) {\n    super(doc);\n    this._config = _config;\n    this.console = console;\n    this.loader = loader;\n    this._loaderPromise = null;\n  }\n  supports(eventName) {\n    if (!EVENT_NAMES.hasOwnProperty(eventName.toLowerCase()) && !this.isCustomEvent(eventName)) {\n      return false;\n    }\n    if (!window.Hammer && !this.loader) {\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        this.console.warn(`The \"${eventName}\" event cannot be bound because Hammer.JS is not ` + `loaded and no custom loader has been specified.`);\n      }\n      return false;\n    }\n    return true;\n  }\n  addEventListener(element, eventName, handler) {\n    const zone = this.manager.getZone();\n    eventName = eventName.toLowerCase();\n    // If Hammer is not present but a loader is specified, we defer adding the event listener\n    // until Hammer is loaded.\n    if (!window.Hammer && this.loader) {\n      this._loaderPromise = this._loaderPromise || zone.runOutsideAngular(() => this.loader());\n      // This `addEventListener` method returns a function to remove the added listener.\n      // Until Hammer is loaded, the returned function needs to *cancel* the registration rather\n      // than remove anything.\n      let cancelRegistration = false;\n      let deregister = () => {\n        cancelRegistration = true;\n      };\n      zone.runOutsideAngular(() => this._loaderPromise.then(() => {\n        // If Hammer isn't actually loaded when the custom loader resolves, give up.\n        if (!window.Hammer) {\n          if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            this.console.warn(`The custom HAMMER_LOADER completed, but Hammer.JS is not present.`);\n          }\n          deregister = () => {};\n          return;\n        }\n        if (!cancelRegistration) {\n          // Now that Hammer is loaded and the listener is being loaded for real,\n          // the deregistration function changes from canceling registration to\n          // removal.\n          deregister = this.addEventListener(element, eventName, handler);\n        }\n      }).catch(() => {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n          this.console.warn(`The \"${eventName}\" event cannot be bound because the custom ` + `Hammer.JS loader failed.`);\n        }\n        deregister = () => {};\n      }));\n      // Return a function that *executes* `deregister` (and not `deregister` itself) so that we\n      // can change the behavior of `deregister` once the listener is added. Using a closure in\n      // this way allows us to avoid any additional data structures to track listener removal.\n      return () => {\n        deregister();\n      };\n    }\n    return zone.runOutsideAngular(() => {\n      // Creating the manager bind events, must be done outside of angular\n      const mc = this._config.buildHammer(element);\n      const callback = function (eventObj) {\n        zone.runGuarded(function () {\n          handler(eventObj);\n        });\n      };\n      mc.on(eventName, callback);\n      return () => {\n        mc.off(eventName, callback);\n        // destroy mc to prevent memory leak\n        if (typeof mc.destroy === 'function') {\n          mc.destroy();\n        }\n      };\n    });\n  }\n  isCustomEvent(eventName) {\n    return this._config.events.indexOf(eventName) > -1;\n  }\n  static {\n    this.ɵfac = function HammerGesturesPlugin_Factory(t) {\n      return new (t || HammerGesturesPlugin)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(HAMMER_GESTURE_CONFIG), i0.ɵɵinject(i0.ɵConsole), i0.ɵɵinject(HAMMER_LOADER, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: HammerGesturesPlugin,\n      factory: HammerGesturesPlugin.ɵfac\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HammerGesturesPlugin, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: HammerGestureConfig,\n      decorators: [{\n        type: Inject,\n        args: [HAMMER_GESTURE_CONFIG]\n      }]\n    }, {\n      type: i0.ɵConsole\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [HAMMER_LOADER]\n      }]\n    }];\n  }, null);\n})();\n/**\n * Adds support for HammerJS.\n *\n * Import this module at the root of your application so that Angular can work with\n * HammerJS to detect gesture events.\n *\n * Note that applications still need to include the HammerJS script itself. This module\n * simply sets up the coordination layer between HammerJS and Angular's EventManager.\n *\n * @publicApi\n */\nclass HammerModule {\n  static {\n    this.ɵfac = function HammerModule_Factory(t) {\n      return new (t || HammerModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: HammerModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [{\n        provide: EVENT_MANAGER_PLUGINS,\n        useClass: HammerGesturesPlugin,\n        multi: true,\n        deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, ɵConsole, [new Optional(), HAMMER_LOADER]]\n      }, {\n        provide: HAMMER_GESTURE_CONFIG,\n        useClass: HammerGestureConfig,\n        deps: []\n      }]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HammerModule, [{\n    type: NgModule,\n    args: [{\n      providers: [{\n        provide: EVENT_MANAGER_PLUGINS,\n        useClass: HammerGesturesPlugin,\n        multi: true,\n        deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, ɵConsole, [new Optional(), HAMMER_LOADER]]\n      }, {\n        provide: HAMMER_GESTURE_CONFIG,\n        useClass: HammerGestureConfig,\n        deps: []\n      }]\n    }]\n  }], null, null);\n})();\n\n/**\n * DomSanitizer helps preventing Cross Site Scripting Security bugs (XSS) by sanitizing\n * values to be safe to use in the different DOM contexts.\n *\n * For example, when binding a URL in an `<a [href]=\"someValue\">` hyperlink, `someValue` will be\n * sanitized so that an attacker cannot inject e.g. a `javascript:` URL that would execute code on\n * the website.\n *\n * In specific situations, it might be necessary to disable sanitization, for example if the\n * application genuinely needs to produce a `javascript:` style link with a dynamic value in it.\n * Users can bypass security by constructing a value with one of the `bypassSecurityTrust...`\n * methods, and then binding to that value from the template.\n *\n * These situations should be very rare, and extraordinary care must be taken to avoid creating a\n * Cross Site Scripting (XSS) security bug!\n *\n * When using `bypassSecurityTrust...`, make sure to call the method as early as possible and as\n * close as possible to the source of the value, to make it easy to verify no security bug is\n * created by its use.\n *\n * It is not required (and not recommended) to bypass security if the value is safe, e.g. a URL that\n * does not start with a suspicious protocol, or an HTML snippet that does not contain dangerous\n * code. The sanitizer leaves safe values intact.\n *\n * @security Calling any of the `bypassSecurityTrust...` APIs disables Angular's built-in\n * sanitization for the value passed in. Carefully check and audit all values and code paths going\n * into this call. Make sure any user data is appropriately escaped for this security context.\n * For more detail, see the [Security Guide](https://g.co/ng/security).\n *\n * @publicApi\n */\nclass DomSanitizer {\n  static {\n    this.ɵfac = function DomSanitizer_Factory(t) {\n      return new (t || DomSanitizer)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: DomSanitizer,\n      factory: function DomSanitizer_Factory(t) {\n        let r = null;\n        if (t) {\n          r = new (t || DomSanitizer)();\n        } else {\n          r = i0.ɵɵinject(DomSanitizerImpl);\n        }\n        return r;\n      },\n      providedIn: 'root'\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomSanitizer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useExisting: forwardRef(() => DomSanitizerImpl)\n    }]\n  }], null, null);\n})();\nfunction domSanitizerImplFactory(injector) {\n  return new DomSanitizerImpl(injector.get(DOCUMENT));\n}\nclass DomSanitizerImpl extends DomSanitizer {\n  constructor(_doc) {\n    super();\n    this._doc = _doc;\n  }\n  sanitize(ctx, value) {\n    if (value == null) return null;\n    switch (ctx) {\n      case SecurityContext.NONE:\n        return value;\n      case SecurityContext.HTML:\n        if (ɵallowSanitizationBypassAndThrow(value, \"HTML\" /* BypassType.Html */)) {\n          return ɵunwrapSafeValue(value);\n        }\n        return ɵ_sanitizeHtml(this._doc, String(value)).toString();\n      case SecurityContext.STYLE:\n        if (ɵallowSanitizationBypassAndThrow(value, \"Style\" /* BypassType.Style */)) {\n          return ɵunwrapSafeValue(value);\n        }\n        return value;\n      case SecurityContext.SCRIPT:\n        if (ɵallowSanitizationBypassAndThrow(value, \"Script\" /* BypassType.Script */)) {\n          return ɵunwrapSafeValue(value);\n        }\n        throw new ɵRuntimeError(5200 /* RuntimeErrorCode.SANITIZATION_UNSAFE_SCRIPT */, (typeof ngDevMode === 'undefined' || ngDevMode) && 'unsafe value used in a script context');\n      case SecurityContext.URL:\n        if (ɵallowSanitizationBypassAndThrow(value, \"URL\" /* BypassType.Url */)) {\n          return ɵunwrapSafeValue(value);\n        }\n        return ɵ_sanitizeUrl(String(value));\n      case SecurityContext.RESOURCE_URL:\n        if (ɵallowSanitizationBypassAndThrow(value, \"ResourceURL\" /* BypassType.ResourceUrl */)) {\n          return ɵunwrapSafeValue(value);\n        }\n        throw new ɵRuntimeError(5201 /* RuntimeErrorCode.SANITIZATION_UNSAFE_RESOURCE_URL */, (typeof ngDevMode === 'undefined' || ngDevMode) && `unsafe value used in a resource URL context (see ${ɵXSS_SECURITY_URL})`);\n      default:\n        throw new ɵRuntimeError(5202 /* RuntimeErrorCode.SANITIZATION_UNEXPECTED_CTX */, (typeof ngDevMode === 'undefined' || ngDevMode) && `Unexpected SecurityContext ${ctx} (see ${ɵXSS_SECURITY_URL})`);\n    }\n  }\n  bypassSecurityTrustHtml(value) {\n    return ɵbypassSanitizationTrustHtml(value);\n  }\n  bypassSecurityTrustStyle(value) {\n    return ɵbypassSanitizationTrustStyle(value);\n  }\n  bypassSecurityTrustScript(value) {\n    return ɵbypassSanitizationTrustScript(value);\n  }\n  bypassSecurityTrustUrl(value) {\n    return ɵbypassSanitizationTrustUrl(value);\n  }\n  bypassSecurityTrustResourceUrl(value) {\n    return ɵbypassSanitizationTrustResourceUrl(value);\n  }\n  static {\n    this.ɵfac = function DomSanitizerImpl_Factory(t) {\n      return new (t || DomSanitizerImpl)(i0.ɵɵinject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: DomSanitizerImpl,\n      factory: function DomSanitizerImpl_Factory(t) {\n        let r = null;\n        if (t) {\n          r = new t();\n        } else {\n          r = domSanitizerImplFactory(i0.ɵɵinject(Injector));\n        }\n        return r;\n      },\n      providedIn: 'root'\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomSanitizerImpl, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useFactory: domSanitizerImplFactory,\n      deps: [Injector]\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n\n/**\n * Helper function to create an object that represents a Hydration feature.\n */\nfunction hydrationFeature(kind, providers = []) {\n  return {\n    ɵkind: kind,\n    ɵproviders: providers\n  };\n}\n/**\n * Disables DOM nodes reuse during hydration. Effectively makes\n * Angular re-render an application from scratch on the client.\n *\n * When this option is enabled, make sure that the initial navigation\n * option is configured for the Router as `enabledBlocking` by using the\n * `withEnabledBlockingInitialNavigation` in the `provideRouter` call:\n *\n * ```\n * bootstrapApplication(RootComponent, {\n *   providers: [\n *     provideRouter(\n *       // ... other features ...\n *       withEnabledBlockingInitialNavigation()\n *     ),\n *     provideClientHydration(withNoDomReuse())\n *   ]\n * });\n * ```\n *\n * This would ensure that the application is rerendered after all async\n * operations in the Router (such as lazy-loading of components,\n * waiting for async guards and resolvers) are completed to avoid\n * clearing the DOM on the client too soon, thus causing content flicker.\n *\n * @see {@link provideRouter}\n * @see {@link withEnabledBlockingInitialNavigation}\n *\n * @publicApi\n * @developerPreview\n */\nfunction withNoDomReuse() {\n  // This feature has no providers and acts as a flag that turns off\n  // non-destructive hydration (which otherwise is turned on by default).\n  return hydrationFeature(0 /* HydrationFeatureKind.NoDomReuseFeature */);\n}\n/**\n * Disables HTTP transfer cache. Effectively causes HTTP requests to be performed twice: once on the\n * server and other one on the browser.\n *\n * @publicApi\n * @developerPreview\n */\nfunction withNoHttpTransferCache() {\n  // This feature has no providers and acts as a flag that turns off\n  // HTTP transfer cache (which otherwise is turned on by default).\n  return hydrationFeature(1 /* HydrationFeatureKind.NoHttpTransferCache */);\n}\n/**\n * Returns an `ENVIRONMENT_INITIALIZER` token setup with a function\n * that verifies whether compatible ZoneJS was used in an application\n * and logs a warning in a console if it's not the case.\n */\nfunction provideZoneJsCompatibilityDetector() {\n  return [{\n    provide: ENVIRONMENT_INITIALIZER,\n    useValue: () => {\n      const ngZone = inject(NgZone);\n      // Checking `ngZone instanceof NgZone` would be insufficient here,\n      // because custom implementations might use NgZone as a base class.\n      if (ngZone.constructor !== NgZone) {\n        const console = inject(ɵConsole);\n        const message = ɵformatRuntimeError(-5000 /* RuntimeErrorCode.UNSUPPORTED_ZONEJS_INSTANCE */, 'Angular detected that hydration was enabled for an application ' + 'that uses a custom or a noop Zone.js implementation. ' + 'This is not yet a fully supported configuration.');\n        // tslint:disable-next-line:no-console\n        console.warn(message);\n      }\n    },\n    multi: true\n  }];\n}\n/**\n * Sets up providers necessary to enable hydration functionality for the application.\n *\n * By default, the function enables the recommended set of features for the optimal\n * performance for most of the applications. You can enable/disable features by\n * passing special functions (from the `HydrationFeatures` set) as arguments to the\n * `provideClientHydration` function. It includes the following features:\n *\n * * Reconciling DOM hydration. Learn more about it [here](guide/hydration).\n * * [`HttpClient`](api/common/http/HttpClient) response caching while running on the server and\n * transferring this cache to the client to avoid extra HTTP requests. Learn more about data caching\n * [here](/guide/universal#caching-data-when-using-httpclient).\n *\n * These functions functions will allow you to disable some of the default features:\n * * {@link withNoDomReuse} to disable DOM nodes reuse during hydration\n * * {@link withNoHttpTransferCache} to disable HTTP transfer cache\n *\n *\n * @usageNotes\n *\n * Basic example of how you can enable hydration in your application when\n * `bootstrapApplication` function is used:\n * ```\n * bootstrapApplication(AppComponent, {\n *   providers: [provideClientHydration()]\n * });\n * ```\n *\n * Alternatively if you are using NgModules, you would add `provideClientHydration`\n * to your root app module's provider list.\n * ```\n * @NgModule({\n *   declarations: [RootCmp],\n *   bootstrap: [RootCmp],\n *   providers: [provideClientHydration()],\n * })\n * export class AppModule {}\n * ```\n *\n * @see {@link withNoDomReuse}\n * @see {@link withNoHttpTransferCache}\n *\n * @param features Optional features to configure additional router behaviors.\n * @returns A set of providers to enable hydration.\n *\n * @publicApi\n * @developerPreview\n */\nfunction provideClientHydration(...features) {\n  const providers = [];\n  const featuresKind = new Set();\n  for (const {\n    ɵproviders,\n    ɵkind\n  } of features) {\n    featuresKind.add(ɵkind);\n    if (ɵproviders.length) {\n      providers.push(ɵproviders);\n    }\n  }\n  return makeEnvironmentProviders([typeof ngDevMode !== 'undefined' && ngDevMode ? provideZoneJsCompatibilityDetector() : [], featuresKind.has(0 /* HydrationFeatureKind.NoDomReuseFeature */) ? [] : ɵwithDomHydration(), featuresKind.has(1 /* HydrationFeatureKind.NoHttpTransferCache */) ? [] : ɵwithHttpTransferCache(), providers]);\n}\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser package.\n */\n/**\n * @publicApi\n */\nconst VERSION = new Version('16.2.12');\n\n// Re-export TransferState to the public API of the `platform-browser` for backwards-compatibility.\n/**\n * Create a `StateKey<T>` that can be used to store value of type T with `TransferState`.\n *\n * Example:\n *\n * ```\n * const COUNTER_KEY = makeStateKey<number>('counter');\n * let value = 10;\n *\n * transferState.set(COUNTER_KEY, value);\n * ```\n *\n * @publicApi\n * @deprecated `makeStateKey` has moved, please import `makeStateKey` from `@angular/core` instead.\n */\n// The below is a workaround to add a deprecated message.\nconst makeStateKey = makeStateKey$1;\n// The below type is needed for G3 due to JSC_CONFORMANCE_VIOLATION.\nconst TransferState = TransferState$1;\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\n// This file only reexports content of the `src` folder. Keep it that way.\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BrowserModule, By, DomSanitizer, EVENT_MANAGER_PLUGINS, EventManager, HAMMER_GESTURE_CONFIG, HAMMER_LOADER, HammerGestureConfig, HammerModule, Meta, REMOVE_STYLES_ON_COMPONENT_DESTROY, Title, TransferState, VERSION, bootstrapApplication, createApplication, disableDebugTools, enableDebugTools, makeStateKey, platformBrowser, provideClientHydration, provideProtractorTestingSupport, withNoDomReuse, withNoHttpTransferCache, BrowserDomAdapter as ɵBrowserDomAdapter, BrowserGetTestability as ɵBrowserGetTestability, DomEventsPlugin as ɵDomEventsPlugin, DomRendererFactory2 as ɵDomRendererFactory2, DomSanitizerImpl as ɵDomSanitizerImpl, HammerGesturesPlugin as ɵHammerGesturesPlugin, INTERNAL_BROWSER_PLATFORM_PROVIDERS as ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS, KeyEventsPlugin as ɵKeyEventsPlugin, SharedStylesHost as ɵSharedStylesHost, initDomAdapter as ɵinitDomAdapter };", "map": {"version": 3, "names": ["i0", "ɵglobal", "ɵRuntimeError", "Injectable", "InjectionToken", "Inject", "APP_ID", "CSP_NONCE", "PLATFORM_ID", "Optional", "ViewEncapsulation", "RendererStyleFlags2", "ɵinternalCreateApplication", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ɵsetDocument", "PLATFORM_INITIALIZER", "createPlatformFactory", "platformCore", "ɵTESTABILITY_GETTER", "ɵTESTABILITY", "Testability", "NgZone", "TestabilityRegistry", "ɵINJECTOR_SCOPE", "RendererFactory2", "ApplicationModule", "NgModule", "SkipSelf", "ɵɵinject", "ApplicationRef", "ɵConsole", "forwardRef", "ɵXSS_SECURITY_URL", "SecurityContext", "ɵallowSanitizationBypassAndThrow", "ɵunwrapSafeValue", "ɵ_sanitizeUrl", "ɵ_sanitizeHtml", "ɵbypassSanitizationTrustHtml", "ɵbypassSanitizationTrustStyle", "ɵbypassSanitizationTrustScript", "ɵbypassSanitizationTrustUrl", "ɵbypassSanitizationTrustResourceUrl", "Injector", "ENVIRONMENT_INITIALIZER", "inject", "ɵformatRuntimeError", "makeEnvironmentProviders", "ɵwithDomHydration", "Version", "makeStateKey", "makeStateKey$1", "TransferState", "TransferState$1", "ɵDomAdapter", "ɵsetRootDomAdapter", "ɵparseCookieValue", "ɵgetDOM", "isPlatformServer", "DOCUMENT", "ɵPLATFORM_BROWSER_ID", "XhrFactory", "CommonModule", "ɵwithHttpTransferCache", "GenericBrowserDomAdapter", "constructor", "arguments", "supportsDOMEvents", "BrowserDomAdapter", "makeCurrent", "onAndCancel", "el", "evt", "listener", "addEventListener", "removeEventListener", "dispatchEvent", "remove", "node", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "createElement", "tagName", "doc", "getDefaultDocument", "createHtmlDocument", "document", "implementation", "createHTMLDocument", "isElementNode", "nodeType", "Node", "ELEMENT_NODE", "isShadowRoot", "DocumentFragment", "getGlobalEventTarget", "target", "window", "body", "getBaseHref", "href", "getBaseElementHref", "relativePath", "resetBaseElement", "baseElement", "getUserAgent", "navigator", "userAgent", "<PERSON><PERSON><PERSON><PERSON>", "name", "cookie", "querySelector", "getAttribute", "urlParsingNode", "url", "setAttribute", "pathName", "pathname", "char<PERSON>t", "BrowserGetTestability", "addToWindow", "registry", "elem", "findInAncestors", "testability", "findTestabilityInTree", "ngDevMode", "getAllTestabilities", "getAllRootElements", "whenAllStable", "callback", "testabilities", "count", "length", "didWork", "decrement", "didWork_", "for<PERSON>ach", "whenStable", "push", "t", "getTestability", "host", "parentElement", "BrowserXhr", "build", "XMLHttpRequest", "ɵfac", "BrowserXhr_Factory", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ɵsetClassMetadata", "type", "EVENT_MANAGER_PLUGINS", "EventManager", "plugins", "_zone", "_eventNameToPlugin", "Map", "plugin", "manager", "_plugins", "slice", "reverse", "element", "eventName", "handler", "_findPluginFor", "getZone", "get", "find", "supports", "set", "EventManager_Factory", "undefined", "decorators", "args", "EventManagerPlugin", "_doc", "APP_ID_ATTRIBUTE_NAME", "SharedStylesHost", "appId", "nonce", "platformId", "styleRef", "hostNodes", "Set", "styleNodesInDOM", "collectServerRenderedStyles", "platformIsServer", "resetHostNodes", "addStyles", "styles", "style", "usageCount", "changeUsageCount", "onStyleAdded", "removeStyles", "onStyleRemoved", "ngOnDestroy", "clear", "getAllStyles", "addHost", "hostNode", "add", "addStyleToHost", "removeHost", "delete", "keys", "elements", "head", "querySelectorAll", "styleMap", "textContent", "delta", "map", "has", "styleRefValue", "usage", "getStyleElement", "styleEl", "removeAttribute", "append<PERSON><PERSON><PERSON>", "styleElRef", "SharedStylesHost_Factory", "Document", "NAMESPACE_URIS", "COMPONENT_REGEX", "COMPONENT_VARIABLE", "HOST_ATTR", "CONTENT_ATTR", "REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT", "REMOVE_STYLES_ON_COMPONENT_DESTROY", "providedIn", "shimContentAttribute", "componentShortId", "replace", "shimHostAttribute", "shim<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "compId", "s", "DomRendererFactory2", "eventManager", "sharedStylesHost", "removeStylesOnCompDestroy", "ngZone", "rendererByCompId", "defaultRenderer", "DefaultDomRenderer2", "<PERSON><PERSON><PERSON><PERSON>", "encapsulation", "ShadowDom", "Emulated", "renderer", "getOr<PERSON><PERSON><PERSON><PERSON><PERSON>", "EmulatedEncapsulationDomRenderer2", "applyToHost", "NoneEncapsulationDomRenderer", "applyStyles", "id", "ShadowDom<PERSON><PERSON><PERSON>", "DomRendererFactory2_Factory", "Object", "data", "create", "destroyNode", "destroy", "namespace", "createElementNS", "createComment", "value", "createText", "createTextNode", "parent", "<PERSON><PERSON><PERSON><PERSON>", "targetParent", "isTemplateNode", "content", "insertBefore", "refChild", "<PERSON><PERSON><PERSON><PERSON>", "selectRootElement", "selectorOrNode", "preserve<PERSON><PERSON>nt", "nextS<PERSON>ling", "namespaceUri", "setAttributeNS", "removeAttributeNS", "addClass", "classList", "removeClass", "setStyle", "flags", "DashCase", "Important", "setProperty", "removeStyle", "removeProperty", "checkNoSyntheticProp", "setValue", "nodeValue", "listen", "event", "Error", "decoratePreventDefault", "<PERSON><PERSON><PERSON><PERSON>", "allowDefaultBehavior", "runGuarded", "preventDefault", "AT_CHARCODE", "charCodeAt", "<PERSON><PERSON><PERSON>", "hostEl", "component", "shadowRoot", "attachShadow", "mode", "nodeOrShadowRoot", "contentAttr", "hostAttr", "DomEventsPlugin", "DomEventsPlugin_Factory", "MODIFIER_KEYS", "_keyMap", "MODIFIER_KEY_GETTERS", "altKey", "ctrl<PERSON>ey", "metaKey", "shift<PERSON>ey", "KeyEventsPlugin", "parseEventName", "parsedEvent", "outsideH<PERSON>ler", "eventCallback", "runOutsideAngular", "parts", "toLowerCase", "split", "domEventName", "shift", "key", "_normalizeKey", "pop", "<PERSON><PERSON><PERSON>", "codeIX", "indexOf", "splice", "modifierName", "index", "result", "matchEventFullKeyCode", "fullKeyCode", "keycode", "code", "modifierGetter", "zone", "keyName", "KeyEventsPlugin_Factory", "bootstrapApplication", "rootComponent", "options", "createProvidersConfig", "createApplication", "appProviders", "BROWSER_MODULE_PROVIDERS", "providers", "platformProviders", "INTERNAL_BROWSER_PLATFORM_PROVIDERS", "provideProtractorTestingSupport", "TESTABILITY_PROVIDERS", "initDomAdapter", "<PERSON><PERSON><PERSON><PERSON>", "_document", "provide", "useValue", "multi", "useFactory", "deps", "platformBrowser", "BROWSER_MODULE_PROVIDERS_MARKER", "useClass", "useExisting", "BrowserModule", "providersAlreadyPresent", "withServerTransition", "params", "ngModule", "BrowserModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "createMeta", "Meta", "_dom", "addTag", "tag", "forceCreation", "_getOrCreateElement", "addTags", "tags", "reduce", "getTag", "attrSelector", "getTags", "list", "call", "updateTag", "selector", "_parseSelector", "meta", "_setMetaElementAttributes", "removeTag", "removeTagElement", "filter", "_containsAttributes", "getElementsByTagName", "prop", "_getMetaKeyMap", "attr", "every", "META_KEYS_MAP", "Meta_Factory", "r", "httpEquiv", "createTitle", "Title", "getTitle", "title", "setTitle", "newTitle", "Title_Factory", "exportNgVar", "COMPILED", "ng", "win", "ChangeDetectionPerfRecord", "msPerTick", "numTicks", "AngularProfiler", "ref", "appRef", "injector", "timeChangeDetection", "config", "record", "profileName", "isProfilerAvailable", "console", "profile", "start", "performanceNow", "tick", "end", "profileEnd", "log", "toFixed", "performance", "now", "Date", "getTime", "PROFILER_GLOBAL_NAME", "enableDebugTools", "disableDebugTools", "By", "all", "css", "debugElement", "nativeElement", "elementMatches", "directive", "debugNode", "providerTokens", "n", "matches", "msMatchesSelector", "webkitMatchesSelector", "EVENT_NAMES", "HAMMER_GESTURE_CONFIG", "HAMMER_LOADER", "HammerGestureConfig", "events", "overrides", "buildHammer", "mc", "Hammer", "enable", "HammerGestureConfig_Factory", "HammerGesturesPlugin", "_config", "loader", "_loaderPromise", "hasOwnProperty", "isCustomEvent", "warn", "cancelRegistration", "deregister", "then", "catch", "eventObj", "on", "off", "HammerGesturesPlugin_Factory", "HammerModule", "HammerModule_Factory", "Dom<PERSON><PERSON><PERSON>zer", "DomSanitizer_Factory", "DomSanitizerImpl", "domSanitizerImplFactory", "sanitize", "ctx", "NONE", "HTML", "String", "toString", "STYLE", "SCRIPT", "URL", "RESOURCE_URL", "bypassSecurityTrustHtml", "bypassSecurityTrustStyle", "bypassSecurityTrustScript", "bypassSecurityTrustUrl", "bypassSecurityTrustResourceUrl", "DomSanitizerImpl_Factory", "hydrationFeature", "kind", "ɵkind", "ɵproviders", "withNoDomReuse", "withNoHttpTransferCache", "provideZoneJsCompatibilityDetector", "message", "provideClientHydration", "features", "featuresKind", "VERSION", "ɵBrowserDomAdapter", "ɵBrowserGetTestability", "ɵDomEventsPlugin", "ɵDomRendererFactory2", "ɵDomSanitizerImpl", "ɵHammerGesturesPlugin", "ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS", "ɵKeyEventsPlugin", "ɵSharedStylesHost", "ɵinitDomAdapter"], "sources": ["C:/cal_wf/Augment/3-img/apqr400bl/node_modules/@angular/platform-browser/fesm2022/platform-browser.mjs"], "sourcesContent": ["/**\n * @license Angular v16.2.12\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { ɵglobal, ɵRuntimeError, Injectable, InjectionToken, Inject, APP_ID, CSP_NONCE, PLATFORM_ID, Optional, ViewEncapsulation, RendererStyleFlags2, ɵinternalCreateApplication, ErrorHandler, ɵsetDocument, PLATFORM_INITIALIZER, createPlatformFactory, platformCore, ɵTESTABILITY_GETTER, ɵTESTABILITY, Testability, NgZone, TestabilityRegistry, ɵINJECTOR_SCOPE, RendererFactory2, ApplicationModule, NgModule, SkipSelf, ɵɵinject, ApplicationRef, ɵConsole, forwardRef, ɵXSS_SECURITY_URL, SecurityContext, ɵallowSanitizationBypassAndThrow, ɵunwrapSafeValue, ɵ_sanitizeUrl, ɵ_sanitizeHtml, ɵbypassSanitizationTrustHtml, ɵbypassSanitizationTrustStyle, ɵbypassSanitizationTrustScript, ɵbypassSanitizationTrustUrl, ɵbypassSanitizationTrustResourceUrl, Injector, ENVIRONMENT_INITIALIZER, inject, ɵformatRuntimeError, makeEnvironmentProviders, ɵwithDomHydration, Version, makeStateKey as makeStateKey$1, TransferState as TransferState$1 } from '@angular/core';\nimport { ɵDomAdapter, ɵsetRootDomAdapter, ɵparseCookieValue, ɵgetDOM, isPlatformServer, DOCUMENT, ɵPLATFORM_BROWSER_ID, XhrFactory, CommonModule } from '@angular/common';\nexport { ɵgetDOM } from '@angular/common';\nimport { ɵwithHttpTransferCache } from '@angular/common/http';\n\n/**\n * Provides DOM operations in any browser environment.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\nclass GenericBrowserDomAdapter extends ɵDomAdapter {\n    constructor() {\n        super(...arguments);\n        this.supportsDOMEvents = true;\n    }\n}\n\n/**\n * A `DomAdapter` powered by full browser DOM APIs.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\n/* tslint:disable:requireParameterType no-console */\nclass BrowserDomAdapter extends GenericBrowserDomAdapter {\n    static makeCurrent() {\n        ɵsetRootDomAdapter(new BrowserDomAdapter());\n    }\n    onAndCancel(el, evt, listener) {\n        el.addEventListener(evt, listener);\n        return () => {\n            el.removeEventListener(evt, listener);\n        };\n    }\n    dispatchEvent(el, evt) {\n        el.dispatchEvent(evt);\n    }\n    remove(node) {\n        if (node.parentNode) {\n            node.parentNode.removeChild(node);\n        }\n    }\n    createElement(tagName, doc) {\n        doc = doc || this.getDefaultDocument();\n        return doc.createElement(tagName);\n    }\n    createHtmlDocument() {\n        return document.implementation.createHTMLDocument('fakeTitle');\n    }\n    getDefaultDocument() {\n        return document;\n    }\n    isElementNode(node) {\n        return node.nodeType === Node.ELEMENT_NODE;\n    }\n    isShadowRoot(node) {\n        return node instanceof DocumentFragment;\n    }\n    /** @deprecated No longer being used in Ivy code. To be removed in version 14. */\n    getGlobalEventTarget(doc, target) {\n        if (target === 'window') {\n            return window;\n        }\n        if (target === 'document') {\n            return doc;\n        }\n        if (target === 'body') {\n            return doc.body;\n        }\n        return null;\n    }\n    getBaseHref(doc) {\n        const href = getBaseElementHref();\n        return href == null ? null : relativePath(href);\n    }\n    resetBaseElement() {\n        baseElement = null;\n    }\n    getUserAgent() {\n        return window.navigator.userAgent;\n    }\n    getCookie(name) {\n        return ɵparseCookieValue(document.cookie, name);\n    }\n}\nlet baseElement = null;\nfunction getBaseElementHref() {\n    baseElement = baseElement || document.querySelector('base');\n    return baseElement ? baseElement.getAttribute('href') : null;\n}\n// based on urlUtils.js in AngularJS 1\nlet urlParsingNode;\nfunction relativePath(url) {\n    urlParsingNode = urlParsingNode || document.createElement('a');\n    urlParsingNode.setAttribute('href', url);\n    const pathName = urlParsingNode.pathname;\n    return pathName.charAt(0) === '/' ? pathName : `/${pathName}`;\n}\n\nclass BrowserGetTestability {\n    addToWindow(registry) {\n        ɵglobal['getAngularTestability'] = (elem, findInAncestors = true) => {\n            const testability = registry.findTestabilityInTree(elem, findInAncestors);\n            if (testability == null) {\n                throw new ɵRuntimeError(5103 /* RuntimeErrorCode.TESTABILITY_NOT_FOUND */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                    'Could not find testability for element.');\n            }\n            return testability;\n        };\n        ɵglobal['getAllAngularTestabilities'] = () => registry.getAllTestabilities();\n        ɵglobal['getAllAngularRootElements'] = () => registry.getAllRootElements();\n        const whenAllStable = (callback) => {\n            const testabilities = ɵglobal['getAllAngularTestabilities']();\n            let count = testabilities.length;\n            let didWork = false;\n            const decrement = function (didWork_) {\n                didWork = didWork || didWork_;\n                count--;\n                if (count == 0) {\n                    callback(didWork);\n                }\n            };\n            testabilities.forEach((testability) => {\n                testability.whenStable(decrement);\n            });\n        };\n        if (!ɵglobal['frameworkStabilizers']) {\n            ɵglobal['frameworkStabilizers'] = [];\n        }\n        ɵglobal['frameworkStabilizers'].push(whenAllStable);\n    }\n    findTestabilityInTree(registry, elem, findInAncestors) {\n        if (elem == null) {\n            return null;\n        }\n        const t = registry.getTestability(elem);\n        if (t != null) {\n            return t;\n        }\n        else if (!findInAncestors) {\n            return null;\n        }\n        if (ɵgetDOM().isShadowRoot(elem)) {\n            return this.findTestabilityInTree(registry, elem.host, true);\n        }\n        return this.findTestabilityInTree(registry, elem.parentElement, true);\n    }\n}\n\n/**\n * A factory for `HttpXhrBackend` that uses the `XMLHttpRequest` browser API.\n */\nclass BrowserXhr {\n    build() {\n        return new XMLHttpRequest();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: BrowserXhr, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: BrowserXhr }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: BrowserXhr, decorators: [{\n            type: Injectable\n        }] });\n\n/**\n * The injection token for the event-manager plug-in service.\n *\n * @publicApi\n */\nconst EVENT_MANAGER_PLUGINS = new InjectionToken('EventManagerPlugins');\n/**\n * An injectable service that provides event management for Angular\n * through a browser plug-in.\n *\n * @publicApi\n */\nclass EventManager {\n    /**\n     * Initializes an instance of the event-manager service.\n     */\n    constructor(plugins, _zone) {\n        this._zone = _zone;\n        this._eventNameToPlugin = new Map();\n        plugins.forEach((plugin) => {\n            plugin.manager = this;\n        });\n        this._plugins = plugins.slice().reverse();\n    }\n    /**\n     * Registers a handler for a specific element and event.\n     *\n     * @param element The HTML element to receive event notifications.\n     * @param eventName The name of the event to listen for.\n     * @param handler A function to call when the notification occurs. Receives the\n     * event object as an argument.\n     * @returns  A callback function that can be used to remove the handler.\n     */\n    addEventListener(element, eventName, handler) {\n        const plugin = this._findPluginFor(eventName);\n        return plugin.addEventListener(element, eventName, handler);\n    }\n    /**\n     * Retrieves the compilation zone in which event listeners are registered.\n     */\n    getZone() {\n        return this._zone;\n    }\n    /** @internal */\n    _findPluginFor(eventName) {\n        let plugin = this._eventNameToPlugin.get(eventName);\n        if (plugin) {\n            return plugin;\n        }\n        const plugins = this._plugins;\n        plugin = plugins.find((plugin) => plugin.supports(eventName));\n        if (!plugin) {\n            throw new ɵRuntimeError(5101 /* RuntimeErrorCode.NO_PLUGIN_FOR_EVENT */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                `No event manager plugin found for event ${eventName}`);\n        }\n        this._eventNameToPlugin.set(eventName, plugin);\n        return plugin;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: EventManager, deps: [{ token: EVENT_MANAGER_PLUGINS }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: EventManager }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: EventManager, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [EVENT_MANAGER_PLUGINS]\n                }] }, { type: i0.NgZone }]; } });\nclass EventManagerPlugin {\n    constructor(_doc) {\n        this._doc = _doc;\n    }\n}\n\n/** The style elements attribute name used to set value of `APP_ID` token. */\nconst APP_ID_ATTRIBUTE_NAME = 'ng-app-id';\nclass SharedStylesHost {\n    constructor(doc, appId, nonce, platformId = {}) {\n        this.doc = doc;\n        this.appId = appId;\n        this.nonce = nonce;\n        this.platformId = platformId;\n        // Maps all registered host nodes to a list of style nodes that have been added to the host node.\n        this.styleRef = new Map();\n        this.hostNodes = new Set();\n        this.styleNodesInDOM = this.collectServerRenderedStyles();\n        this.platformIsServer = isPlatformServer(platformId);\n        this.resetHostNodes();\n    }\n    addStyles(styles) {\n        for (const style of styles) {\n            const usageCount = this.changeUsageCount(style, 1);\n            if (usageCount === 1) {\n                this.onStyleAdded(style);\n            }\n        }\n    }\n    removeStyles(styles) {\n        for (const style of styles) {\n            const usageCount = this.changeUsageCount(style, -1);\n            if (usageCount <= 0) {\n                this.onStyleRemoved(style);\n            }\n        }\n    }\n    ngOnDestroy() {\n        const styleNodesInDOM = this.styleNodesInDOM;\n        if (styleNodesInDOM) {\n            styleNodesInDOM.forEach((node) => node.remove());\n            styleNodesInDOM.clear();\n        }\n        for (const style of this.getAllStyles()) {\n            this.onStyleRemoved(style);\n        }\n        this.resetHostNodes();\n    }\n    addHost(hostNode) {\n        this.hostNodes.add(hostNode);\n        for (const style of this.getAllStyles()) {\n            this.addStyleToHost(hostNode, style);\n        }\n    }\n    removeHost(hostNode) {\n        this.hostNodes.delete(hostNode);\n    }\n    getAllStyles() {\n        return this.styleRef.keys();\n    }\n    onStyleAdded(style) {\n        for (const host of this.hostNodes) {\n            this.addStyleToHost(host, style);\n        }\n    }\n    onStyleRemoved(style) {\n        const styleRef = this.styleRef;\n        styleRef.get(style)?.elements?.forEach((node) => node.remove());\n        styleRef.delete(style);\n    }\n    collectServerRenderedStyles() {\n        const styles = this.doc.head?.querySelectorAll(`style[${APP_ID_ATTRIBUTE_NAME}=\"${this.appId}\"]`);\n        if (styles?.length) {\n            const styleMap = new Map();\n            styles.forEach((style) => {\n                if (style.textContent != null) {\n                    styleMap.set(style.textContent, style);\n                }\n            });\n            return styleMap;\n        }\n        return null;\n    }\n    changeUsageCount(style, delta) {\n        const map = this.styleRef;\n        if (map.has(style)) {\n            const styleRefValue = map.get(style);\n            styleRefValue.usage += delta;\n            return styleRefValue.usage;\n        }\n        map.set(style, { usage: delta, elements: [] });\n        return delta;\n    }\n    getStyleElement(host, style) {\n        const styleNodesInDOM = this.styleNodesInDOM;\n        const styleEl = styleNodesInDOM?.get(style);\n        if (styleEl?.parentNode === host) {\n            // `styleNodesInDOM` cannot be undefined due to the above `styleNodesInDOM?.get`.\n            styleNodesInDOM.delete(style);\n            styleEl.removeAttribute(APP_ID_ATTRIBUTE_NAME);\n            if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                // This attribute is solely used for debugging purposes.\n                styleEl.setAttribute('ng-style-reused', '');\n            }\n            return styleEl;\n        }\n        else {\n            const styleEl = this.doc.createElement('style');\n            if (this.nonce) {\n                styleEl.setAttribute('nonce', this.nonce);\n            }\n            styleEl.textContent = style;\n            if (this.platformIsServer) {\n                styleEl.setAttribute(APP_ID_ATTRIBUTE_NAME, this.appId);\n            }\n            return styleEl;\n        }\n    }\n    addStyleToHost(host, style) {\n        const styleEl = this.getStyleElement(host, style);\n        host.appendChild(styleEl);\n        const styleRef = this.styleRef;\n        const styleElRef = styleRef.get(style)?.elements;\n        if (styleElRef) {\n            styleElRef.push(styleEl);\n        }\n        else {\n            styleRef.set(style, { elements: [styleEl], usage: 1 });\n        }\n    }\n    resetHostNodes() {\n        const hostNodes = this.hostNodes;\n        hostNodes.clear();\n        // Re-add the head element back since this is the default host.\n        hostNodes.add(this.doc.head);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: SharedStylesHost, deps: [{ token: DOCUMENT }, { token: APP_ID }, { token: CSP_NONCE, optional: true }, { token: PLATFORM_ID }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: SharedStylesHost }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: SharedStylesHost, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: Document, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [APP_ID]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [CSP_NONCE]\n                }, {\n                    type: Optional\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }]; } });\n\nconst NAMESPACE_URIS = {\n    'svg': 'http://www.w3.org/2000/svg',\n    'xhtml': 'http://www.w3.org/1999/xhtml',\n    'xlink': 'http://www.w3.org/1999/xlink',\n    'xml': 'http://www.w3.org/XML/1998/namespace',\n    'xmlns': 'http://www.w3.org/2000/xmlns/',\n    'math': 'http://www.w3.org/1998/MathML/',\n};\nconst COMPONENT_REGEX = /%COMP%/g;\nconst COMPONENT_VARIABLE = '%COMP%';\nconst HOST_ATTR = `_nghost-${COMPONENT_VARIABLE}`;\nconst CONTENT_ATTR = `_ngcontent-${COMPONENT_VARIABLE}`;\n/**\n * The default value for the `REMOVE_STYLES_ON_COMPONENT_DESTROY` DI token.\n */\nconst REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT = false;\n/**\n * A [DI token](guide/glossary#di-token \"DI token definition\") that indicates whether styles\n * of destroyed components should be removed from DOM.\n *\n * By default, the value is set to `false`. This will be changed in the next major version.\n * @publicApi\n */\nconst REMOVE_STYLES_ON_COMPONENT_DESTROY = new InjectionToken('RemoveStylesOnCompDestroy', {\n    providedIn: 'root',\n    factory: () => REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT,\n});\nfunction shimContentAttribute(componentShortId) {\n    return CONTENT_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\nfunction shimHostAttribute(componentShortId) {\n    return HOST_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\nfunction shimStylesContent(compId, styles) {\n    return styles.map(s => s.replace(COMPONENT_REGEX, compId));\n}\nclass DomRendererFactory2 {\n    constructor(eventManager, sharedStylesHost, appId, removeStylesOnCompDestroy, doc, platformId, ngZone, nonce = null) {\n        this.eventManager = eventManager;\n        this.sharedStylesHost = sharedStylesHost;\n        this.appId = appId;\n        this.removeStylesOnCompDestroy = removeStylesOnCompDestroy;\n        this.doc = doc;\n        this.platformId = platformId;\n        this.ngZone = ngZone;\n        this.nonce = nonce;\n        this.rendererByCompId = new Map();\n        this.platformIsServer = isPlatformServer(platformId);\n        this.defaultRenderer =\n            new DefaultDomRenderer2(eventManager, doc, ngZone, this.platformIsServer);\n    }\n    createRenderer(element, type) {\n        if (!element || !type) {\n            return this.defaultRenderer;\n        }\n        if (this.platformIsServer && type.encapsulation === ViewEncapsulation.ShadowDom) {\n            // Domino does not support shadow DOM.\n            type = { ...type, encapsulation: ViewEncapsulation.Emulated };\n        }\n        const renderer = this.getOrCreateRenderer(element, type);\n        // Renderers have different logic due to different encapsulation behaviours.\n        // Ex: for emulated, an attribute is added to the element.\n        if (renderer instanceof EmulatedEncapsulationDomRenderer2) {\n            renderer.applyToHost(element);\n        }\n        else if (renderer instanceof NoneEncapsulationDomRenderer) {\n            renderer.applyStyles();\n        }\n        return renderer;\n    }\n    getOrCreateRenderer(element, type) {\n        const rendererByCompId = this.rendererByCompId;\n        let renderer = rendererByCompId.get(type.id);\n        if (!renderer) {\n            const doc = this.doc;\n            const ngZone = this.ngZone;\n            const eventManager = this.eventManager;\n            const sharedStylesHost = this.sharedStylesHost;\n            const removeStylesOnCompDestroy = this.removeStylesOnCompDestroy;\n            const platformIsServer = this.platformIsServer;\n            switch (type.encapsulation) {\n                case ViewEncapsulation.Emulated:\n                    renderer = new EmulatedEncapsulationDomRenderer2(eventManager, sharedStylesHost, type, this.appId, removeStylesOnCompDestroy, doc, ngZone, platformIsServer);\n                    break;\n                case ViewEncapsulation.ShadowDom:\n                    return new ShadowDomRenderer(eventManager, sharedStylesHost, element, type, doc, ngZone, this.nonce, platformIsServer);\n                default:\n                    renderer = new NoneEncapsulationDomRenderer(eventManager, sharedStylesHost, type, removeStylesOnCompDestroy, doc, ngZone, platformIsServer);\n                    break;\n            }\n            rendererByCompId.set(type.id, renderer);\n        }\n        return renderer;\n    }\n    ngOnDestroy() {\n        this.rendererByCompId.clear();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: DomRendererFactory2, deps: [{ token: EventManager }, { token: SharedStylesHost }, { token: APP_ID }, { token: REMOVE_STYLES_ON_COMPONENT_DESTROY }, { token: DOCUMENT }, { token: PLATFORM_ID }, { token: i0.NgZone }, { token: CSP_NONCE }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: DomRendererFactory2 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: DomRendererFactory2, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: EventManager }, { type: SharedStylesHost }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [APP_ID]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [REMOVE_STYLES_ON_COMPONENT_DESTROY]\n                }] }, { type: Document, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: Object, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [CSP_NONCE]\n                }] }]; } });\nclass DefaultDomRenderer2 {\n    constructor(eventManager, doc, ngZone, platformIsServer) {\n        this.eventManager = eventManager;\n        this.doc = doc;\n        this.ngZone = ngZone;\n        this.platformIsServer = platformIsServer;\n        this.data = Object.create(null);\n        this.destroyNode = null;\n    }\n    destroy() { }\n    createElement(name, namespace) {\n        if (namespace) {\n            // TODO: `|| namespace` was added in\n            // https://github.com/angular/angular/commit/2b9cc8503d48173492c29f5a271b61126104fbdb to\n            // support how Ivy passed around the namespace URI rather than short name at the time. It did\n            // not, however extend the support to other parts of the system (setAttribute, setAttribute,\n            // and the ServerRenderer). We should decide what exactly the semantics for dealing with\n            // namespaces should be and make it consistent.\n            // Related issues:\n            // https://github.com/angular/angular/issues/44028\n            // https://github.com/angular/angular/issues/44883\n            return this.doc.createElementNS(NAMESPACE_URIS[namespace] || namespace, name);\n        }\n        return this.doc.createElement(name);\n    }\n    createComment(value) {\n        return this.doc.createComment(value);\n    }\n    createText(value) {\n        return this.doc.createTextNode(value);\n    }\n    appendChild(parent, newChild) {\n        const targetParent = isTemplateNode(parent) ? parent.content : parent;\n        targetParent.appendChild(newChild);\n    }\n    insertBefore(parent, newChild, refChild) {\n        if (parent) {\n            const targetParent = isTemplateNode(parent) ? parent.content : parent;\n            targetParent.insertBefore(newChild, refChild);\n        }\n    }\n    removeChild(parent, oldChild) {\n        if (parent) {\n            parent.removeChild(oldChild);\n        }\n    }\n    selectRootElement(selectorOrNode, preserveContent) {\n        let el = typeof selectorOrNode === 'string' ? this.doc.querySelector(selectorOrNode) :\n            selectorOrNode;\n        if (!el) {\n            throw new ɵRuntimeError(-5104 /* RuntimeErrorCode.ROOT_NODE_NOT_FOUND */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                `The selector \"${selectorOrNode}\" did not match any elements`);\n        }\n        if (!preserveContent) {\n            el.textContent = '';\n        }\n        return el;\n    }\n    parentNode(node) {\n        return node.parentNode;\n    }\n    nextSibling(node) {\n        return node.nextSibling;\n    }\n    setAttribute(el, name, value, namespace) {\n        if (namespace) {\n            name = namespace + ':' + name;\n            const namespaceUri = NAMESPACE_URIS[namespace];\n            if (namespaceUri) {\n                el.setAttributeNS(namespaceUri, name, value);\n            }\n            else {\n                el.setAttribute(name, value);\n            }\n        }\n        else {\n            el.setAttribute(name, value);\n        }\n    }\n    removeAttribute(el, name, namespace) {\n        if (namespace) {\n            const namespaceUri = NAMESPACE_URIS[namespace];\n            if (namespaceUri) {\n                el.removeAttributeNS(namespaceUri, name);\n            }\n            else {\n                el.removeAttribute(`${namespace}:${name}`);\n            }\n        }\n        else {\n            el.removeAttribute(name);\n        }\n    }\n    addClass(el, name) {\n        el.classList.add(name);\n    }\n    removeClass(el, name) {\n        el.classList.remove(name);\n    }\n    setStyle(el, style, value, flags) {\n        if (flags & (RendererStyleFlags2.DashCase | RendererStyleFlags2.Important)) {\n            el.style.setProperty(style, value, flags & RendererStyleFlags2.Important ? 'important' : '');\n        }\n        else {\n            el.style[style] = value;\n        }\n    }\n    removeStyle(el, style, flags) {\n        if (flags & RendererStyleFlags2.DashCase) {\n            // removeProperty has no effect when used on camelCased properties.\n            el.style.removeProperty(style);\n        }\n        else {\n            el.style[style] = '';\n        }\n    }\n    setProperty(el, name, value) {\n        (typeof ngDevMode === 'undefined' || ngDevMode) && checkNoSyntheticProp(name, 'property');\n        el[name] = value;\n    }\n    setValue(node, value) {\n        node.nodeValue = value;\n    }\n    listen(target, event, callback) {\n        (typeof ngDevMode === 'undefined' || ngDevMode) && checkNoSyntheticProp(event, 'listener');\n        if (typeof target === 'string') {\n            target = ɵgetDOM().getGlobalEventTarget(this.doc, target);\n            if (!target) {\n                throw new Error(`Unsupported event target ${target} for event ${event}`);\n            }\n        }\n        return this.eventManager.addEventListener(target, event, this.decoratePreventDefault(callback));\n    }\n    decoratePreventDefault(eventHandler) {\n        // `DebugNode.triggerEventHandler` needs to know if the listener was created with\n        // decoratePreventDefault or is a listener added outside the Angular context so it can handle\n        // the two differently. In the first case, the special '__ngUnwrap__' token is passed to the\n        // unwrap the listener (see below).\n        return (event) => {\n            // Ivy uses '__ngUnwrap__' as a special token that allows us to unwrap the function\n            // so that it can be invoked programmatically by `DebugNode.triggerEventHandler`. The\n            // debug_node can inspect the listener toString contents for the existence of this special\n            // token. Because the token is a string literal, it is ensured to not be modified by compiled\n            // code.\n            if (event === '__ngUnwrap__') {\n                return eventHandler;\n            }\n            // Run the event handler inside the ngZone because event handlers are not patched\n            // by Zone on the server. This is required only for tests.\n            const allowDefaultBehavior = this.platformIsServer ?\n                this.ngZone.runGuarded(() => eventHandler(event)) :\n                eventHandler(event);\n            if (allowDefaultBehavior === false) {\n                event.preventDefault();\n            }\n            return undefined;\n        };\n    }\n}\nconst AT_CHARCODE = (() => '@'.charCodeAt(0))();\nfunction checkNoSyntheticProp(name, nameKind) {\n    if (name.charCodeAt(0) === AT_CHARCODE) {\n        throw new ɵRuntimeError(5105 /* RuntimeErrorCode.UNEXPECTED_SYNTHETIC_PROPERTY */, `Unexpected synthetic ${nameKind} ${name} found. Please make sure that:\n  - Either \\`BrowserAnimationsModule\\` or \\`NoopAnimationsModule\\` are imported in your application.\n  - There is corresponding configuration for the animation named \\`${name}\\` defined in the \\`animations\\` field of the \\`@Component\\` decorator (see https://angular.io/api/core/Component#animations).`);\n    }\n}\nfunction isTemplateNode(node) {\n    return node.tagName === 'TEMPLATE' && node.content !== undefined;\n}\nclass ShadowDomRenderer extends DefaultDomRenderer2 {\n    constructor(eventManager, sharedStylesHost, hostEl, component, doc, ngZone, nonce, platformIsServer) {\n        super(eventManager, doc, ngZone, platformIsServer);\n        this.sharedStylesHost = sharedStylesHost;\n        this.hostEl = hostEl;\n        this.shadowRoot = hostEl.attachShadow({ mode: 'open' });\n        this.sharedStylesHost.addHost(this.shadowRoot);\n        const styles = shimStylesContent(component.id, component.styles);\n        for (const style of styles) {\n            const styleEl = document.createElement('style');\n            if (nonce) {\n                styleEl.setAttribute('nonce', nonce);\n            }\n            styleEl.textContent = style;\n            this.shadowRoot.appendChild(styleEl);\n        }\n    }\n    nodeOrShadowRoot(node) {\n        return node === this.hostEl ? this.shadowRoot : node;\n    }\n    appendChild(parent, newChild) {\n        return super.appendChild(this.nodeOrShadowRoot(parent), newChild);\n    }\n    insertBefore(parent, newChild, refChild) {\n        return super.insertBefore(this.nodeOrShadowRoot(parent), newChild, refChild);\n    }\n    removeChild(parent, oldChild) {\n        return super.removeChild(this.nodeOrShadowRoot(parent), oldChild);\n    }\n    parentNode(node) {\n        return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(node)));\n    }\n    destroy() {\n        this.sharedStylesHost.removeHost(this.shadowRoot);\n    }\n}\nclass NoneEncapsulationDomRenderer extends DefaultDomRenderer2 {\n    constructor(eventManager, sharedStylesHost, component, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, compId) {\n        super(eventManager, doc, ngZone, platformIsServer);\n        this.sharedStylesHost = sharedStylesHost;\n        this.removeStylesOnCompDestroy = removeStylesOnCompDestroy;\n        this.styles = compId ? shimStylesContent(compId, component.styles) : component.styles;\n    }\n    applyStyles() {\n        this.sharedStylesHost.addStyles(this.styles);\n    }\n    destroy() {\n        if (!this.removeStylesOnCompDestroy) {\n            return;\n        }\n        this.sharedStylesHost.removeStyles(this.styles);\n    }\n}\nclass EmulatedEncapsulationDomRenderer2 extends NoneEncapsulationDomRenderer {\n    constructor(eventManager, sharedStylesHost, component, appId, removeStylesOnCompDestroy, doc, ngZone, platformIsServer) {\n        const compId = appId + '-' + component.id;\n        super(eventManager, sharedStylesHost, component, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, compId);\n        this.contentAttr = shimContentAttribute(compId);\n        this.hostAttr = shimHostAttribute(compId);\n    }\n    applyToHost(element) {\n        this.applyStyles();\n        this.setAttribute(element, this.hostAttr, '');\n    }\n    createElement(parent, name) {\n        const el = super.createElement(parent, name);\n        super.setAttribute(el, this.contentAttr, '');\n        return el;\n    }\n}\n\nclass DomEventsPlugin extends EventManagerPlugin {\n    constructor(doc) {\n        super(doc);\n    }\n    // This plugin should come last in the list of plugins, because it accepts all\n    // events.\n    supports(eventName) {\n        return true;\n    }\n    addEventListener(element, eventName, handler) {\n        element.addEventListener(eventName, handler, false);\n        return () => this.removeEventListener(element, eventName, handler);\n    }\n    removeEventListener(target, eventName, callback) {\n        return target.removeEventListener(eventName, callback);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: DomEventsPlugin, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: DomEventsPlugin }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: DomEventsPlugin, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\n/**\n * Defines supported modifiers for key events.\n */\nconst MODIFIER_KEYS = ['alt', 'control', 'meta', 'shift'];\n// The following values are here for cross-browser compatibility and to match the W3C standard\n// cf https://www.w3.org/TR/DOM-Level-3-Events-key/\nconst _keyMap = {\n    '\\b': 'Backspace',\n    '\\t': 'Tab',\n    '\\x7F': 'Delete',\n    '\\x1B': 'Escape',\n    'Del': 'Delete',\n    'Esc': 'Escape',\n    'Left': 'ArrowLeft',\n    'Right': 'ArrowRight',\n    'Up': 'ArrowUp',\n    'Down': 'ArrowDown',\n    'Menu': 'ContextMenu',\n    'Scroll': 'ScrollLock',\n    'Win': 'OS'\n};\n/**\n * Retrieves modifiers from key-event objects.\n */\nconst MODIFIER_KEY_GETTERS = {\n    'alt': (event) => event.altKey,\n    'control': (event) => event.ctrlKey,\n    'meta': (event) => event.metaKey,\n    'shift': (event) => event.shiftKey\n};\n/**\n * @publicApi\n * A browser plug-in that provides support for handling of key events in Angular.\n */\nclass KeyEventsPlugin extends EventManagerPlugin {\n    /**\n     * Initializes an instance of the browser plug-in.\n     * @param doc The document in which key events will be detected.\n     */\n    constructor(doc) {\n        super(doc);\n    }\n    /**\n     * Reports whether a named key event is supported.\n     * @param eventName The event name to query.\n     * @return True if the named key event is supported.\n     */\n    supports(eventName) {\n        return KeyEventsPlugin.parseEventName(eventName) != null;\n    }\n    /**\n     * Registers a handler for a specific element and key event.\n     * @param element The HTML element to receive event notifications.\n     * @param eventName The name of the key event to listen for.\n     * @param handler A function to call when the notification occurs. Receives the\n     * event object as an argument.\n     * @returns The key event that was registered.\n     */\n    addEventListener(element, eventName, handler) {\n        const parsedEvent = KeyEventsPlugin.parseEventName(eventName);\n        const outsideHandler = KeyEventsPlugin.eventCallback(parsedEvent['fullKey'], handler, this.manager.getZone());\n        return this.manager.getZone().runOutsideAngular(() => {\n            return ɵgetDOM().onAndCancel(element, parsedEvent['domEventName'], outsideHandler);\n        });\n    }\n    /**\n     * Parses the user provided full keyboard event definition and normalizes it for\n     * later internal use. It ensures the string is all lowercase, converts special\n     * characters to a standard spelling, and orders all the values consistently.\n     *\n     * @param eventName The name of the key event to listen for.\n     * @returns an object with the full, normalized string, and the dom event name\n     * or null in the case when the event doesn't match a keyboard event.\n     */\n    static parseEventName(eventName) {\n        const parts = eventName.toLowerCase().split('.');\n        const domEventName = parts.shift();\n        if ((parts.length === 0) || !(domEventName === 'keydown' || domEventName === 'keyup')) {\n            return null;\n        }\n        const key = KeyEventsPlugin._normalizeKey(parts.pop());\n        let fullKey = '';\n        let codeIX = parts.indexOf('code');\n        if (codeIX > -1) {\n            parts.splice(codeIX, 1);\n            fullKey = 'code.';\n        }\n        MODIFIER_KEYS.forEach(modifierName => {\n            const index = parts.indexOf(modifierName);\n            if (index > -1) {\n                parts.splice(index, 1);\n                fullKey += modifierName + '.';\n            }\n        });\n        fullKey += key;\n        if (parts.length != 0 || key.length === 0) {\n            // returning null instead of throwing to let another plugin process the event\n            return null;\n        }\n        // NOTE: Please don't rewrite this as so, as it will break JSCompiler property renaming.\n        //       The code must remain in the `result['domEventName']` form.\n        // return {domEventName, fullKey};\n        const result = {};\n        result['domEventName'] = domEventName;\n        result['fullKey'] = fullKey;\n        return result;\n    }\n    /**\n     * Determines whether the actual keys pressed match the configured key code string.\n     * The `fullKeyCode` event is normalized in the `parseEventName` method when the\n     * event is attached to the DOM during the `addEventListener` call. This is unseen\n     * by the end user and is normalized for internal consistency and parsing.\n     *\n     * @param event The keyboard event.\n     * @param fullKeyCode The normalized user defined expected key event string\n     * @returns boolean.\n     */\n    static matchEventFullKeyCode(event, fullKeyCode) {\n        let keycode = _keyMap[event.key] || event.key;\n        let key = '';\n        if (fullKeyCode.indexOf('code.') > -1) {\n            keycode = event.code;\n            key = 'code.';\n        }\n        // the keycode could be unidentified so we have to check here\n        if (keycode == null || !keycode)\n            return false;\n        keycode = keycode.toLowerCase();\n        if (keycode === ' ') {\n            keycode = 'space'; // for readability\n        }\n        else if (keycode === '.') {\n            keycode = 'dot'; // because '.' is used as a separator in event names\n        }\n        MODIFIER_KEYS.forEach(modifierName => {\n            if (modifierName !== keycode) {\n                const modifierGetter = MODIFIER_KEY_GETTERS[modifierName];\n                if (modifierGetter(event)) {\n                    key += modifierName + '.';\n                }\n            }\n        });\n        key += keycode;\n        return key === fullKeyCode;\n    }\n    /**\n     * Configures a handler callback for a key event.\n     * @param fullKey The event name that combines all simultaneous keystrokes.\n     * @param handler The function that responds to the key event.\n     * @param zone The zone in which the event occurred.\n     * @returns A callback function.\n     */\n    static eventCallback(fullKey, handler, zone) {\n        return (event) => {\n            if (KeyEventsPlugin.matchEventFullKeyCode(event, fullKey)) {\n                zone.runGuarded(() => handler(event));\n            }\n        };\n    }\n    /** @internal */\n    static _normalizeKey(keyName) {\n        // TODO: switch to a Map if the mapping grows too much\n        switch (keyName) {\n            case 'esc':\n                return 'escape';\n            default:\n                return keyName;\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: KeyEventsPlugin, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: KeyEventsPlugin }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: KeyEventsPlugin, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\n/**\n * Bootstraps an instance of an Angular application and renders a standalone component as the\n * application's root component. More information about standalone components can be found in [this\n * guide](guide/standalone-components).\n *\n * @usageNotes\n * The root component passed into this function *must* be a standalone one (should have the\n * `standalone: true` flag in the `@Component` decorator config).\n *\n * ```typescript\n * @Component({\n *   standalone: true,\n *   template: 'Hello world!'\n * })\n * class RootComponent {}\n *\n * const appRef: ApplicationRef = await bootstrapApplication(RootComponent);\n * ```\n *\n * You can add the list of providers that should be available in the application injector by\n * specifying the `providers` field in an object passed as the second argument:\n *\n * ```typescript\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     {provide: BACKEND_URL, useValue: 'https://yourdomain.com/api'}\n *   ]\n * });\n * ```\n *\n * The `importProvidersFrom` helper method can be used to collect all providers from any\n * existing NgModule (and transitively from all NgModules that it imports):\n *\n * ```typescript\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     importProvidersFrom(SomeNgModule)\n *   ]\n * });\n * ```\n *\n * Note: the `bootstrapApplication` method doesn't include [Testability](api/core/Testability) by\n * default. You can add [Testability](api/core/Testability) by getting the list of necessary\n * providers using `provideProtractorTestingSupport()` function and adding them into the `providers`\n * array, for example:\n *\n * ```typescript\n * import {provideProtractorTestingSupport} from '@angular/platform-browser';\n *\n * await bootstrapApplication(RootComponent, {providers: [provideProtractorTestingSupport()]});\n * ```\n *\n * @param rootComponent A reference to a standalone component that should be rendered.\n * @param options Extra configuration for the bootstrap operation, see `ApplicationConfig` for\n *     additional info.\n * @returns A promise that returns an `ApplicationRef` instance once resolved.\n *\n * @publicApi\n */\nfunction bootstrapApplication(rootComponent, options) {\n    return ɵinternalCreateApplication({ rootComponent, ...createProvidersConfig(options) });\n}\n/**\n * Create an instance of an Angular application without bootstrapping any components. This is useful\n * for the situation where one wants to decouple application environment creation (a platform and\n * associated injectors) from rendering components on a screen. Components can be subsequently\n * bootstrapped on the returned `ApplicationRef`.\n *\n * @param options Extra configuration for the application environment, see `ApplicationConfig` for\n *     additional info.\n * @returns A promise that returns an `ApplicationRef` instance once resolved.\n *\n * @publicApi\n */\nfunction createApplication(options) {\n    return ɵinternalCreateApplication(createProvidersConfig(options));\n}\nfunction createProvidersConfig(options) {\n    return {\n        appProviders: [\n            ...BROWSER_MODULE_PROVIDERS,\n            ...(options?.providers ?? []),\n        ],\n        platformProviders: INTERNAL_BROWSER_PLATFORM_PROVIDERS\n    };\n}\n/**\n * Returns a set of providers required to setup [Testability](api/core/Testability) for an\n * application bootstrapped using the `bootstrapApplication` function. The set of providers is\n * needed to support testing an application with Protractor (which relies on the Testability APIs\n * to be present).\n *\n * @returns An array of providers required to setup Testability for an application and make it\n *     available for testing using Protractor.\n *\n * @publicApi\n */\nfunction provideProtractorTestingSupport() {\n    // Return a copy to prevent changes to the original array in case any in-place\n    // alterations are performed to the `provideProtractorTestingSupport` call results in app\n    // code.\n    return [...TESTABILITY_PROVIDERS];\n}\nfunction initDomAdapter() {\n    BrowserDomAdapter.makeCurrent();\n}\nfunction errorHandler() {\n    return new ErrorHandler();\n}\nfunction _document() {\n    // Tell ivy about the global document\n    ɵsetDocument(document);\n    return document;\n}\nconst INTERNAL_BROWSER_PLATFORM_PROVIDERS = [\n    { provide: PLATFORM_ID, useValue: ɵPLATFORM_BROWSER_ID },\n    { provide: PLATFORM_INITIALIZER, useValue: initDomAdapter, multi: true },\n    { provide: DOCUMENT, useFactory: _document, deps: [] },\n];\n/**\n * A factory function that returns a `PlatformRef` instance associated with browser service\n * providers.\n *\n * @publicApi\n */\nconst platformBrowser = createPlatformFactory(platformCore, 'browser', INTERNAL_BROWSER_PLATFORM_PROVIDERS);\n/**\n * Internal marker to signal whether providers from the `BrowserModule` are already present in DI.\n * This is needed to avoid loading `BrowserModule` providers twice. We can't rely on the\n * `BrowserModule` presence itself, since the standalone-based bootstrap just imports\n * `BrowserModule` providers without referencing the module itself.\n */\nconst BROWSER_MODULE_PROVIDERS_MARKER = new InjectionToken((typeof ngDevMode === 'undefined' || ngDevMode) ? 'BrowserModule Providers Marker' : '');\nconst TESTABILITY_PROVIDERS = [\n    {\n        provide: ɵTESTABILITY_GETTER,\n        useClass: BrowserGetTestability,\n        deps: [],\n    },\n    {\n        provide: ɵTESTABILITY,\n        useClass: Testability,\n        deps: [NgZone, TestabilityRegistry, ɵTESTABILITY_GETTER]\n    },\n    {\n        provide: Testability,\n        useClass: Testability,\n        deps: [NgZone, TestabilityRegistry, ɵTESTABILITY_GETTER]\n    }\n];\nconst BROWSER_MODULE_PROVIDERS = [\n    { provide: ɵINJECTOR_SCOPE, useValue: 'root' },\n    { provide: ErrorHandler, useFactory: errorHandler, deps: [] }, {\n        provide: EVENT_MANAGER_PLUGINS,\n        useClass: DomEventsPlugin,\n        multi: true,\n        deps: [DOCUMENT, NgZone, PLATFORM_ID]\n    },\n    { provide: EVENT_MANAGER_PLUGINS, useClass: KeyEventsPlugin, multi: true, deps: [DOCUMENT] },\n    DomRendererFactory2, SharedStylesHost, EventManager,\n    { provide: RendererFactory2, useExisting: DomRendererFactory2 },\n    { provide: XhrFactory, useClass: BrowserXhr, deps: [] },\n    (typeof ngDevMode === 'undefined' || ngDevMode) ?\n        { provide: BROWSER_MODULE_PROVIDERS_MARKER, useValue: true } :\n        []\n];\n/**\n * Exports required infrastructure for all Angular apps.\n * Included by default in all Angular apps created with the CLI\n * `new` command.\n * Re-exports `CommonModule` and `ApplicationModule`, making their\n * exports and providers available to all apps.\n *\n * @publicApi\n */\nclass BrowserModule {\n    constructor(providersAlreadyPresent) {\n        if ((typeof ngDevMode === 'undefined' || ngDevMode) && providersAlreadyPresent) {\n            throw new ɵRuntimeError(5100 /* RuntimeErrorCode.BROWER_MODULE_ALREADY_LOADED */, `Providers from the \\`BrowserModule\\` have already been loaded. If you need access ` +\n                `to common directives such as NgIf and NgFor, import the \\`CommonModule\\` instead.`);\n        }\n    }\n    /**\n     * Configures a browser-based app to transition from a server-rendered app, if\n     * one is present on the page.\n     *\n     * @param params An object containing an identifier for the app to transition.\n     * The ID must match between the client and server versions of the app.\n     * @returns The reconfigured `BrowserModule` to import into the app's root `AppModule`.\n     *\n     * @deprecated Use {@link APP_ID} instead to set the application ID.\n     */\n    static withServerTransition(params) {\n        return {\n            ngModule: BrowserModule,\n            providers: [\n                { provide: APP_ID, useValue: params.appId },\n            ],\n        };\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: BrowserModule, deps: [{ token: BROWSER_MODULE_PROVIDERS_MARKER, optional: true, skipSelf: true }], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.2.12\", ngImport: i0, type: BrowserModule, exports: [CommonModule, ApplicationModule] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: BrowserModule, providers: [...BROWSER_MODULE_PROVIDERS, ...TESTABILITY_PROVIDERS], imports: [CommonModule, ApplicationModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: BrowserModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [...BROWSER_MODULE_PROVIDERS, ...TESTABILITY_PROVIDERS],\n                    exports: [CommonModule, ApplicationModule],\n                }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: SkipSelf\n                }, {\n                    type: Inject,\n                    args: [BROWSER_MODULE_PROVIDERS_MARKER]\n                }] }]; } });\n\n/**\n * Factory to create a `Meta` service instance for the current DOM document.\n */\nfunction createMeta() {\n    return new Meta(ɵɵinject(DOCUMENT));\n}\n/**\n * A service for managing HTML `<meta>` tags.\n *\n * Properties of the `MetaDefinition` object match the attributes of the\n * HTML `<meta>` tag. These tags define document metadata that is important for\n * things like configuring a Content Security Policy, defining browser compatibility\n * and security settings, setting HTTP Headers, defining rich content for social sharing,\n * and Search Engine Optimization (SEO).\n *\n * To identify specific `<meta>` tags in a document, use an attribute selection\n * string in the format `\"tag_attribute='value string'\"`.\n * For example, an `attrSelector` value of `\"name='description'\"` matches a tag\n * whose `name` attribute has the value `\"description\"`.\n * Selectors are used with the `querySelector()` Document method,\n * in the format `meta[{attrSelector}]`.\n *\n * @see [HTML meta tag](https://developer.mozilla.org/docs/Web/HTML/Element/meta)\n * @see [Document.querySelector()](https://developer.mozilla.org/docs/Web/API/Document/querySelector)\n *\n *\n * @publicApi\n */\nclass Meta {\n    constructor(_doc) {\n        this._doc = _doc;\n        this._dom = ɵgetDOM();\n    }\n    /**\n     * Retrieves or creates a specific `<meta>` tag element in the current HTML document.\n     * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n     * values in the provided tag definition, and verifies that all other attribute values are equal.\n     * If an existing element is found, it is returned and is not modified in any way.\n     * @param tag The definition of a `<meta>` element to match or create.\n     * @param forceCreation True to create a new element without checking whether one already exists.\n     * @returns The existing element with the same attributes and values if found,\n     * the new element if no match is found, or `null` if the tag parameter is not defined.\n     */\n    addTag(tag, forceCreation = false) {\n        if (!tag)\n            return null;\n        return this._getOrCreateElement(tag, forceCreation);\n    }\n    /**\n     * Retrieves or creates a set of `<meta>` tag elements in the current HTML document.\n     * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n     * values in the provided tag definition, and verifies that all other attribute values are equal.\n     * @param tags An array of tag definitions to match or create.\n     * @param forceCreation True to create new elements without checking whether they already exist.\n     * @returns The matching elements if found, or the new elements.\n     */\n    addTags(tags, forceCreation = false) {\n        if (!tags)\n            return [];\n        return tags.reduce((result, tag) => {\n            if (tag) {\n                result.push(this._getOrCreateElement(tag, forceCreation));\n            }\n            return result;\n        }, []);\n    }\n    /**\n     * Retrieves a `<meta>` tag element in the current HTML document.\n     * @param attrSelector The tag attribute and value to match against, in the format\n     * `\"tag_attribute='value string'\"`.\n     * @returns The matching element, if any.\n     */\n    getTag(attrSelector) {\n        if (!attrSelector)\n            return null;\n        return this._doc.querySelector(`meta[${attrSelector}]`) || null;\n    }\n    /**\n     * Retrieves a set of `<meta>` tag elements in the current HTML document.\n     * @param attrSelector The tag attribute and value to match against, in the format\n     * `\"tag_attribute='value string'\"`.\n     * @returns The matching elements, if any.\n     */\n    getTags(attrSelector) {\n        if (!attrSelector)\n            return [];\n        const list /*NodeList*/ = this._doc.querySelectorAll(`meta[${attrSelector}]`);\n        return list ? [].slice.call(list) : [];\n    }\n    /**\n     * Modifies an existing `<meta>` tag element in the current HTML document.\n     * @param tag The tag description with which to replace the existing tag content.\n     * @param selector A tag attribute and value to match against, to identify\n     * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n     * If not supplied, matches a tag with the same `name` or `property` attribute value as the\n     * replacement tag.\n     * @return The modified element.\n     */\n    updateTag(tag, selector) {\n        if (!tag)\n            return null;\n        selector = selector || this._parseSelector(tag);\n        const meta = this.getTag(selector);\n        if (meta) {\n            return this._setMetaElementAttributes(tag, meta);\n        }\n        return this._getOrCreateElement(tag, true);\n    }\n    /**\n     * Removes an existing `<meta>` tag element from the current HTML document.\n     * @param attrSelector A tag attribute and value to match against, to identify\n     * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n     */\n    removeTag(attrSelector) {\n        this.removeTagElement(this.getTag(attrSelector));\n    }\n    /**\n     * Removes an existing `<meta>` tag element from the current HTML document.\n     * @param meta The tag definition to match against to identify an existing tag.\n     */\n    removeTagElement(meta) {\n        if (meta) {\n            this._dom.remove(meta);\n        }\n    }\n    _getOrCreateElement(meta, forceCreation = false) {\n        if (!forceCreation) {\n            const selector = this._parseSelector(meta);\n            // It's allowed to have multiple elements with the same name so it's not enough to\n            // just check that element with the same name already present on the page. We also need to\n            // check if element has tag attributes\n            const elem = this.getTags(selector).filter(elem => this._containsAttributes(meta, elem))[0];\n            if (elem !== undefined)\n                return elem;\n        }\n        const element = this._dom.createElement('meta');\n        this._setMetaElementAttributes(meta, element);\n        const head = this._doc.getElementsByTagName('head')[0];\n        head.appendChild(element);\n        return element;\n    }\n    _setMetaElementAttributes(tag, el) {\n        Object.keys(tag).forEach((prop) => el.setAttribute(this._getMetaKeyMap(prop), tag[prop]));\n        return el;\n    }\n    _parseSelector(tag) {\n        const attr = tag.name ? 'name' : 'property';\n        return `${attr}=\"${tag[attr]}\"`;\n    }\n    _containsAttributes(tag, elem) {\n        return Object.keys(tag).every((key) => elem.getAttribute(this._getMetaKeyMap(key)) === tag[key]);\n    }\n    _getMetaKeyMap(prop) {\n        return META_KEYS_MAP[prop] || prop;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: Meta, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: Meta, providedIn: 'root', useFactory: createMeta, deps: [] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: Meta, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root', useFactory: createMeta, deps: [] }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n/**\n * Mapping for MetaDefinition properties with their correct meta attribute names\n */\nconst META_KEYS_MAP = {\n    httpEquiv: 'http-equiv'\n};\n\n/**\n * Factory to create Title service.\n */\nfunction createTitle() {\n    return new Title(ɵɵinject(DOCUMENT));\n}\n/**\n * A service that can be used to get and set the title of a current HTML document.\n *\n * Since an Angular application can't be bootstrapped on the entire HTML document (`<html>` tag)\n * it is not possible to bind to the `text` property of the `HTMLTitleElement` elements\n * (representing the `<title>` tag). Instead, this service can be used to set and get the current\n * title value.\n *\n * @publicApi\n */\nclass Title {\n    constructor(_doc) {\n        this._doc = _doc;\n    }\n    /**\n     * Get the title of the current HTML document.\n     */\n    getTitle() {\n        return this._doc.title;\n    }\n    /**\n     * Set the title of the current HTML document.\n     * @param newTitle\n     */\n    setTitle(newTitle) {\n        this._doc.title = newTitle || '';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: Title, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: Title, providedIn: 'root', useFactory: createTitle, deps: [] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: Title, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root', useFactory: createTitle, deps: [] }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\n/**\n * Exports the value under a given `name` in the global property `ng`. For example `ng.probe` if\n * `name` is `'probe'`.\n * @param name Name under which it will be exported. Keep in mind this will be a property of the\n * global `ng` object.\n * @param value The value to export.\n */\nfunction exportNgVar(name, value) {\n    if (typeof COMPILED === 'undefined' || !COMPILED) {\n        // Note: we can't export `ng` when using closure enhanced optimization as:\n        // - closure declares globals itself for minified names, which sometimes clobber our `ng` global\n        // - we can't declare a closure extern as the namespace `ng` is already used within Google\n        //   for typings for angularJS (via `goog.provide('ng....')`).\n        const ng = ɵglobal['ng'] = ɵglobal['ng'] || {};\n        ng[name] = value;\n    }\n}\n\nconst win = typeof window !== 'undefined' && window || {};\n\nclass ChangeDetectionPerfRecord {\n    constructor(msPerTick, numTicks) {\n        this.msPerTick = msPerTick;\n        this.numTicks = numTicks;\n    }\n}\n/**\n * Entry point for all Angular profiling-related debug tools. This object\n * corresponds to the `ng.profiler` in the dev console.\n */\nclass AngularProfiler {\n    constructor(ref) {\n        this.appRef = ref.injector.get(ApplicationRef);\n    }\n    // tslint:disable:no-console\n    /**\n     * Exercises change detection in a loop and then prints the average amount of\n     * time in milliseconds how long a single round of change detection takes for\n     * the current state of the UI. It runs a minimum of 5 rounds for a minimum\n     * of 500 milliseconds.\n     *\n     * Optionally, a user may pass a `config` parameter containing a map of\n     * options. Supported options are:\n     *\n     * `record` (boolean) - causes the profiler to record a CPU profile while\n     * it exercises the change detector. Example:\n     *\n     * ```\n     * ng.profiler.timeChangeDetection({record: true})\n     * ```\n     */\n    timeChangeDetection(config) {\n        const record = config && config['record'];\n        const profileName = 'Change Detection';\n        // Profiler is not available in Android browsers without dev tools opened\n        const isProfilerAvailable = win.console.profile != null;\n        if (record && isProfilerAvailable) {\n            win.console.profile(profileName);\n        }\n        const start = performanceNow();\n        let numTicks = 0;\n        while (numTicks < 5 || (performanceNow() - start) < 500) {\n            this.appRef.tick();\n            numTicks++;\n        }\n        const end = performanceNow();\n        if (record && isProfilerAvailable) {\n            win.console.profileEnd(profileName);\n        }\n        const msPerTick = (end - start) / numTicks;\n        win.console.log(`ran ${numTicks} change detection cycles`);\n        win.console.log(`${msPerTick.toFixed(2)} ms per check`);\n        return new ChangeDetectionPerfRecord(msPerTick, numTicks);\n    }\n}\nfunction performanceNow() {\n    return win.performance && win.performance.now ? win.performance.now() :\n        new Date().getTime();\n}\n\nconst PROFILER_GLOBAL_NAME = 'profiler';\n/**\n * Enabled Angular debug tools that are accessible via your browser's\n * developer console.\n *\n * Usage:\n *\n * 1. Open developer console (e.g. in Chrome Ctrl + Shift + j)\n * 1. Type `ng.` (usually the console will show auto-complete suggestion)\n * 1. Try the change detection profiler `ng.profiler.timeChangeDetection()`\n *    then hit Enter.\n *\n * @publicApi\n */\nfunction enableDebugTools(ref) {\n    exportNgVar(PROFILER_GLOBAL_NAME, new AngularProfiler(ref));\n    return ref;\n}\n/**\n * Disables Angular tools.\n *\n * @publicApi\n */\nfunction disableDebugTools() {\n    exportNgVar(PROFILER_GLOBAL_NAME, null);\n}\n\n/**\n * Predicates for use with {@link DebugElement}'s query functions.\n *\n * @publicApi\n */\nclass By {\n    /**\n     * Match all nodes.\n     *\n     * @usageNotes\n     * ### Example\n     *\n     * {@example platform-browser/dom/debug/ts/by/by.ts region='by_all'}\n     */\n    static all() {\n        return () => true;\n    }\n    /**\n     * Match elements by the given CSS selector.\n     *\n     * @usageNotes\n     * ### Example\n     *\n     * {@example platform-browser/dom/debug/ts/by/by.ts region='by_css'}\n     */\n    static css(selector) {\n        return (debugElement) => {\n            return debugElement.nativeElement != null ?\n                elementMatches(debugElement.nativeElement, selector) :\n                false;\n        };\n    }\n    /**\n     * Match nodes that have the given directive present.\n     *\n     * @usageNotes\n     * ### Example\n     *\n     * {@example platform-browser/dom/debug/ts/by/by.ts region='by_directive'}\n     */\n    static directive(type) {\n        return (debugNode) => debugNode.providerTokens.indexOf(type) !== -1;\n    }\n}\nfunction elementMatches(n, selector) {\n    if (ɵgetDOM().isElementNode(n)) {\n        return n.matches && n.matches(selector) ||\n            n.msMatchesSelector && n.msMatchesSelector(selector) ||\n            n.webkitMatchesSelector && n.webkitMatchesSelector(selector);\n    }\n    return false;\n}\n\n/**\n * Supported HammerJS recognizer event names.\n */\nconst EVENT_NAMES = {\n    // pan\n    'pan': true,\n    'panstart': true,\n    'panmove': true,\n    'panend': true,\n    'pancancel': true,\n    'panleft': true,\n    'panright': true,\n    'panup': true,\n    'pandown': true,\n    // pinch\n    'pinch': true,\n    'pinchstart': true,\n    'pinchmove': true,\n    'pinchend': true,\n    'pinchcancel': true,\n    'pinchin': true,\n    'pinchout': true,\n    // press\n    'press': true,\n    'pressup': true,\n    // rotate\n    'rotate': true,\n    'rotatestart': true,\n    'rotatemove': true,\n    'rotateend': true,\n    'rotatecancel': true,\n    // swipe\n    'swipe': true,\n    'swipeleft': true,\n    'swiperight': true,\n    'swipeup': true,\n    'swipedown': true,\n    // tap\n    'tap': true,\n    'doubletap': true\n};\n/**\n * DI token for providing [HammerJS](https://hammerjs.github.io/) support to Angular.\n * @see {@link HammerGestureConfig}\n *\n * @ngModule HammerModule\n * @publicApi\n */\nconst HAMMER_GESTURE_CONFIG = new InjectionToken('HammerGestureConfig');\n/**\n * Injection token used to provide a {@link HammerLoader} to Angular.\n *\n * @publicApi\n */\nconst HAMMER_LOADER = new InjectionToken('HammerLoader');\n/**\n * An injectable [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n * for gesture recognition. Configures specific event recognition.\n * @publicApi\n */\nclass HammerGestureConfig {\n    constructor() {\n        /**\n         * A set of supported event names for gestures to be used in Angular.\n         * Angular supports all built-in recognizers, as listed in\n         * [HammerJS documentation](https://hammerjs.github.io/).\n         */\n        this.events = [];\n        /**\n         * Maps gesture event names to a set of configuration options\n         * that specify overrides to the default values for specific properties.\n         *\n         * The key is a supported event name to be configured,\n         * and the options object contains a set of properties, with override values\n         * to be applied to the named recognizer event.\n         * For example, to disable recognition of the rotate event, specify\n         *  `{\"rotate\": {\"enable\": false}}`.\n         *\n         * Properties that are not present take the HammerJS default values.\n         * For information about which properties are supported for which events,\n         * and their allowed and default values, see\n         * [HammerJS documentation](https://hammerjs.github.io/).\n         *\n         */\n        this.overrides = {};\n    }\n    /**\n     * Creates a [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n     * and attaches it to a given HTML element.\n     * @param element The element that will recognize gestures.\n     * @returns A HammerJS event-manager object.\n     */\n    buildHammer(element) {\n        const mc = new Hammer(element, this.options);\n        mc.get('pinch').set({ enable: true });\n        mc.get('rotate').set({ enable: true });\n        for (const eventName in this.overrides) {\n            mc.get(eventName).set(this.overrides[eventName]);\n        }\n        return mc;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: HammerGestureConfig, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: HammerGestureConfig }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: HammerGestureConfig, decorators: [{\n            type: Injectable\n        }] });\n/**\n * Event plugin that adds Hammer support to an application.\n *\n * @ngModule HammerModule\n */\nclass HammerGesturesPlugin extends EventManagerPlugin {\n    constructor(doc, _config, console, loader) {\n        super(doc);\n        this._config = _config;\n        this.console = console;\n        this.loader = loader;\n        this._loaderPromise = null;\n    }\n    supports(eventName) {\n        if (!EVENT_NAMES.hasOwnProperty(eventName.toLowerCase()) && !this.isCustomEvent(eventName)) {\n            return false;\n        }\n        if (!window.Hammer && !this.loader) {\n            if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                this.console.warn(`The \"${eventName}\" event cannot be bound because Hammer.JS is not ` +\n                    `loaded and no custom loader has been specified.`);\n            }\n            return false;\n        }\n        return true;\n    }\n    addEventListener(element, eventName, handler) {\n        const zone = this.manager.getZone();\n        eventName = eventName.toLowerCase();\n        // If Hammer is not present but a loader is specified, we defer adding the event listener\n        // until Hammer is loaded.\n        if (!window.Hammer && this.loader) {\n            this._loaderPromise = this._loaderPromise || zone.runOutsideAngular(() => this.loader());\n            // This `addEventListener` method returns a function to remove the added listener.\n            // Until Hammer is loaded, the returned function needs to *cancel* the registration rather\n            // than remove anything.\n            let cancelRegistration = false;\n            let deregister = () => {\n                cancelRegistration = true;\n            };\n            zone.runOutsideAngular(() => this._loaderPromise\n                .then(() => {\n                // If Hammer isn't actually loaded when the custom loader resolves, give up.\n                if (!window.Hammer) {\n                    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                        this.console.warn(`The custom HAMMER_LOADER completed, but Hammer.JS is not present.`);\n                    }\n                    deregister = () => { };\n                    return;\n                }\n                if (!cancelRegistration) {\n                    // Now that Hammer is loaded and the listener is being loaded for real,\n                    // the deregistration function changes from canceling registration to\n                    // removal.\n                    deregister = this.addEventListener(element, eventName, handler);\n                }\n            })\n                .catch(() => {\n                if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                    this.console.warn(`The \"${eventName}\" event cannot be bound because the custom ` +\n                        `Hammer.JS loader failed.`);\n                }\n                deregister = () => { };\n            }));\n            // Return a function that *executes* `deregister` (and not `deregister` itself) so that we\n            // can change the behavior of `deregister` once the listener is added. Using a closure in\n            // this way allows us to avoid any additional data structures to track listener removal.\n            return () => {\n                deregister();\n            };\n        }\n        return zone.runOutsideAngular(() => {\n            // Creating the manager bind events, must be done outside of angular\n            const mc = this._config.buildHammer(element);\n            const callback = function (eventObj) {\n                zone.runGuarded(function () {\n                    handler(eventObj);\n                });\n            };\n            mc.on(eventName, callback);\n            return () => {\n                mc.off(eventName, callback);\n                // destroy mc to prevent memory leak\n                if (typeof mc.destroy === 'function') {\n                    mc.destroy();\n                }\n            };\n        });\n    }\n    isCustomEvent(eventName) {\n        return this._config.events.indexOf(eventName) > -1;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: HammerGesturesPlugin, deps: [{ token: DOCUMENT }, { token: HAMMER_GESTURE_CONFIG }, { token: i0.ɵConsole }, { token: HAMMER_LOADER, optional: true }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: HammerGesturesPlugin }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: HammerGesturesPlugin, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: HammerGestureConfig, decorators: [{\n                    type: Inject,\n                    args: [HAMMER_GESTURE_CONFIG]\n                }] }, { type: i0.ɵConsole }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [HAMMER_LOADER]\n                }] }]; } });\n/**\n * Adds support for HammerJS.\n *\n * Import this module at the root of your application so that Angular can work with\n * HammerJS to detect gesture events.\n *\n * Note that applications still need to include the HammerJS script itself. This module\n * simply sets up the coordination layer between HammerJS and Angular's EventManager.\n *\n * @publicApi\n */\nclass HammerModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: HammerModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.2.12\", ngImport: i0, type: HammerModule }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: HammerModule, providers: [\n            {\n                provide: EVENT_MANAGER_PLUGINS,\n                useClass: HammerGesturesPlugin,\n                multi: true,\n                deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, ɵConsole, [new Optional(), HAMMER_LOADER]]\n            },\n            { provide: HAMMER_GESTURE_CONFIG, useClass: HammerGestureConfig, deps: [] },\n        ] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: HammerModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [\n                        {\n                            provide: EVENT_MANAGER_PLUGINS,\n                            useClass: HammerGesturesPlugin,\n                            multi: true,\n                            deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, ɵConsole, [new Optional(), HAMMER_LOADER]]\n                        },\n                        { provide: HAMMER_GESTURE_CONFIG, useClass: HammerGestureConfig, deps: [] },\n                    ]\n                }]\n        }] });\n\n/**\n * DomSanitizer helps preventing Cross Site Scripting Security bugs (XSS) by sanitizing\n * values to be safe to use in the different DOM contexts.\n *\n * For example, when binding a URL in an `<a [href]=\"someValue\">` hyperlink, `someValue` will be\n * sanitized so that an attacker cannot inject e.g. a `javascript:` URL that would execute code on\n * the website.\n *\n * In specific situations, it might be necessary to disable sanitization, for example if the\n * application genuinely needs to produce a `javascript:` style link with a dynamic value in it.\n * Users can bypass security by constructing a value with one of the `bypassSecurityTrust...`\n * methods, and then binding to that value from the template.\n *\n * These situations should be very rare, and extraordinary care must be taken to avoid creating a\n * Cross Site Scripting (XSS) security bug!\n *\n * When using `bypassSecurityTrust...`, make sure to call the method as early as possible and as\n * close as possible to the source of the value, to make it easy to verify no security bug is\n * created by its use.\n *\n * It is not required (and not recommended) to bypass security if the value is safe, e.g. a URL that\n * does not start with a suspicious protocol, or an HTML snippet that does not contain dangerous\n * code. The sanitizer leaves safe values intact.\n *\n * @security Calling any of the `bypassSecurityTrust...` APIs disables Angular's built-in\n * sanitization for the value passed in. Carefully check and audit all values and code paths going\n * into this call. Make sure any user data is appropriately escaped for this security context.\n * For more detail, see the [Security Guide](https://g.co/ng/security).\n *\n * @publicApi\n */\nclass DomSanitizer {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: DomSanitizer, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: DomSanitizer, providedIn: 'root', useExisting: i0.forwardRef(function () { return DomSanitizerImpl; }) }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: DomSanitizer, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root', useExisting: forwardRef(() => DomSanitizerImpl) }]\n        }] });\nfunction domSanitizerImplFactory(injector) {\n    return new DomSanitizerImpl(injector.get(DOCUMENT));\n}\nclass DomSanitizerImpl extends DomSanitizer {\n    constructor(_doc) {\n        super();\n        this._doc = _doc;\n    }\n    sanitize(ctx, value) {\n        if (value == null)\n            return null;\n        switch (ctx) {\n            case SecurityContext.NONE:\n                return value;\n            case SecurityContext.HTML:\n                if (ɵallowSanitizationBypassAndThrow(value, \"HTML\" /* BypassType.Html */)) {\n                    return ɵunwrapSafeValue(value);\n                }\n                return ɵ_sanitizeHtml(this._doc, String(value)).toString();\n            case SecurityContext.STYLE:\n                if (ɵallowSanitizationBypassAndThrow(value, \"Style\" /* BypassType.Style */)) {\n                    return ɵunwrapSafeValue(value);\n                }\n                return value;\n            case SecurityContext.SCRIPT:\n                if (ɵallowSanitizationBypassAndThrow(value, \"Script\" /* BypassType.Script */)) {\n                    return ɵunwrapSafeValue(value);\n                }\n                throw new ɵRuntimeError(5200 /* RuntimeErrorCode.SANITIZATION_UNSAFE_SCRIPT */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                    'unsafe value used in a script context');\n            case SecurityContext.URL:\n                if (ɵallowSanitizationBypassAndThrow(value, \"URL\" /* BypassType.Url */)) {\n                    return ɵunwrapSafeValue(value);\n                }\n                return ɵ_sanitizeUrl(String(value));\n            case SecurityContext.RESOURCE_URL:\n                if (ɵallowSanitizationBypassAndThrow(value, \"ResourceURL\" /* BypassType.ResourceUrl */)) {\n                    return ɵunwrapSafeValue(value);\n                }\n                throw new ɵRuntimeError(5201 /* RuntimeErrorCode.SANITIZATION_UNSAFE_RESOURCE_URL */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                    `unsafe value used in a resource URL context (see ${ɵXSS_SECURITY_URL})`);\n            default:\n                throw new ɵRuntimeError(5202 /* RuntimeErrorCode.SANITIZATION_UNEXPECTED_CTX */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                    `Unexpected SecurityContext ${ctx} (see ${ɵXSS_SECURITY_URL})`);\n        }\n    }\n    bypassSecurityTrustHtml(value) {\n        return ɵbypassSanitizationTrustHtml(value);\n    }\n    bypassSecurityTrustStyle(value) {\n        return ɵbypassSanitizationTrustStyle(value);\n    }\n    bypassSecurityTrustScript(value) {\n        return ɵbypassSanitizationTrustScript(value);\n    }\n    bypassSecurityTrustUrl(value) {\n        return ɵbypassSanitizationTrustUrl(value);\n    }\n    bypassSecurityTrustResourceUrl(value) {\n        return ɵbypassSanitizationTrustResourceUrl(value);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: DomSanitizerImpl, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: DomSanitizerImpl, providedIn: 'root', useFactory: domSanitizerImplFactory, deps: [{ token: Injector }] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: DomSanitizerImpl, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root', useFactory: domSanitizerImplFactory, deps: [Injector] }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\n/**\n * Helper function to create an object that represents a Hydration feature.\n */\nfunction hydrationFeature(kind, providers = []) {\n    return { ɵkind: kind, ɵproviders: providers };\n}\n/**\n * Disables DOM nodes reuse during hydration. Effectively makes\n * Angular re-render an application from scratch on the client.\n *\n * When this option is enabled, make sure that the initial navigation\n * option is configured for the Router as `enabledBlocking` by using the\n * `withEnabledBlockingInitialNavigation` in the `provideRouter` call:\n *\n * ```\n * bootstrapApplication(RootComponent, {\n *   providers: [\n *     provideRouter(\n *       // ... other features ...\n *       withEnabledBlockingInitialNavigation()\n *     ),\n *     provideClientHydration(withNoDomReuse())\n *   ]\n * });\n * ```\n *\n * This would ensure that the application is rerendered after all async\n * operations in the Router (such as lazy-loading of components,\n * waiting for async guards and resolvers) are completed to avoid\n * clearing the DOM on the client too soon, thus causing content flicker.\n *\n * @see {@link provideRouter}\n * @see {@link withEnabledBlockingInitialNavigation}\n *\n * @publicApi\n * @developerPreview\n */\nfunction withNoDomReuse() {\n    // This feature has no providers and acts as a flag that turns off\n    // non-destructive hydration (which otherwise is turned on by default).\n    return hydrationFeature(0 /* HydrationFeatureKind.NoDomReuseFeature */);\n}\n/**\n * Disables HTTP transfer cache. Effectively causes HTTP requests to be performed twice: once on the\n * server and other one on the browser.\n *\n * @publicApi\n * @developerPreview\n */\nfunction withNoHttpTransferCache() {\n    // This feature has no providers and acts as a flag that turns off\n    // HTTP transfer cache (which otherwise is turned on by default).\n    return hydrationFeature(1 /* HydrationFeatureKind.NoHttpTransferCache */);\n}\n/**\n * Returns an `ENVIRONMENT_INITIALIZER` token setup with a function\n * that verifies whether compatible ZoneJS was used in an application\n * and logs a warning in a console if it's not the case.\n */\nfunction provideZoneJsCompatibilityDetector() {\n    return [{\n            provide: ENVIRONMENT_INITIALIZER,\n            useValue: () => {\n                const ngZone = inject(NgZone);\n                // Checking `ngZone instanceof NgZone` would be insufficient here,\n                // because custom implementations might use NgZone as a base class.\n                if (ngZone.constructor !== NgZone) {\n                    const console = inject(ɵConsole);\n                    const message = ɵformatRuntimeError(-5000 /* RuntimeErrorCode.UNSUPPORTED_ZONEJS_INSTANCE */, 'Angular detected that hydration was enabled for an application ' +\n                        'that uses a custom or a noop Zone.js implementation. ' +\n                        'This is not yet a fully supported configuration.');\n                    // tslint:disable-next-line:no-console\n                    console.warn(message);\n                }\n            },\n            multi: true,\n        }];\n}\n/**\n * Sets up providers necessary to enable hydration functionality for the application.\n *\n * By default, the function enables the recommended set of features for the optimal\n * performance for most of the applications. You can enable/disable features by\n * passing special functions (from the `HydrationFeatures` set) as arguments to the\n * `provideClientHydration` function. It includes the following features:\n *\n * * Reconciling DOM hydration. Learn more about it [here](guide/hydration).\n * * [`HttpClient`](api/common/http/HttpClient) response caching while running on the server and\n * transferring this cache to the client to avoid extra HTTP requests. Learn more about data caching\n * [here](/guide/universal#caching-data-when-using-httpclient).\n *\n * These functions functions will allow you to disable some of the default features:\n * * {@link withNoDomReuse} to disable DOM nodes reuse during hydration\n * * {@link withNoHttpTransferCache} to disable HTTP transfer cache\n *\n *\n * @usageNotes\n *\n * Basic example of how you can enable hydration in your application when\n * `bootstrapApplication` function is used:\n * ```\n * bootstrapApplication(AppComponent, {\n *   providers: [provideClientHydration()]\n * });\n * ```\n *\n * Alternatively if you are using NgModules, you would add `provideClientHydration`\n * to your root app module's provider list.\n * ```\n * @NgModule({\n *   declarations: [RootCmp],\n *   bootstrap: [RootCmp],\n *   providers: [provideClientHydration()],\n * })\n * export class AppModule {}\n * ```\n *\n * @see {@link withNoDomReuse}\n * @see {@link withNoHttpTransferCache}\n *\n * @param features Optional features to configure additional router behaviors.\n * @returns A set of providers to enable hydration.\n *\n * @publicApi\n * @developerPreview\n */\nfunction provideClientHydration(...features) {\n    const providers = [];\n    const featuresKind = new Set();\n    for (const { ɵproviders, ɵkind } of features) {\n        featuresKind.add(ɵkind);\n        if (ɵproviders.length) {\n            providers.push(ɵproviders);\n        }\n    }\n    return makeEnvironmentProviders([\n        (typeof ngDevMode !== 'undefined' && ngDevMode) ? provideZoneJsCompatibilityDetector() : [],\n        (featuresKind.has(0 /* HydrationFeatureKind.NoDomReuseFeature */) ? [] : ɵwithDomHydration()),\n        (featuresKind.has(1 /* HydrationFeatureKind.NoHttpTransferCache */) ? [] : ɵwithHttpTransferCache()),\n        providers,\n    ]);\n}\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser package.\n */\n/**\n * @publicApi\n */\nconst VERSION = new Version('16.2.12');\n\n// Re-export TransferState to the public API of the `platform-browser` for backwards-compatibility.\n/**\n * Create a `StateKey<T>` that can be used to store value of type T with `TransferState`.\n *\n * Example:\n *\n * ```\n * const COUNTER_KEY = makeStateKey<number>('counter');\n * let value = 10;\n *\n * transferState.set(COUNTER_KEY, value);\n * ```\n *\n * @publicApi\n * @deprecated `makeStateKey` has moved, please import `makeStateKey` from `@angular/core` instead.\n */\n// The below is a workaround to add a deprecated message.\nconst makeStateKey = makeStateKey$1;\n// The below type is needed for G3 due to JSC_CONFORMANCE_VIOLATION.\nconst TransferState = TransferState$1;\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\n// This file only reexports content of the `src` folder. Keep it that way.\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BrowserModule, By, DomSanitizer, EVENT_MANAGER_PLUGINS, EventManager, HAMMER_GESTURE_CONFIG, HAMMER_LOADER, HammerGestureConfig, HammerModule, Meta, REMOVE_STYLES_ON_COMPONENT_DESTROY, Title, TransferState, VERSION, bootstrapApplication, createApplication, disableDebugTools, enableDebugTools, makeStateKey, platformBrowser, provideClientHydration, provideProtractorTestingSupport, withNoDomReuse, withNoHttpTransferCache, BrowserDomAdapter as ɵBrowserDomAdapter, BrowserGetTestability as ɵBrowserGetTestability, DomEventsPlugin as ɵDomEventsPlugin, DomRendererFactory2 as ɵDomRendererFactory2, DomSanitizerImpl as ɵDomSanitizerImpl, HammerGesturesPlugin as ɵHammerGesturesPlugin, INTERNAL_BROWSER_PLATFORM_PROVIDERS as ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS, KeyEventsPlugin as ɵKeyEventsPlugin, SharedStylesHost as ɵSharedStylesHost, initDomAdapter as ɵinitDomAdapter };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,OAAO,EAAEC,aAAa,EAAEC,UAAU,EAAEC,cAAc,EAAEC,MAAM,EAAEC,MAAM,EAAEC,SAAS,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,iBAAiB,EAAEC,mBAAmB,EAAEC,0BAA0B,EAAEC,YAAY,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,qBAAqB,EAAEC,YAAY,EAAEC,mBAAmB,EAAEC,YAAY,EAAEC,WAAW,EAAEC,MAAM,EAAEC,mBAAmB,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,iBAAiB,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,iBAAiB,EAAEC,eAAe,EAAEC,gCAAgC,EAAEC,gBAAgB,EAAEC,aAAa,EAAEC,cAAc,EAAEC,4BAA4B,EAAEC,6BAA6B,EAAEC,8BAA8B,EAAEC,2BAA2B,EAAEC,mCAAmC,EAAEC,QAAQ,EAAEC,uBAAuB,EAAEC,MAAM,EAAEC,mBAAmB,EAAEC,wBAAwB,EAAEC,iBAAiB,EAAEC,OAAO,EAAEC,YAAY,IAAIC,cAAc,EAAEC,aAAa,IAAIC,eAAe,QAAQ,eAAe;AACp7B,SAASC,WAAW,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAEC,OAAO,EAAEC,gBAAgB,EAAEC,QAAQ,EAAEC,oBAAoB,EAAEC,UAAU,EAAEC,YAAY,QAAQ,iBAAiB;AACzK,SAASL,OAAO,QAAQ,iBAAiB;AACzC,SAASM,sBAAsB,QAAQ,sBAAsB;;AAE7D;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,wBAAwB,SAASV,WAAW,CAAC;EAC/CW,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,iBAAiB,GAAG,IAAI;EACjC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,SAASJ,wBAAwB,CAAC;EACrD,OAAOK,WAAWA,CAAA,EAAG;IACjBd,kBAAkB,CAAC,IAAIa,iBAAiB,CAAC,CAAC,CAAC;EAC/C;EACAE,WAAWA,CAACC,EAAE,EAAEC,GAAG,EAAEC,QAAQ,EAAE;IAC3BF,EAAE,CAACG,gBAAgB,CAACF,GAAG,EAAEC,QAAQ,CAAC;IAClC,OAAO,MAAM;MACTF,EAAE,CAACI,mBAAmB,CAACH,GAAG,EAAEC,QAAQ,CAAC;IACzC,CAAC;EACL;EACAG,aAAaA,CAACL,EAAE,EAAEC,GAAG,EAAE;IACnBD,EAAE,CAACK,aAAa,CAACJ,GAAG,CAAC;EACzB;EACAK,MAAMA,CAACC,IAAI,EAAE;IACT,IAAIA,IAAI,CAACC,UAAU,EAAE;MACjBD,IAAI,CAACC,UAAU,CAACC,WAAW,CAACF,IAAI,CAAC;IACrC;EACJ;EACAG,aAAaA,CAACC,OAAO,EAAEC,GAAG,EAAE;IACxBA,GAAG,GAAGA,GAAG,IAAI,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACtC,OAAOD,GAAG,CAACF,aAAa,CAACC,OAAO,CAAC;EACrC;EACAG,kBAAkBA,CAAA,EAAG;IACjB,OAAOC,QAAQ,CAACC,cAAc,CAACC,kBAAkB,CAAC,WAAW,CAAC;EAClE;EACAJ,kBAAkBA,CAAA,EAAG;IACjB,OAAOE,QAAQ;EACnB;EACAG,aAAaA,CAACX,IAAI,EAAE;IAChB,OAAOA,IAAI,CAACY,QAAQ,KAAKC,IAAI,CAACC,YAAY;EAC9C;EACAC,YAAYA,CAACf,IAAI,EAAE;IACf,OAAOA,IAAI,YAAYgB,gBAAgB;EAC3C;EACA;EACAC,oBAAoBA,CAACZ,GAAG,EAAEa,MAAM,EAAE;IAC9B,IAAIA,MAAM,KAAK,QAAQ,EAAE;MACrB,OAAOC,MAAM;IACjB;IACA,IAAID,MAAM,KAAK,UAAU,EAAE;MACvB,OAAOb,GAAG;IACd;IACA,IAAIa,MAAM,KAAK,MAAM,EAAE;MACnB,OAAOb,GAAG,CAACe,IAAI;IACnB;IACA,OAAO,IAAI;EACf;EACAC,WAAWA,CAAChB,GAAG,EAAE;IACb,MAAMiB,IAAI,GAAGC,kBAAkB,CAAC,CAAC;IACjC,OAAOD,IAAI,IAAI,IAAI,GAAG,IAAI,GAAGE,YAAY,CAACF,IAAI,CAAC;EACnD;EACAG,gBAAgBA,CAAA,EAAG;IACfC,WAAW,GAAG,IAAI;EACtB;EACAC,YAAYA,CAAA,EAAG;IACX,OAAOR,MAAM,CAACS,SAAS,CAACC,SAAS;EACrC;EACAC,SAASA,CAACC,IAAI,EAAE;IACZ,OAAOrD,iBAAiB,CAAC8B,QAAQ,CAACwB,MAAM,EAAED,IAAI,CAAC;EACnD;AACJ;AACA,IAAIL,WAAW,GAAG,IAAI;AACtB,SAASH,kBAAkBA,CAAA,EAAG;EAC1BG,WAAW,GAAGA,WAAW,IAAIlB,QAAQ,CAACyB,aAAa,CAAC,MAAM,CAAC;EAC3D,OAAOP,WAAW,GAAGA,WAAW,CAACQ,YAAY,CAAC,MAAM,CAAC,GAAG,IAAI;AAChE;AACA;AACA,IAAIC,cAAc;AAClB,SAASX,YAAYA,CAACY,GAAG,EAAE;EACvBD,cAAc,GAAGA,cAAc,IAAI3B,QAAQ,CAACL,aAAa,CAAC,GAAG,CAAC;EAC9DgC,cAAc,CAACE,YAAY,CAAC,MAAM,EAAED,GAAG,CAAC;EACxC,MAAME,QAAQ,GAAGH,cAAc,CAACI,QAAQ;EACxC,OAAOD,QAAQ,CAACE,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,GAAGF,QAAQ,GAAI,IAAGA,QAAS,EAAC;AACjE;AAEA,MAAMG,qBAAqB,CAAC;EACxBC,WAAWA,CAACC,QAAQ,EAAE;IAClBxH,OAAO,CAAC,uBAAuB,CAAC,GAAG,CAACyH,IAAI,EAAEC,eAAe,GAAG,IAAI,KAAK;MACjE,MAAMC,WAAW,GAAGH,QAAQ,CAACI,qBAAqB,CAACH,IAAI,EAAEC,eAAe,CAAC;MACzE,IAAIC,WAAW,IAAI,IAAI,EAAE;QACrB,MAAM,IAAI1H,aAAa,CAAC,IAAI,CAAC,8CAA8C,CAAC,OAAO4H,SAAS,KAAK,WAAW,IAAIA,SAAS,KACrH,yCAAyC,CAAC;MAClD;MACA,OAAOF,WAAW;IACtB,CAAC;IACD3H,OAAO,CAAC,4BAA4B,CAAC,GAAG,MAAMwH,QAAQ,CAACM,mBAAmB,CAAC,CAAC;IAC5E9H,OAAO,CAAC,2BAA2B,CAAC,GAAG,MAAMwH,QAAQ,CAACO,kBAAkB,CAAC,CAAC;IAC1E,MAAMC,aAAa,GAAIC,QAAQ,IAAK;MAChC,MAAMC,aAAa,GAAGlI,OAAO,CAAC,4BAA4B,CAAC,CAAC,CAAC;MAC7D,IAAImI,KAAK,GAAGD,aAAa,CAACE,MAAM;MAChC,IAAIC,OAAO,GAAG,KAAK;MACnB,MAAMC,SAAS,GAAG,SAAAA,CAAUC,QAAQ,EAAE;QAClCF,OAAO,GAAGA,OAAO,IAAIE,QAAQ;QAC7BJ,KAAK,EAAE;QACP,IAAIA,KAAK,IAAI,CAAC,EAAE;UACZF,QAAQ,CAACI,OAAO,CAAC;QACrB;MACJ,CAAC;MACDH,aAAa,CAACM,OAAO,CAAEb,WAAW,IAAK;QACnCA,WAAW,CAACc,UAAU,CAACH,SAAS,CAAC;MACrC,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAACtI,OAAO,CAAC,sBAAsB,CAAC,EAAE;MAClCA,OAAO,CAAC,sBAAsB,CAAC,GAAG,EAAE;IACxC;IACAA,OAAO,CAAC,sBAAsB,CAAC,CAAC0I,IAAI,CAACV,aAAa,CAAC;EACvD;EACAJ,qBAAqBA,CAACJ,QAAQ,EAAEC,IAAI,EAAEC,eAAe,EAAE;IACnD,IAAID,IAAI,IAAI,IAAI,EAAE;MACd,OAAO,IAAI;IACf;IACA,MAAMkB,CAAC,GAAGnB,QAAQ,CAACoB,cAAc,CAACnB,IAAI,CAAC;IACvC,IAAIkB,CAAC,IAAI,IAAI,EAAE;MACX,OAAOA,CAAC;IACZ,CAAC,MACI,IAAI,CAACjB,eAAe,EAAE;MACvB,OAAO,IAAI;IACf;IACA,IAAIlE,OAAO,CAAC,CAAC,CAACoC,YAAY,CAAC6B,IAAI,CAAC,EAAE;MAC9B,OAAO,IAAI,CAACG,qBAAqB,CAACJ,QAAQ,EAAEC,IAAI,CAACoB,IAAI,EAAE,IAAI,CAAC;IAChE;IACA,OAAO,IAAI,CAACjB,qBAAqB,CAACJ,QAAQ,EAAEC,IAAI,CAACqB,aAAa,EAAE,IAAI,CAAC;EACzE;AACJ;;AAEA;AACA;AACA;AACA,MAAMC,UAAU,CAAC;EACbC,KAAKA,CAAA,EAAG;IACJ,OAAO,IAAIC,cAAc,CAAC,CAAC;EAC/B;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,mBAAAR,CAAA;MAAA,YAAAA,CAAA,IAAyFI,UAAU;IAAA,CAAoD;EAAE;EAC3K;IAAS,IAAI,CAACK,KAAK,kBAD8ErJ,EAAE,CAAAsJ,kBAAA;MAAAC,KAAA,EACYP,UAAU;MAAAQ,OAAA,EAAVR,UAAU,CAAAG;IAAA,EAAG;EAAE;AAClI;AACA;EAAA,QAAArB,SAAA,oBAAAA,SAAA,KAHqG9H,EAAE,CAAAyJ,iBAAA,CAGXT,UAAU,EAAc,CAAC;IACzGU,IAAI,EAAEvJ;EACV,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA,MAAMwJ,qBAAqB,GAAG,IAAIvJ,cAAc,CAAC,qBAAqB,CAAC;AACvE;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwJ,YAAY,CAAC;EACf;AACJ;AACA;EACI3F,WAAWA,CAAC4F,OAAO,EAAEC,KAAK,EAAE;IACxB,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,kBAAkB,GAAG,IAAIC,GAAG,CAAC,CAAC;IACnCH,OAAO,CAACpB,OAAO,CAAEwB,MAAM,IAAK;MACxBA,MAAM,CAACC,OAAO,GAAG,IAAI;IACzB,CAAC,CAAC;IACF,IAAI,CAACC,QAAQ,GAAGN,OAAO,CAACO,KAAK,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;EAC7C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI3F,gBAAgBA,CAAC4F,OAAO,EAAEC,SAAS,EAAEC,OAAO,EAAE;IAC1C,MAAMP,MAAM,GAAG,IAAI,CAACQ,cAAc,CAACF,SAAS,CAAC;IAC7C,OAAON,MAAM,CAACvF,gBAAgB,CAAC4F,OAAO,EAAEC,SAAS,EAAEC,OAAO,CAAC;EAC/D;EACA;AACJ;AACA;EACIE,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACZ,KAAK;EACrB;EACA;EACAW,cAAcA,CAACF,SAAS,EAAE;IACtB,IAAIN,MAAM,GAAG,IAAI,CAACF,kBAAkB,CAACY,GAAG,CAACJ,SAAS,CAAC;IACnD,IAAIN,MAAM,EAAE;MACR,OAAOA,MAAM;IACjB;IACA,MAAMJ,OAAO,GAAG,IAAI,CAACM,QAAQ;IAC7BF,MAAM,GAAGJ,OAAO,CAACe,IAAI,CAAEX,MAAM,IAAKA,MAAM,CAACY,QAAQ,CAACN,SAAS,CAAC,CAAC;IAC7D,IAAI,CAACN,MAAM,EAAE;MACT,MAAM,IAAI/J,aAAa,CAAC,IAAI,CAAC,4CAA4C,CAAC,OAAO4H,SAAS,KAAK,WAAW,IAAIA,SAAS,KAClH,2CAA0CyC,SAAU,EAAC,CAAC;IAC/D;IACA,IAAI,CAACR,kBAAkB,CAACe,GAAG,CAACP,SAAS,EAAEN,MAAM,CAAC;IAC9C,OAAOA,MAAM;EACjB;EACA;IAAS,IAAI,CAACd,IAAI,YAAA4B,qBAAAnC,CAAA;MAAA,YAAAA,CAAA,IAAyFgB,YAAY,EAjEtB5J,EAAE,CAAA4B,QAAA,CAiEsC+H,qBAAqB,GAjE7D3J,EAAE,CAAA4B,QAAA,CAiEwE5B,EAAE,CAACqB,MAAM;IAAA,CAA6C;EAAE;EACnO;IAAS,IAAI,CAACgI,KAAK,kBAlE8ErJ,EAAE,CAAAsJ,kBAAA;MAAAC,KAAA,EAkEYK,YAAY;MAAAJ,OAAA,EAAZI,YAAY,CAAAT;IAAA,EAAG;EAAE;AACpI;AACA;EAAA,QAAArB,SAAA,oBAAAA,SAAA,KApEqG9H,EAAE,CAAAyJ,iBAAA,CAoEXG,YAAY,EAAc,CAAC;IAC3GF,IAAI,EAAEvJ;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEuJ,IAAI,EAAEsB,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DvB,IAAI,EAAErJ,MAAM;QACZ6K,IAAI,EAAE,CAACvB,qBAAqB;MAChC,CAAC;IAAE,CAAC,EAAE;MAAED,IAAI,EAAE1J,EAAE,CAACqB;IAAO,CAAC,CAAC;EAAE,CAAC;AAAA;AAC7C,MAAM8J,kBAAkB,CAAC;EACrBlH,WAAWA,CAACmH,IAAI,EAAE;IACd,IAAI,CAACA,IAAI,GAAGA,IAAI;EACpB;AACJ;;AAEA;AACA,MAAMC,qBAAqB,GAAG,WAAW;AACzC,MAAMC,gBAAgB,CAAC;EACnBrH,WAAWA,CAACkB,GAAG,EAAEoG,KAAK,EAAEC,KAAK,EAAEC,UAAU,GAAG,CAAC,CAAC,EAAE;IAC5C,IAAI,CAACtG,GAAG,GAAGA,GAAG;IACd,IAAI,CAACoG,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B;IACA,IAAI,CAACC,QAAQ,GAAG,IAAI1B,GAAG,CAAC,CAAC;IACzB,IAAI,CAAC2B,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC1B,IAAI,CAACC,eAAe,GAAG,IAAI,CAACC,2BAA2B,CAAC,CAAC;IACzD,IAAI,CAACC,gBAAgB,GAAGrI,gBAAgB,CAAC+H,UAAU,CAAC;IACpD,IAAI,CAACO,cAAc,CAAC,CAAC;EACzB;EACAC,SAASA,CAACC,MAAM,EAAE;IACd,KAAK,MAAMC,KAAK,IAAID,MAAM,EAAE;MACxB,MAAME,UAAU,GAAG,IAAI,CAACC,gBAAgB,CAACF,KAAK,EAAE,CAAC,CAAC;MAClD,IAAIC,UAAU,KAAK,CAAC,EAAE;QAClB,IAAI,CAACE,YAAY,CAACH,KAAK,CAAC;MAC5B;IACJ;EACJ;EACAI,YAAYA,CAACL,MAAM,EAAE;IACjB,KAAK,MAAMC,KAAK,IAAID,MAAM,EAAE;MACxB,MAAME,UAAU,GAAG,IAAI,CAACC,gBAAgB,CAACF,KAAK,EAAE,CAAC,CAAC,CAAC;MACnD,IAAIC,UAAU,IAAI,CAAC,EAAE;QACjB,IAAI,CAACI,cAAc,CAACL,KAAK,CAAC;MAC9B;IACJ;EACJ;EACAM,WAAWA,CAAA,EAAG;IACV,MAAMZ,eAAe,GAAG,IAAI,CAACA,eAAe;IAC5C,IAAIA,eAAe,EAAE;MACjBA,eAAe,CAACpD,OAAO,CAAE3D,IAAI,IAAKA,IAAI,CAACD,MAAM,CAAC,CAAC,CAAC;MAChDgH,eAAe,CAACa,KAAK,CAAC,CAAC;IAC3B;IACA,KAAK,MAAMP,KAAK,IAAI,IAAI,CAACQ,YAAY,CAAC,CAAC,EAAE;MACrC,IAAI,CAACH,cAAc,CAACL,KAAK,CAAC;IAC9B;IACA,IAAI,CAACH,cAAc,CAAC,CAAC;EACzB;EACAY,OAAOA,CAACC,QAAQ,EAAE;IACd,IAAI,CAAClB,SAAS,CAACmB,GAAG,CAACD,QAAQ,CAAC;IAC5B,KAAK,MAAMV,KAAK,IAAI,IAAI,CAACQ,YAAY,CAAC,CAAC,EAAE;MACrC,IAAI,CAACI,cAAc,CAACF,QAAQ,EAAEV,KAAK,CAAC;IACxC;EACJ;EACAa,UAAUA,CAACH,QAAQ,EAAE;IACjB,IAAI,CAAClB,SAAS,CAACsB,MAAM,CAACJ,QAAQ,CAAC;EACnC;EACAF,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACjB,QAAQ,CAACwB,IAAI,CAAC,CAAC;EAC/B;EACAZ,YAAYA,CAACH,KAAK,EAAE;IAChB,KAAK,MAAMrD,IAAI,IAAI,IAAI,CAAC6C,SAAS,EAAE;MAC/B,IAAI,CAACoB,cAAc,CAACjE,IAAI,EAAEqD,KAAK,CAAC;IACpC;EACJ;EACAK,cAAcA,CAACL,KAAK,EAAE;IAClB,MAAMT,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9BA,QAAQ,CAACf,GAAG,CAACwB,KAAK,CAAC,EAAEgB,QAAQ,EAAE1E,OAAO,CAAE3D,IAAI,IAAKA,IAAI,CAACD,MAAM,CAAC,CAAC,CAAC;IAC/D6G,QAAQ,CAACuB,MAAM,CAACd,KAAK,CAAC;EAC1B;EACAL,2BAA2BA,CAAA,EAAG;IAC1B,MAAMI,MAAM,GAAG,IAAI,CAAC/G,GAAG,CAACiI,IAAI,EAAEC,gBAAgB,CAAE,SAAQhC,qBAAsB,KAAI,IAAI,CAACE,KAAM,IAAG,CAAC;IACjG,IAAIW,MAAM,EAAE7D,MAAM,EAAE;MAChB,MAAMiF,QAAQ,GAAG,IAAItD,GAAG,CAAC,CAAC;MAC1BkC,MAAM,CAACzD,OAAO,CAAE0D,KAAK,IAAK;QACtB,IAAIA,KAAK,CAACoB,WAAW,IAAI,IAAI,EAAE;UAC3BD,QAAQ,CAACxC,GAAG,CAACqB,KAAK,CAACoB,WAAW,EAAEpB,KAAK,CAAC;QAC1C;MACJ,CAAC,CAAC;MACF,OAAOmB,QAAQ;IACnB;IACA,OAAO,IAAI;EACf;EACAjB,gBAAgBA,CAACF,KAAK,EAAEqB,KAAK,EAAE;IAC3B,MAAMC,GAAG,GAAG,IAAI,CAAC/B,QAAQ;IACzB,IAAI+B,GAAG,CAACC,GAAG,CAACvB,KAAK,CAAC,EAAE;MAChB,MAAMwB,aAAa,GAAGF,GAAG,CAAC9C,GAAG,CAACwB,KAAK,CAAC;MACpCwB,aAAa,CAACC,KAAK,IAAIJ,KAAK;MAC5B,OAAOG,aAAa,CAACC,KAAK;IAC9B;IACAH,GAAG,CAAC3C,GAAG,CAACqB,KAAK,EAAE;MAAEyB,KAAK,EAAEJ,KAAK;MAAEL,QAAQ,EAAE;IAAG,CAAC,CAAC;IAC9C,OAAOK,KAAK;EAChB;EACAK,eAAeA,CAAC/E,IAAI,EAAEqD,KAAK,EAAE;IACzB,MAAMN,eAAe,GAAG,IAAI,CAACA,eAAe;IAC5C,MAAMiC,OAAO,GAAGjC,eAAe,EAAElB,GAAG,CAACwB,KAAK,CAAC;IAC3C,IAAI2B,OAAO,EAAE/I,UAAU,KAAK+D,IAAI,EAAE;MAC9B;MACA+C,eAAe,CAACoB,MAAM,CAACd,KAAK,CAAC;MAC7B2B,OAAO,CAACC,eAAe,CAAC1C,qBAAqB,CAAC;MAC9C,IAAI,OAAOvD,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;QAC/C;QACAgG,OAAO,CAAC3G,YAAY,CAAC,iBAAiB,EAAE,EAAE,CAAC;MAC/C;MACA,OAAO2G,OAAO;IAClB,CAAC,MACI;MACD,MAAMA,OAAO,GAAG,IAAI,CAAC3I,GAAG,CAACF,aAAa,CAAC,OAAO,CAAC;MAC/C,IAAI,IAAI,CAACuG,KAAK,EAAE;QACZsC,OAAO,CAAC3G,YAAY,CAAC,OAAO,EAAE,IAAI,CAACqE,KAAK,CAAC;MAC7C;MACAsC,OAAO,CAACP,WAAW,GAAGpB,KAAK;MAC3B,IAAI,IAAI,CAACJ,gBAAgB,EAAE;QACvB+B,OAAO,CAAC3G,YAAY,CAACkE,qBAAqB,EAAE,IAAI,CAACE,KAAK,CAAC;MAC3D;MACA,OAAOuC,OAAO;IAClB;EACJ;EACAf,cAAcA,CAACjE,IAAI,EAAEqD,KAAK,EAAE;IACxB,MAAM2B,OAAO,GAAG,IAAI,CAACD,eAAe,CAAC/E,IAAI,EAAEqD,KAAK,CAAC;IACjDrD,IAAI,CAACkF,WAAW,CAACF,OAAO,CAAC;IACzB,MAAMpC,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,MAAMuC,UAAU,GAAGvC,QAAQ,CAACf,GAAG,CAACwB,KAAK,CAAC,EAAEgB,QAAQ;IAChD,IAAIc,UAAU,EAAE;MACZA,UAAU,CAACtF,IAAI,CAACmF,OAAO,CAAC;IAC5B,CAAC,MACI;MACDpC,QAAQ,CAACZ,GAAG,CAACqB,KAAK,EAAE;QAAEgB,QAAQ,EAAE,CAACW,OAAO,CAAC;QAAEF,KAAK,EAAE;MAAE,CAAC,CAAC;IAC1D;EACJ;EACA5B,cAAcA,CAAA,EAAG;IACb,MAAML,SAAS,GAAG,IAAI,CAACA,SAAS;IAChCA,SAAS,CAACe,KAAK,CAAC,CAAC;IACjB;IACAf,SAAS,CAACmB,GAAG,CAAC,IAAI,CAAC3H,GAAG,CAACiI,IAAI,CAAC;EAChC;EACA;IAAS,IAAI,CAACjE,IAAI,YAAA+E,yBAAAtF,CAAA;MAAA,YAAAA,CAAA,IAAyF0C,gBAAgB,EAlN1BtL,EAAE,CAAA4B,QAAA,CAkN0C+B,QAAQ,GAlNpD3D,EAAE,CAAA4B,QAAA,CAkN+DtB,MAAM,GAlNvEN,EAAE,CAAA4B,QAAA,CAkNkFrB,SAAS,MAlN7FP,EAAE,CAAA4B,QAAA,CAkNwHpB,WAAW;IAAA,CAA6C;EAAE;EACrR;IAAS,IAAI,CAAC6I,KAAK,kBAnN8ErJ,EAAE,CAAAsJ,kBAAA;MAAAC,KAAA,EAmNY+B,gBAAgB;MAAA9B,OAAA,EAAhB8B,gBAAgB,CAAAnC;IAAA,EAAG;EAAE;AACxI;AACA;EAAA,QAAArB,SAAA,oBAAAA,SAAA,KArNqG9H,EAAE,CAAAyJ,iBAAA,CAqNX6B,gBAAgB,EAAc,CAAC;IAC/G5B,IAAI,EAAEvJ;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEuJ,IAAI,EAAEyE,QAAQ;MAAElD,UAAU,EAAE,CAAC;QAC7DvB,IAAI,EAAErJ,MAAM;QACZ6K,IAAI,EAAE,CAACvH,QAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAE+F,IAAI,EAAEsB,SAAS;MAAEC,UAAU,EAAE,CAAC;QAClCvB,IAAI,EAAErJ,MAAM;QACZ6K,IAAI,EAAE,CAAC5K,MAAM;MACjB,CAAC;IAAE,CAAC,EAAE;MAAEoJ,IAAI,EAAEsB,SAAS;MAAEC,UAAU,EAAE,CAAC;QAClCvB,IAAI,EAAErJ,MAAM;QACZ6K,IAAI,EAAE,CAAC3K,SAAS;MACpB,CAAC,EAAE;QACCmJ,IAAI,EAAEjJ;MACV,CAAC;IAAE,CAAC,EAAE;MAAEiJ,IAAI,EAAEsB,SAAS;MAAEC,UAAU,EAAE,CAAC;QAClCvB,IAAI,EAAErJ,MAAM;QACZ6K,IAAI,EAAE,CAAC1K,WAAW;MACtB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AAExB,MAAM4N,cAAc,GAAG;EACnB,KAAK,EAAE,4BAA4B;EACnC,OAAO,EAAE,8BAA8B;EACvC,OAAO,EAAE,8BAA8B;EACvC,KAAK,EAAE,sCAAsC;EAC7C,OAAO,EAAE,+BAA+B;EACxC,MAAM,EAAE;AACZ,CAAC;AACD,MAAMC,eAAe,GAAG,SAAS;AACjC,MAAMC,kBAAkB,GAAG,QAAQ;AACnC,MAAMC,SAAS,GAAI,WAAUD,kBAAmB,EAAC;AACjD,MAAME,YAAY,GAAI,cAAaF,kBAAmB,EAAC;AACvD;AACA;AACA;AACA,MAAMG,0CAA0C,GAAG,KAAK;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,kCAAkC,GAAG,IAAItO,cAAc,CAAC,2BAA2B,EAAE;EACvFuO,UAAU,EAAE,MAAM;EAClBnF,OAAO,EAAEA,CAAA,KAAMiF;AACnB,CAAC,CAAC;AACF,SAASG,oBAAoBA,CAACC,gBAAgB,EAAE;EAC5C,OAAOL,YAAY,CAACM,OAAO,CAACT,eAAe,EAAEQ,gBAAgB,CAAC;AAClE;AACA,SAASE,iBAAiBA,CAACF,gBAAgB,EAAE;EACzC,OAAON,SAAS,CAACO,OAAO,CAACT,eAAe,EAAEQ,gBAAgB,CAAC;AAC/D;AACA,SAASG,iBAAiBA,CAACC,MAAM,EAAE/C,MAAM,EAAE;EACvC,OAAOA,MAAM,CAACuB,GAAG,CAACyB,CAAC,IAAIA,CAAC,CAACJ,OAAO,CAACT,eAAe,EAAEY,MAAM,CAAC,CAAC;AAC9D;AACA,MAAME,mBAAmB,CAAC;EACtBlL,WAAWA,CAACmL,YAAY,EAAEC,gBAAgB,EAAE9D,KAAK,EAAE+D,yBAAyB,EAAEnK,GAAG,EAAEsG,UAAU,EAAE8D,MAAM,EAAE/D,KAAK,GAAG,IAAI,EAAE;IACjH,IAAI,CAAC4D,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAAC9D,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC+D,yBAAyB,GAAGA,yBAAyB;IAC1D,IAAI,CAACnK,GAAG,GAAGA,GAAG;IACd,IAAI,CAACsG,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAAC8D,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC/D,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACgE,gBAAgB,GAAG,IAAIxF,GAAG,CAAC,CAAC;IACjC,IAAI,CAAC+B,gBAAgB,GAAGrI,gBAAgB,CAAC+H,UAAU,CAAC;IACpD,IAAI,CAACgE,eAAe,GAChB,IAAIC,mBAAmB,CAACN,YAAY,EAAEjK,GAAG,EAAEoK,MAAM,EAAE,IAAI,CAACxD,gBAAgB,CAAC;EACjF;EACA4D,cAAcA,CAACrF,OAAO,EAAEZ,IAAI,EAAE;IAC1B,IAAI,CAACY,OAAO,IAAI,CAACZ,IAAI,EAAE;MACnB,OAAO,IAAI,CAAC+F,eAAe;IAC/B;IACA,IAAI,IAAI,CAAC1D,gBAAgB,IAAIrC,IAAI,CAACkG,aAAa,KAAKlP,iBAAiB,CAACmP,SAAS,EAAE;MAC7E;MACAnG,IAAI,GAAG;QAAE,GAAGA,IAAI;QAAEkG,aAAa,EAAElP,iBAAiB,CAACoP;MAAS,CAAC;IACjE;IACA,MAAMC,QAAQ,GAAG,IAAI,CAACC,mBAAmB,CAAC1F,OAAO,EAAEZ,IAAI,CAAC;IACxD;IACA;IACA,IAAIqG,QAAQ,YAAYE,iCAAiC,EAAE;MACvDF,QAAQ,CAACG,WAAW,CAAC5F,OAAO,CAAC;IACjC,CAAC,MACI,IAAIyF,QAAQ,YAAYI,4BAA4B,EAAE;MACvDJ,QAAQ,CAACK,WAAW,CAAC,CAAC;IAC1B;IACA,OAAOL,QAAQ;EACnB;EACAC,mBAAmBA,CAAC1F,OAAO,EAAEZ,IAAI,EAAE;IAC/B,MAAM8F,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;IAC9C,IAAIO,QAAQ,GAAGP,gBAAgB,CAAC7E,GAAG,CAACjB,IAAI,CAAC2G,EAAE,CAAC;IAC5C,IAAI,CAACN,QAAQ,EAAE;MACX,MAAM5K,GAAG,GAAG,IAAI,CAACA,GAAG;MACpB,MAAMoK,MAAM,GAAG,IAAI,CAACA,MAAM;MAC1B,MAAMH,YAAY,GAAG,IAAI,CAACA,YAAY;MACtC,MAAMC,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;MAC9C,MAAMC,yBAAyB,GAAG,IAAI,CAACA,yBAAyB;MAChE,MAAMvD,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;MAC9C,QAAQrC,IAAI,CAACkG,aAAa;QACtB,KAAKlP,iBAAiB,CAACoP,QAAQ;UAC3BC,QAAQ,GAAG,IAAIE,iCAAiC,CAACb,YAAY,EAAEC,gBAAgB,EAAE3F,IAAI,EAAE,IAAI,CAAC6B,KAAK,EAAE+D,yBAAyB,EAAEnK,GAAG,EAAEoK,MAAM,EAAExD,gBAAgB,CAAC;UAC5J;QACJ,KAAKrL,iBAAiB,CAACmP,SAAS;UAC5B,OAAO,IAAIS,iBAAiB,CAAClB,YAAY,EAAEC,gBAAgB,EAAE/E,OAAO,EAAEZ,IAAI,EAAEvE,GAAG,EAAEoK,MAAM,EAAE,IAAI,CAAC/D,KAAK,EAAEO,gBAAgB,CAAC;QAC1H;UACIgE,QAAQ,GAAG,IAAII,4BAA4B,CAACf,YAAY,EAAEC,gBAAgB,EAAE3F,IAAI,EAAE4F,yBAAyB,EAAEnK,GAAG,EAAEoK,MAAM,EAAExD,gBAAgB,CAAC;UAC3I;MACR;MACAyD,gBAAgB,CAAC1E,GAAG,CAACpB,IAAI,CAAC2G,EAAE,EAAEN,QAAQ,CAAC;IAC3C;IACA,OAAOA,QAAQ;EACnB;EACAtD,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC+C,gBAAgB,CAAC9C,KAAK,CAAC,CAAC;EACjC;EACA;IAAS,IAAI,CAACvD,IAAI,YAAAoH,4BAAA3H,CAAA;MAAA,YAAAA,CAAA,IAAyFuG,mBAAmB,EAxU7BnP,EAAE,CAAA4B,QAAA,CAwU6CgI,YAAY,GAxU3D5J,EAAE,CAAA4B,QAAA,CAwUsE0J,gBAAgB,GAxUxFtL,EAAE,CAAA4B,QAAA,CAwUmGtB,MAAM,GAxU3GN,EAAE,CAAA4B,QAAA,CAwUsH8M,kCAAkC,GAxU1J1O,EAAE,CAAA4B,QAAA,CAwUqK+B,QAAQ,GAxU/K3D,EAAE,CAAA4B,QAAA,CAwU0LpB,WAAW,GAxUvMR,EAAE,CAAA4B,QAAA,CAwUkN5B,EAAE,CAACqB,MAAM,GAxU7NrB,EAAE,CAAA4B,QAAA,CAwUwOrB,SAAS;IAAA,CAA6C;EAAE;EACnY;IAAS,IAAI,CAAC8I,KAAK,kBAzU8ErJ,EAAE,CAAAsJ,kBAAA;MAAAC,KAAA,EAyUY4F,mBAAmB;MAAA3F,OAAA,EAAnB2F,mBAAmB,CAAAhG;IAAA,EAAG;EAAE;AAC3I;AACA;EAAA,QAAArB,SAAA,oBAAAA,SAAA,KA3UqG9H,EAAE,CAAAyJ,iBAAA,CA2UX0F,mBAAmB,EAAc,CAAC;IAClHzF,IAAI,EAAEvJ;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEuJ,IAAI,EAAEE;IAAa,CAAC,EAAE;MAAEF,IAAI,EAAE4B;IAAiB,CAAC,EAAE;MAAE5B,IAAI,EAAEsB,SAAS;MAAEC,UAAU,EAAE,CAAC;QAClHvB,IAAI,EAAErJ,MAAM;QACZ6K,IAAI,EAAE,CAAC5K,MAAM;MACjB,CAAC;IAAE,CAAC,EAAE;MAAEoJ,IAAI,EAAEsB,SAAS;MAAEC,UAAU,EAAE,CAAC;QAClCvB,IAAI,EAAErJ,MAAM;QACZ6K,IAAI,EAAE,CAACwD,kCAAkC;MAC7C,CAAC;IAAE,CAAC,EAAE;MAAEhF,IAAI,EAAEyE,QAAQ;MAAElD,UAAU,EAAE,CAAC;QACjCvB,IAAI,EAAErJ,MAAM;QACZ6K,IAAI,EAAE,CAACvH,QAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAE+F,IAAI,EAAE8G,MAAM;MAAEvF,UAAU,EAAE,CAAC;QAC/BvB,IAAI,EAAErJ,MAAM;QACZ6K,IAAI,EAAE,CAAC1K,WAAW;MACtB,CAAC;IAAE,CAAC,EAAE;MAAEkJ,IAAI,EAAE1J,EAAE,CAACqB;IAAO,CAAC,EAAE;MAAEqI,IAAI,EAAEsB,SAAS;MAAEC,UAAU,EAAE,CAAC;QACvDvB,IAAI,EAAErJ,MAAM;QACZ6K,IAAI,EAAE,CAAC3K,SAAS;MACpB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AACxB,MAAMmP,mBAAmB,CAAC;EACtBzL,WAAWA,CAACmL,YAAY,EAAEjK,GAAG,EAAEoK,MAAM,EAAExD,gBAAgB,EAAE;IACrD,IAAI,CAACqD,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACjK,GAAG,GAAGA,GAAG;IACd,IAAI,CAACoK,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACxD,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAAC0E,IAAI,GAAGD,MAAM,CAACE,MAAM,CAAC,IAAI,CAAC;IAC/B,IAAI,CAACC,WAAW,GAAG,IAAI;EAC3B;EACAC,OAAOA,CAAA,EAAG,CAAE;EACZ3L,aAAaA,CAAC4B,IAAI,EAAEgK,SAAS,EAAE;IAC3B,IAAIA,SAAS,EAAE;MACX;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,OAAO,IAAI,CAAC1L,GAAG,CAAC2L,eAAe,CAAC1C,cAAc,CAACyC,SAAS,CAAC,IAAIA,SAAS,EAAEhK,IAAI,CAAC;IACjF;IACA,OAAO,IAAI,CAAC1B,GAAG,CAACF,aAAa,CAAC4B,IAAI,CAAC;EACvC;EACAkK,aAAaA,CAACC,KAAK,EAAE;IACjB,OAAO,IAAI,CAAC7L,GAAG,CAAC4L,aAAa,CAACC,KAAK,CAAC;EACxC;EACAC,UAAUA,CAACD,KAAK,EAAE;IACd,OAAO,IAAI,CAAC7L,GAAG,CAAC+L,cAAc,CAACF,KAAK,CAAC;EACzC;EACAhD,WAAWA,CAACmD,MAAM,EAAEC,QAAQ,EAAE;IAC1B,MAAMC,YAAY,GAAGC,cAAc,CAACH,MAAM,CAAC,GAAGA,MAAM,CAACI,OAAO,GAAGJ,MAAM;IACrEE,YAAY,CAACrD,WAAW,CAACoD,QAAQ,CAAC;EACtC;EACAI,YAAYA,CAACL,MAAM,EAAEC,QAAQ,EAAEK,QAAQ,EAAE;IACrC,IAAIN,MAAM,EAAE;MACR,MAAME,YAAY,GAAGC,cAAc,CAACH,MAAM,CAAC,GAAGA,MAAM,CAACI,OAAO,GAAGJ,MAAM;MACrEE,YAAY,CAACG,YAAY,CAACJ,QAAQ,EAAEK,QAAQ,CAAC;IACjD;EACJ;EACAzM,WAAWA,CAACmM,MAAM,EAAEO,QAAQ,EAAE;IAC1B,IAAIP,MAAM,EAAE;MACRA,MAAM,CAACnM,WAAW,CAAC0M,QAAQ,CAAC;IAChC;EACJ;EACAC,iBAAiBA,CAACC,cAAc,EAAEC,eAAe,EAAE;IAC/C,IAAItN,EAAE,GAAG,OAAOqN,cAAc,KAAK,QAAQ,GAAG,IAAI,CAACzM,GAAG,CAAC4B,aAAa,CAAC6K,cAAc,CAAC,GAChFA,cAAc;IAClB,IAAI,CAACrN,EAAE,EAAE;MACL,MAAM,IAAIrE,aAAa,CAAC,CAAC,IAAI,CAAC,4CAA4C,CAAC,OAAO4H,SAAS,KAAK,WAAW,IAAIA,SAAS,KACnH,iBAAgB8J,cAAe,8BAA6B,CAAC;IACtE;IACA,IAAI,CAACC,eAAe,EAAE;MAClBtN,EAAE,CAACgJ,WAAW,GAAG,EAAE;IACvB;IACA,OAAOhJ,EAAE;EACb;EACAQ,UAAUA,CAACD,IAAI,EAAE;IACb,OAAOA,IAAI,CAACC,UAAU;EAC1B;EACA+M,WAAWA,CAAChN,IAAI,EAAE;IACd,OAAOA,IAAI,CAACgN,WAAW;EAC3B;EACA3K,YAAYA,CAAC5C,EAAE,EAAEsC,IAAI,EAAEmK,KAAK,EAAEH,SAAS,EAAE;IACrC,IAAIA,SAAS,EAAE;MACXhK,IAAI,GAAGgK,SAAS,GAAG,GAAG,GAAGhK,IAAI;MAC7B,MAAMkL,YAAY,GAAG3D,cAAc,CAACyC,SAAS,CAAC;MAC9C,IAAIkB,YAAY,EAAE;QACdxN,EAAE,CAACyN,cAAc,CAACD,YAAY,EAAElL,IAAI,EAAEmK,KAAK,CAAC;MAChD,CAAC,MACI;QACDzM,EAAE,CAAC4C,YAAY,CAACN,IAAI,EAAEmK,KAAK,CAAC;MAChC;IACJ,CAAC,MACI;MACDzM,EAAE,CAAC4C,YAAY,CAACN,IAAI,EAAEmK,KAAK,CAAC;IAChC;EACJ;EACAjD,eAAeA,CAACxJ,EAAE,EAAEsC,IAAI,EAAEgK,SAAS,EAAE;IACjC,IAAIA,SAAS,EAAE;MACX,MAAMkB,YAAY,GAAG3D,cAAc,CAACyC,SAAS,CAAC;MAC9C,IAAIkB,YAAY,EAAE;QACdxN,EAAE,CAAC0N,iBAAiB,CAACF,YAAY,EAAElL,IAAI,CAAC;MAC5C,CAAC,MACI;QACDtC,EAAE,CAACwJ,eAAe,CAAE,GAAE8C,SAAU,IAAGhK,IAAK,EAAC,CAAC;MAC9C;IACJ,CAAC,MACI;MACDtC,EAAE,CAACwJ,eAAe,CAAClH,IAAI,CAAC;IAC5B;EACJ;EACAqL,QAAQA,CAAC3N,EAAE,EAAEsC,IAAI,EAAE;IACftC,EAAE,CAAC4N,SAAS,CAACrF,GAAG,CAACjG,IAAI,CAAC;EAC1B;EACAuL,WAAWA,CAAC7N,EAAE,EAAEsC,IAAI,EAAE;IAClBtC,EAAE,CAAC4N,SAAS,CAACtN,MAAM,CAACgC,IAAI,CAAC;EAC7B;EACAwL,QAAQA,CAAC9N,EAAE,EAAE4H,KAAK,EAAE6E,KAAK,EAAEsB,KAAK,EAAE;IAC9B,IAAIA,KAAK,IAAI3R,mBAAmB,CAAC4R,QAAQ,GAAG5R,mBAAmB,CAAC6R,SAAS,CAAC,EAAE;MACxEjO,EAAE,CAAC4H,KAAK,CAACsG,WAAW,CAACtG,KAAK,EAAE6E,KAAK,EAAEsB,KAAK,GAAG3R,mBAAmB,CAAC6R,SAAS,GAAG,WAAW,GAAG,EAAE,CAAC;IAChG,CAAC,MACI;MACDjO,EAAE,CAAC4H,KAAK,CAACA,KAAK,CAAC,GAAG6E,KAAK;IAC3B;EACJ;EACA0B,WAAWA,CAACnO,EAAE,EAAE4H,KAAK,EAAEmG,KAAK,EAAE;IAC1B,IAAIA,KAAK,GAAG3R,mBAAmB,CAAC4R,QAAQ,EAAE;MACtC;MACAhO,EAAE,CAAC4H,KAAK,CAACwG,cAAc,CAACxG,KAAK,CAAC;IAClC,CAAC,MACI;MACD5H,EAAE,CAAC4H,KAAK,CAACA,KAAK,CAAC,GAAG,EAAE;IACxB;EACJ;EACAsG,WAAWA,CAAClO,EAAE,EAAEsC,IAAI,EAAEmK,KAAK,EAAE;IACzB,CAAC,OAAOlJ,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAK8K,oBAAoB,CAAC/L,IAAI,EAAE,UAAU,CAAC;IACzFtC,EAAE,CAACsC,IAAI,CAAC,GAAGmK,KAAK;EACpB;EACA6B,QAAQA,CAAC/N,IAAI,EAAEkM,KAAK,EAAE;IAClBlM,IAAI,CAACgO,SAAS,GAAG9B,KAAK;EAC1B;EACA+B,MAAMA,CAAC/M,MAAM,EAAEgN,KAAK,EAAE9K,QAAQ,EAAE;IAC5B,CAAC,OAAOJ,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAK8K,oBAAoB,CAACI,KAAK,EAAE,UAAU,CAAC;IAC1F,IAAI,OAAOhN,MAAM,KAAK,QAAQ,EAAE;MAC5BA,MAAM,GAAGvC,OAAO,CAAC,CAAC,CAACsC,oBAAoB,CAAC,IAAI,CAACZ,GAAG,EAAEa,MAAM,CAAC;MACzD,IAAI,CAACA,MAAM,EAAE;QACT,MAAM,IAAIiN,KAAK,CAAE,4BAA2BjN,MAAO,cAAagN,KAAM,EAAC,CAAC;MAC5E;IACJ;IACA,OAAO,IAAI,CAAC5D,YAAY,CAAC1K,gBAAgB,CAACsB,MAAM,EAAEgN,KAAK,EAAE,IAAI,CAACE,sBAAsB,CAAChL,QAAQ,CAAC,CAAC;EACnG;EACAgL,sBAAsBA,CAACC,YAAY,EAAE;IACjC;IACA;IACA;IACA;IACA,OAAQH,KAAK,IAAK;MACd;MACA;MACA;MACA;MACA;MACA,IAAIA,KAAK,KAAK,cAAc,EAAE;QAC1B,OAAOG,YAAY;MACvB;MACA;MACA;MACA,MAAMC,oBAAoB,GAAG,IAAI,CAACrH,gBAAgB,GAC9C,IAAI,CAACwD,MAAM,CAAC8D,UAAU,CAAC,MAAMF,YAAY,CAACH,KAAK,CAAC,CAAC,GACjDG,YAAY,CAACH,KAAK,CAAC;MACvB,IAAII,oBAAoB,KAAK,KAAK,EAAE;QAChCJ,KAAK,CAACM,cAAc,CAAC,CAAC;MAC1B;MACA,OAAOtI,SAAS;IACpB,CAAC;EACL;AACJ;AACA,MAAMuI,WAAW,GAAG,CAAC,MAAM,GAAG,CAACC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;AAC/C,SAASZ,oBAAoBA,CAAC/L,IAAI,EAAE4M,QAAQ,EAAE;EAC1C,IAAI5M,IAAI,CAAC2M,UAAU,CAAC,CAAC,CAAC,KAAKD,WAAW,EAAE;IACpC,MAAM,IAAIrT,aAAa,CAAC,IAAI,CAAC,sDAAuD,wBAAuBuT,QAAS,IAAG5M,IAAK;AACpI;AACA,qEAAqEA,IAAK,gIAA+H,CAAC;EACtM;AACJ;AACA,SAASyK,cAAcA,CAACxM,IAAI,EAAE;EAC1B,OAAOA,IAAI,CAACI,OAAO,KAAK,UAAU,IAAIJ,IAAI,CAACyM,OAAO,KAAKvG,SAAS;AACpE;AACA,MAAMsF,iBAAiB,SAASZ,mBAAmB,CAAC;EAChDzL,WAAWA,CAACmL,YAAY,EAAEC,gBAAgB,EAAEqE,MAAM,EAAEC,SAAS,EAAExO,GAAG,EAAEoK,MAAM,EAAE/D,KAAK,EAAEO,gBAAgB,EAAE;IACjG,KAAK,CAACqD,YAAY,EAAEjK,GAAG,EAAEoK,MAAM,EAAExD,gBAAgB,CAAC;IAClD,IAAI,CAACsD,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACqE,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACE,UAAU,GAAGF,MAAM,CAACG,YAAY,CAAC;MAAEC,IAAI,EAAE;IAAO,CAAC,CAAC;IACvD,IAAI,CAACzE,gBAAgB,CAACzC,OAAO,CAAC,IAAI,CAACgH,UAAU,CAAC;IAC9C,MAAM1H,MAAM,GAAG8C,iBAAiB,CAAC2E,SAAS,CAACtD,EAAE,EAAEsD,SAAS,CAACzH,MAAM,CAAC;IAChE,KAAK,MAAMC,KAAK,IAAID,MAAM,EAAE;MACxB,MAAM4B,OAAO,GAAGxI,QAAQ,CAACL,aAAa,CAAC,OAAO,CAAC;MAC/C,IAAIuG,KAAK,EAAE;QACPsC,OAAO,CAAC3G,YAAY,CAAC,OAAO,EAAEqE,KAAK,CAAC;MACxC;MACAsC,OAAO,CAACP,WAAW,GAAGpB,KAAK;MAC3B,IAAI,CAACyH,UAAU,CAAC5F,WAAW,CAACF,OAAO,CAAC;IACxC;EACJ;EACAiG,gBAAgBA,CAACjP,IAAI,EAAE;IACnB,OAAOA,IAAI,KAAK,IAAI,CAAC4O,MAAM,GAAG,IAAI,CAACE,UAAU,GAAG9O,IAAI;EACxD;EACAkJ,WAAWA,CAACmD,MAAM,EAAEC,QAAQ,EAAE;IAC1B,OAAO,KAAK,CAACpD,WAAW,CAAC,IAAI,CAAC+F,gBAAgB,CAAC5C,MAAM,CAAC,EAAEC,QAAQ,CAAC;EACrE;EACAI,YAAYA,CAACL,MAAM,EAAEC,QAAQ,EAAEK,QAAQ,EAAE;IACrC,OAAO,KAAK,CAACD,YAAY,CAAC,IAAI,CAACuC,gBAAgB,CAAC5C,MAAM,CAAC,EAAEC,QAAQ,EAAEK,QAAQ,CAAC;EAChF;EACAzM,WAAWA,CAACmM,MAAM,EAAEO,QAAQ,EAAE;IAC1B,OAAO,KAAK,CAAC1M,WAAW,CAAC,IAAI,CAAC+O,gBAAgB,CAAC5C,MAAM,CAAC,EAAEO,QAAQ,CAAC;EACrE;EACA3M,UAAUA,CAACD,IAAI,EAAE;IACb,OAAO,IAAI,CAACiP,gBAAgB,CAAC,KAAK,CAAChP,UAAU,CAAC,IAAI,CAACgP,gBAAgB,CAACjP,IAAI,CAAC,CAAC,CAAC;EAC/E;EACA8L,OAAOA,CAAA,EAAG;IACN,IAAI,CAACvB,gBAAgB,CAACrC,UAAU,CAAC,IAAI,CAAC4G,UAAU,CAAC;EACrD;AACJ;AACA,MAAMzD,4BAA4B,SAAST,mBAAmB,CAAC;EAC3DzL,WAAWA,CAACmL,YAAY,EAAEC,gBAAgB,EAAEsE,SAAS,EAAErE,yBAAyB,EAAEnK,GAAG,EAAEoK,MAAM,EAAExD,gBAAgB,EAAEkD,MAAM,EAAE;IACrH,KAAK,CAACG,YAAY,EAAEjK,GAAG,EAAEoK,MAAM,EAAExD,gBAAgB,CAAC;IAClD,IAAI,CAACsD,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,yBAAyB,GAAGA,yBAAyB;IAC1D,IAAI,CAACpD,MAAM,GAAG+C,MAAM,GAAGD,iBAAiB,CAACC,MAAM,EAAE0E,SAAS,CAACzH,MAAM,CAAC,GAAGyH,SAAS,CAACzH,MAAM;EACzF;EACAkE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACf,gBAAgB,CAACpD,SAAS,CAAC,IAAI,CAACC,MAAM,CAAC;EAChD;EACA0E,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC,IAAI,CAACtB,yBAAyB,EAAE;MACjC;IACJ;IACA,IAAI,CAACD,gBAAgB,CAAC9C,YAAY,CAAC,IAAI,CAACL,MAAM,CAAC;EACnD;AACJ;AACA,MAAM+D,iCAAiC,SAASE,4BAA4B,CAAC;EACzElM,WAAWA,CAACmL,YAAY,EAAEC,gBAAgB,EAAEsE,SAAS,EAAEpI,KAAK,EAAE+D,yBAAyB,EAAEnK,GAAG,EAAEoK,MAAM,EAAExD,gBAAgB,EAAE;IACpH,MAAMkD,MAAM,GAAG1D,KAAK,GAAG,GAAG,GAAGoI,SAAS,CAACtD,EAAE;IACzC,KAAK,CAACjB,YAAY,EAAEC,gBAAgB,EAAEsE,SAAS,EAAErE,yBAAyB,EAAEnK,GAAG,EAAEoK,MAAM,EAAExD,gBAAgB,EAAEkD,MAAM,CAAC;IAClH,IAAI,CAAC+E,WAAW,GAAGpF,oBAAoB,CAACK,MAAM,CAAC;IAC/C,IAAI,CAACgF,QAAQ,GAAGlF,iBAAiB,CAACE,MAAM,CAAC;EAC7C;EACAiB,WAAWA,CAAC5F,OAAO,EAAE;IACjB,IAAI,CAAC8F,WAAW,CAAC,CAAC;IAClB,IAAI,CAACjJ,YAAY,CAACmD,OAAO,EAAE,IAAI,CAAC2J,QAAQ,EAAE,EAAE,CAAC;EACjD;EACAhP,aAAaA,CAACkM,MAAM,EAAEtK,IAAI,EAAE;IACxB,MAAMtC,EAAE,GAAG,KAAK,CAACU,aAAa,CAACkM,MAAM,EAAEtK,IAAI,CAAC;IAC5C,KAAK,CAACM,YAAY,CAAC5C,EAAE,EAAE,IAAI,CAACyP,WAAW,EAAE,EAAE,CAAC;IAC5C,OAAOzP,EAAE;EACb;AACJ;AAEA,MAAM2P,eAAe,SAAS/I,kBAAkB,CAAC;EAC7ClH,WAAWA,CAACkB,GAAG,EAAE;IACb,KAAK,CAACA,GAAG,CAAC;EACd;EACA;EACA;EACA0F,QAAQA,CAACN,SAAS,EAAE;IAChB,OAAO,IAAI;EACf;EACA7F,gBAAgBA,CAAC4F,OAAO,EAAEC,SAAS,EAAEC,OAAO,EAAE;IAC1CF,OAAO,CAAC5F,gBAAgB,CAAC6F,SAAS,EAAEC,OAAO,EAAE,KAAK,CAAC;IACnD,OAAO,MAAM,IAAI,CAAC7F,mBAAmB,CAAC2F,OAAO,EAAEC,SAAS,EAAEC,OAAO,CAAC;EACtE;EACA7F,mBAAmBA,CAACqB,MAAM,EAAEuE,SAAS,EAAErC,QAAQ,EAAE;IAC7C,OAAOlC,MAAM,CAACrB,mBAAmB,CAAC4F,SAAS,EAAErC,QAAQ,CAAC;EAC1D;EACA;IAAS,IAAI,CAACiB,IAAI,YAAAgL,wBAAAvL,CAAA;MAAA,YAAAA,CAAA,IAAyFsL,eAAe,EA9lBzBlU,EAAE,CAAA4B,QAAA,CA8lByC+B,QAAQ;IAAA,CAA6C;EAAE;EACnM;IAAS,IAAI,CAAC0F,KAAK,kBA/lB8ErJ,EAAE,CAAAsJ,kBAAA;MAAAC,KAAA,EA+lBY2K,eAAe;MAAA1K,OAAA,EAAf0K,eAAe,CAAA/K;IAAA,EAAG;EAAE;AACvI;AACA;EAAA,QAAArB,SAAA,oBAAAA,SAAA,KAjmBqG9H,EAAE,CAAAyJ,iBAAA,CAimBXyK,eAAe,EAAc,CAAC;IAC9GxK,IAAI,EAAEvJ;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEuJ,IAAI,EAAEsB,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DvB,IAAI,EAAErJ,MAAM;QACZ6K,IAAI,EAAE,CAACvH,QAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA;AACA;AACA,MAAMyQ,aAAa,GAAG,CAAC,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC;AACzD;AACA;AACA,MAAMC,OAAO,GAAG;EACZ,IAAI,EAAE,WAAW;EACjB,IAAI,EAAE,KAAK;EACX,MAAM,EAAE,QAAQ;EAChB,MAAM,EAAE,QAAQ;EAChB,KAAK,EAAE,QAAQ;EACf,KAAK,EAAE,QAAQ;EACf,MAAM,EAAE,WAAW;EACnB,OAAO,EAAE,YAAY;EACrB,IAAI,EAAE,SAAS;EACf,MAAM,EAAE,WAAW;EACnB,MAAM,EAAE,aAAa;EACrB,QAAQ,EAAE,YAAY;EACtB,KAAK,EAAE;AACX,CAAC;AACD;AACA;AACA;AACA,MAAMC,oBAAoB,GAAG;EACzB,KAAK,EAAGtB,KAAK,IAAKA,KAAK,CAACuB,MAAM;EAC9B,SAAS,EAAGvB,KAAK,IAAKA,KAAK,CAACwB,OAAO;EACnC,MAAM,EAAGxB,KAAK,IAAKA,KAAK,CAACyB,OAAO;EAChC,OAAO,EAAGzB,KAAK,IAAKA,KAAK,CAAC0B;AAC9B,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMC,eAAe,SAASxJ,kBAAkB,CAAC;EAC7C;AACJ;AACA;AACA;EACIlH,WAAWA,CAACkB,GAAG,EAAE;IACb,KAAK,CAACA,GAAG,CAAC;EACd;EACA;AACJ;AACA;AACA;AACA;EACI0F,QAAQA,CAACN,SAAS,EAAE;IAChB,OAAOoK,eAAe,CAACC,cAAc,CAACrK,SAAS,CAAC,IAAI,IAAI;EAC5D;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI7F,gBAAgBA,CAAC4F,OAAO,EAAEC,SAAS,EAAEC,OAAO,EAAE;IAC1C,MAAMqK,WAAW,GAAGF,eAAe,CAACC,cAAc,CAACrK,SAAS,CAAC;IAC7D,MAAMuK,cAAc,GAAGH,eAAe,CAACI,aAAa,CAACF,WAAW,CAAC,SAAS,CAAC,EAAErK,OAAO,EAAE,IAAI,CAACN,OAAO,CAACQ,OAAO,CAAC,CAAC,CAAC;IAC7G,OAAO,IAAI,CAACR,OAAO,CAACQ,OAAO,CAAC,CAAC,CAACsK,iBAAiB,CAAC,MAAM;MAClD,OAAOvR,OAAO,CAAC,CAAC,CAACa,WAAW,CAACgG,OAAO,EAAEuK,WAAW,CAAC,cAAc,CAAC,EAAEC,cAAc,CAAC;IACtF,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOF,cAAcA,CAACrK,SAAS,EAAE;IAC7B,MAAM0K,KAAK,GAAG1K,SAAS,CAAC2K,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;IAChD,MAAMC,YAAY,GAAGH,KAAK,CAACI,KAAK,CAAC,CAAC;IAClC,IAAKJ,KAAK,CAAC5M,MAAM,KAAK,CAAC,IAAK,EAAE+M,YAAY,KAAK,SAAS,IAAIA,YAAY,KAAK,OAAO,CAAC,EAAE;MACnF,OAAO,IAAI;IACf;IACA,MAAME,GAAG,GAAGX,eAAe,CAACY,aAAa,CAACN,KAAK,CAACO,GAAG,CAAC,CAAC,CAAC;IACtD,IAAIC,OAAO,GAAG,EAAE;IAChB,IAAIC,MAAM,GAAGT,KAAK,CAACU,OAAO,CAAC,MAAM,CAAC;IAClC,IAAID,MAAM,GAAG,CAAC,CAAC,EAAE;MACbT,KAAK,CAACW,MAAM,CAACF,MAAM,EAAE,CAAC,CAAC;MACvBD,OAAO,GAAG,OAAO;IACrB;IACArB,aAAa,CAAC3L,OAAO,CAACoN,YAAY,IAAI;MAClC,MAAMC,KAAK,GAAGb,KAAK,CAACU,OAAO,CAACE,YAAY,CAAC;MACzC,IAAIC,KAAK,GAAG,CAAC,CAAC,EAAE;QACZb,KAAK,CAACW,MAAM,CAACE,KAAK,EAAE,CAAC,CAAC;QACtBL,OAAO,IAAII,YAAY,GAAG,GAAG;MACjC;IACJ,CAAC,CAAC;IACFJ,OAAO,IAAIH,GAAG;IACd,IAAIL,KAAK,CAAC5M,MAAM,IAAI,CAAC,IAAIiN,GAAG,CAACjN,MAAM,KAAK,CAAC,EAAE;MACvC;MACA,OAAO,IAAI;IACf;IACA;IACA;IACA;IACA,MAAM0N,MAAM,GAAG,CAAC,CAAC;IACjBA,MAAM,CAAC,cAAc,CAAC,GAAGX,YAAY;IACrCW,MAAM,CAAC,SAAS,CAAC,GAAGN,OAAO;IAC3B,OAAOM,MAAM;EACjB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOC,qBAAqBA,CAAChD,KAAK,EAAEiD,WAAW,EAAE;IAC7C,IAAIC,OAAO,GAAG7B,OAAO,CAACrB,KAAK,CAACsC,GAAG,CAAC,IAAItC,KAAK,CAACsC,GAAG;IAC7C,IAAIA,GAAG,GAAG,EAAE;IACZ,IAAIW,WAAW,CAACN,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;MACnCO,OAAO,GAAGlD,KAAK,CAACmD,IAAI;MACpBb,GAAG,GAAG,OAAO;IACjB;IACA;IACA,IAAIY,OAAO,IAAI,IAAI,IAAI,CAACA,OAAO,EAC3B,OAAO,KAAK;IAChBA,OAAO,GAAGA,OAAO,CAAChB,WAAW,CAAC,CAAC;IAC/B,IAAIgB,OAAO,KAAK,GAAG,EAAE;MACjBA,OAAO,GAAG,OAAO,CAAC,CAAC;IACvB,CAAC,MACI,IAAIA,OAAO,KAAK,GAAG,EAAE;MACtBA,OAAO,GAAG,KAAK,CAAC,CAAC;IACrB;;IACA9B,aAAa,CAAC3L,OAAO,CAACoN,YAAY,IAAI;MAClC,IAAIA,YAAY,KAAKK,OAAO,EAAE;QAC1B,MAAME,cAAc,GAAG9B,oBAAoB,CAACuB,YAAY,CAAC;QACzD,IAAIO,cAAc,CAACpD,KAAK,CAAC,EAAE;UACvBsC,GAAG,IAAIO,YAAY,GAAG,GAAG;QAC7B;MACJ;IACJ,CAAC,CAAC;IACFP,GAAG,IAAIY,OAAO;IACd,OAAOZ,GAAG,KAAKW,WAAW;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,OAAOlB,aAAaA,CAACU,OAAO,EAAEjL,OAAO,EAAE6L,IAAI,EAAE;IACzC,OAAQrD,KAAK,IAAK;MACd,IAAI2B,eAAe,CAACqB,qBAAqB,CAAChD,KAAK,EAAEyC,OAAO,CAAC,EAAE;QACvDY,IAAI,CAAChD,UAAU,CAAC,MAAM7I,OAAO,CAACwI,KAAK,CAAC,CAAC;MACzC;IACJ,CAAC;EACL;EACA;EACA,OAAOuC,aAAaA,CAACe,OAAO,EAAE;IAC1B;IACA,QAAQA,OAAO;MACX,KAAK,KAAK;QACN,OAAO,QAAQ;MACnB;QACI,OAAOA,OAAO;IACtB;EACJ;EACA;IAAS,IAAI,CAACnN,IAAI,YAAAoN,wBAAA3N,CAAA;MAAA,YAAAA,CAAA,IAAyF+L,eAAe,EAjxBzB3U,EAAE,CAAA4B,QAAA,CAixByC+B,QAAQ;IAAA,CAA6C;EAAE;EACnM;IAAS,IAAI,CAAC0F,KAAK,kBAlxB8ErJ,EAAE,CAAAsJ,kBAAA;MAAAC,KAAA,EAkxBYoL,eAAe;MAAAnL,OAAA,EAAfmL,eAAe,CAAAxL;IAAA,EAAG;EAAE;AACvI;AACA;EAAA,QAAArB,SAAA,oBAAAA,SAAA,KApxBqG9H,EAAE,CAAAyJ,iBAAA,CAoxBXkL,eAAe,EAAc,CAAC;IAC9GjL,IAAI,EAAEvJ;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEuJ,IAAI,EAAEsB,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DvB,IAAI,EAAErJ,MAAM;QACZ6K,IAAI,EAAE,CAACvH,QAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS6S,oBAAoBA,CAACC,aAAa,EAAEC,OAAO,EAAE;EAClD,OAAO9V,0BAA0B,CAAC;IAAE6V,aAAa;IAAE,GAAGE,qBAAqB,CAACD,OAAO;EAAE,CAAC,CAAC;AAC3F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,iBAAiBA,CAACF,OAAO,EAAE;EAChC,OAAO9V,0BAA0B,CAAC+V,qBAAqB,CAACD,OAAO,CAAC,CAAC;AACrE;AACA,SAASC,qBAAqBA,CAACD,OAAO,EAAE;EACpC,OAAO;IACHG,YAAY,EAAE,CACV,GAAGC,wBAAwB,EAC3B,IAAIJ,OAAO,EAAEK,SAAS,IAAI,EAAE,CAAC,CAChC;IACDC,iBAAiB,EAAEC;EACvB,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,+BAA+BA,CAAA,EAAG;EACvC;EACA;EACA;EACA,OAAO,CAAC,GAAGC,qBAAqB,CAAC;AACrC;AACA,SAASC,cAAcA,CAAA,EAAG;EACtBhT,iBAAiB,CAACC,WAAW,CAAC,CAAC;AACnC;AACA,SAASgT,YAAYA,CAAA,EAAG;EACpB,OAAO,IAAIxW,YAAY,CAAC,CAAC;AAC7B;AACA,SAASyW,SAASA,CAAA,EAAG;EACjB;EACAxW,YAAY,CAACwE,QAAQ,CAAC;EACtB,OAAOA,QAAQ;AACnB;AACA,MAAM2R,mCAAmC,GAAG,CACxC;EAAEM,OAAO,EAAE/W,WAAW;EAAEgX,QAAQ,EAAE5T;AAAqB,CAAC,EACxD;EAAE2T,OAAO,EAAExW,oBAAoB;EAAEyW,QAAQ,EAAEJ,cAAc;EAAEK,KAAK,EAAE;AAAK,CAAC,EACxE;EAAEF,OAAO,EAAE5T,QAAQ;EAAE+T,UAAU,EAAEJ,SAAS;EAAEK,IAAI,EAAE;AAAG,CAAC,CACzD;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,GAAG5W,qBAAqB,CAACC,YAAY,EAAE,SAAS,EAAEgW,mCAAmC,CAAC;AAC3G;AACA;AACA;AACA;AACA;AACA;AACA,MAAMY,+BAA+B,GAAG,IAAIzX,cAAc,CAAE,OAAO0H,SAAS,KAAK,WAAW,IAAIA,SAAS,GAAI,gCAAgC,GAAG,EAAE,CAAC;AACnJ,MAAMqP,qBAAqB,GAAG,CAC1B;EACII,OAAO,EAAErW,mBAAmB;EAC5B4W,QAAQ,EAAEvQ,qBAAqB;EAC/BoQ,IAAI,EAAE;AACV,CAAC,EACD;EACIJ,OAAO,EAAEpW,YAAY;EACrB2W,QAAQ,EAAE1W,WAAW;EACrBuW,IAAI,EAAE,CAACtW,MAAM,EAAEC,mBAAmB,EAAEJ,mBAAmB;AAC3D,CAAC,EACD;EACIqW,OAAO,EAAEnW,WAAW;EACpB0W,QAAQ,EAAE1W,WAAW;EACrBuW,IAAI,EAAE,CAACtW,MAAM,EAAEC,mBAAmB,EAAEJ,mBAAmB;AAC3D,CAAC,CACJ;AACD,MAAM4V,wBAAwB,GAAG,CAC7B;EAAES,OAAO,EAAEhW,eAAe;EAAEiW,QAAQ,EAAE;AAAO,CAAC,EAC9C;EAAED,OAAO,EAAE1W,YAAY;EAAE6W,UAAU,EAAEL,YAAY;EAAEM,IAAI,EAAE;AAAG,CAAC,EAAE;EAC3DJ,OAAO,EAAE5N,qBAAqB;EAC9BmO,QAAQ,EAAE5D,eAAe;EACzBuD,KAAK,EAAE,IAAI;EACXE,IAAI,EAAE,CAAChU,QAAQ,EAAEtC,MAAM,EAAEb,WAAW;AACxC,CAAC,EACD;EAAE+W,OAAO,EAAE5N,qBAAqB;EAAEmO,QAAQ,EAAEnD,eAAe;EAAE8C,KAAK,EAAE,IAAI;EAAEE,IAAI,EAAE,CAAChU,QAAQ;AAAE,CAAC,EAC5FwL,mBAAmB,EAAE7D,gBAAgB,EAAE1B,YAAY,EACnD;EAAE2N,OAAO,EAAE/V,gBAAgB;EAAEuW,WAAW,EAAE5I;AAAoB,CAAC,EAC/D;EAAEoI,OAAO,EAAE1T,UAAU;EAAEiU,QAAQ,EAAE9O,UAAU;EAAE2O,IAAI,EAAE;AAAG,CAAC,EACtD,OAAO7P,SAAS,KAAK,WAAW,IAAIA,SAAS,GAC1C;EAAEyP,OAAO,EAAEM,+BAA+B;EAAEL,QAAQ,EAAE;AAAK,CAAC,GAC5D,EAAE,CACT;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMQ,aAAa,CAAC;EAChB/T,WAAWA,CAACgU,uBAAuB,EAAE;IACjC,IAAI,CAAC,OAAOnQ,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAKmQ,uBAAuB,EAAE;MAC5E,MAAM,IAAI/X,aAAa,CAAC,IAAI,CAAC,qDAAsD,oFAAmF,GACjK,mFAAkF,CAAC;IAC5F;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOgY,oBAAoBA,CAACC,MAAM,EAAE;IAChC,OAAO;MACHC,QAAQ,EAAEJ,aAAa;MACvBjB,SAAS,EAAE,CACP;QAAEQ,OAAO,EAAEjX,MAAM;QAAEkX,QAAQ,EAAEW,MAAM,CAAC5M;MAAM,CAAC;IAEnD,CAAC;EACL;EACA;IAAS,IAAI,CAACpC,IAAI,YAAAkP,sBAAAzP,CAAA;MAAA,YAAAA,CAAA,IAAyFoP,aAAa,EAn+BvBhY,EAAE,CAAA4B,QAAA,CAm+BuCiW,+BAA+B;IAAA,CAA2E;EAAE;EACtP;IAAS,IAAI,CAACS,IAAI,kBAp+B+EtY,EAAE,CAAAuY,gBAAA;MAAA7O,IAAA,EAo+BSsO;IAAa,EAA+C;EAAE;EAC1K;IAAS,IAAI,CAACQ,IAAI,kBAr+B+ExY,EAAE,CAAAyY,gBAAA;MAAA1B,SAAA,EAq+BmC,CAAC,GAAGD,wBAAwB,EAAE,GAAGK,qBAAqB,CAAC;MAAAuB,OAAA,GAAY5U,YAAY,EAAErC,iBAAiB;IAAA,EAAI;EAAE;AAClP;AACA;EAAA,QAAAqG,SAAA,oBAAAA,SAAA,KAv+BqG9H,EAAE,CAAAyJ,iBAAA,CAu+BXuO,aAAa,EAAc,CAAC;IAC5GtO,IAAI,EAAEhI,QAAQ;IACdwJ,IAAI,EAAE,CAAC;MACC6L,SAAS,EAAE,CAAC,GAAGD,wBAAwB,EAAE,GAAGK,qBAAqB,CAAC;MAClEwB,OAAO,EAAE,CAAC7U,YAAY,EAAErC,iBAAiB;IAC7C,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEiI,IAAI,EAAEsB,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DvB,IAAI,EAAEjJ;MACV,CAAC,EAAE;QACCiJ,IAAI,EAAE/H;MACV,CAAC,EAAE;QACC+H,IAAI,EAAErJ,MAAM;QACZ6K,IAAI,EAAE,CAAC2M,+BAA+B;MAC1C,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA;AACA;AACA,SAASe,UAAUA,CAAA,EAAG;EAClB,OAAO,IAAIC,IAAI,CAACjX,QAAQ,CAAC+B,QAAQ,CAAC,CAAC;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMkV,IAAI,CAAC;EACP5U,WAAWA,CAACmH,IAAI,EAAE;IACd,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAC0N,IAAI,GAAGrV,OAAO,CAAC,CAAC;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIsV,MAAMA,CAACC,GAAG,EAAEC,aAAa,GAAG,KAAK,EAAE;IAC/B,IAAI,CAACD,GAAG,EACJ,OAAO,IAAI;IACf,OAAO,IAAI,CAACE,mBAAmB,CAACF,GAAG,EAAEC,aAAa,CAAC;EACvD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIE,OAAOA,CAACC,IAAI,EAAEH,aAAa,GAAG,KAAK,EAAE;IACjC,IAAI,CAACG,IAAI,EACL,OAAO,EAAE;IACb,OAAOA,IAAI,CAACC,MAAM,CAAC,CAACtD,MAAM,EAAEiD,GAAG,KAAK;MAChC,IAAIA,GAAG,EAAE;QACLjD,MAAM,CAACpN,IAAI,CAAC,IAAI,CAACuQ,mBAAmB,CAACF,GAAG,EAAEC,aAAa,CAAC,CAAC;MAC7D;MACA,OAAOlD,MAAM;IACjB,CAAC,EAAE,EAAE,CAAC;EACV;EACA;AACJ;AACA;AACA;AACA;AACA;EACIuD,MAAMA,CAACC,YAAY,EAAE;IACjB,IAAI,CAACA,YAAY,EACb,OAAO,IAAI;IACf,OAAO,IAAI,CAACnO,IAAI,CAACrE,aAAa,CAAE,QAAOwS,YAAa,GAAE,CAAC,IAAI,IAAI;EACnE;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,OAAOA,CAACD,YAAY,EAAE;IAClB,IAAI,CAACA,YAAY,EACb,OAAO,EAAE;IACb,MAAME,IAAI,CAAC,eAAe,IAAI,CAACrO,IAAI,CAACiC,gBAAgB,CAAE,QAAOkM,YAAa,GAAE,CAAC;IAC7E,OAAOE,IAAI,GAAG,EAAE,CAACrP,KAAK,CAACsP,IAAI,CAACD,IAAI,CAAC,GAAG,EAAE;EAC1C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIE,SAASA,CAACX,GAAG,EAAEY,QAAQ,EAAE;IACrB,IAAI,CAACZ,GAAG,EACJ,OAAO,IAAI;IACfY,QAAQ,GAAGA,QAAQ,IAAI,IAAI,CAACC,cAAc,CAACb,GAAG,CAAC;IAC/C,MAAMc,IAAI,GAAG,IAAI,CAACR,MAAM,CAACM,QAAQ,CAAC;IAClC,IAAIE,IAAI,EAAE;MACN,OAAO,IAAI,CAACC,yBAAyB,CAACf,GAAG,EAAEc,IAAI,CAAC;IACpD;IACA,OAAO,IAAI,CAACZ,mBAAmB,CAACF,GAAG,EAAE,IAAI,CAAC;EAC9C;EACA;AACJ;AACA;AACA;AACA;EACIgB,SAASA,CAACT,YAAY,EAAE;IACpB,IAAI,CAACU,gBAAgB,CAAC,IAAI,CAACX,MAAM,CAACC,YAAY,CAAC,CAAC;EACpD;EACA;AACJ;AACA;AACA;EACIU,gBAAgBA,CAACH,IAAI,EAAE;IACnB,IAAIA,IAAI,EAAE;MACN,IAAI,CAAChB,IAAI,CAACjU,MAAM,CAACiV,IAAI,CAAC;IAC1B;EACJ;EACAZ,mBAAmBA,CAACY,IAAI,EAAEb,aAAa,GAAG,KAAK,EAAE;IAC7C,IAAI,CAACA,aAAa,EAAE;MAChB,MAAMW,QAAQ,GAAG,IAAI,CAACC,cAAc,CAACC,IAAI,CAAC;MAC1C;MACA;MACA;MACA,MAAMpS,IAAI,GAAG,IAAI,CAAC8R,OAAO,CAACI,QAAQ,CAAC,CAACM,MAAM,CAACxS,IAAI,IAAI,IAAI,CAACyS,mBAAmB,CAACL,IAAI,EAAEpS,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3F,IAAIA,IAAI,KAAKsD,SAAS,EAClB,OAAOtD,IAAI;IACnB;IACA,MAAM4C,OAAO,GAAG,IAAI,CAACwO,IAAI,CAAC7T,aAAa,CAAC,MAAM,CAAC;IAC/C,IAAI,CAAC8U,yBAAyB,CAACD,IAAI,EAAExP,OAAO,CAAC;IAC7C,MAAM8C,IAAI,GAAG,IAAI,CAAChC,IAAI,CAACgP,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IACtDhN,IAAI,CAACY,WAAW,CAAC1D,OAAO,CAAC;IACzB,OAAOA,OAAO;EAClB;EACAyP,yBAAyBA,CAACf,GAAG,EAAEzU,EAAE,EAAE;IAC/BiM,MAAM,CAACtD,IAAI,CAAC8L,GAAG,CAAC,CAACvQ,OAAO,CAAE4R,IAAI,IAAK9V,EAAE,CAAC4C,YAAY,CAAC,IAAI,CAACmT,cAAc,CAACD,IAAI,CAAC,EAAErB,GAAG,CAACqB,IAAI,CAAC,CAAC,CAAC;IACzF,OAAO9V,EAAE;EACb;EACAsV,cAAcA,CAACb,GAAG,EAAE;IAChB,MAAMuB,IAAI,GAAGvB,GAAG,CAACnS,IAAI,GAAG,MAAM,GAAG,UAAU;IAC3C,OAAQ,GAAE0T,IAAK,KAAIvB,GAAG,CAACuB,IAAI,CAAE,GAAE;EACnC;EACAJ,mBAAmBA,CAACnB,GAAG,EAAEtR,IAAI,EAAE;IAC3B,OAAO8I,MAAM,CAACtD,IAAI,CAAC8L,GAAG,CAAC,CAACwB,KAAK,CAAElF,GAAG,IAAK5N,IAAI,CAACV,YAAY,CAAC,IAAI,CAACsT,cAAc,CAAChF,GAAG,CAAC,CAAC,KAAK0D,GAAG,CAAC1D,GAAG,CAAC,CAAC;EACpG;EACAgF,cAAcA,CAACD,IAAI,EAAE;IACjB,OAAOI,aAAa,CAACJ,IAAI,CAAC,IAAIA,IAAI;EACtC;EACA;IAAS,IAAI,CAAClR,IAAI,YAAAuR,aAAA9R,CAAA;MAAA,YAAAA,CAAA,IAAyFiQ,IAAI,EAjpCd7Y,EAAE,CAAA4B,QAAA,CAipC8B+B,QAAQ;IAAA,CAA6C;EAAE;EACxL;IAAS,IAAI,CAAC0F,KAAK,kBAlpC8ErJ,EAAE,CAAAsJ,kBAAA;MAAAC,KAAA,EAkpCYsP,IAAI;MAAArP,OAAA,WAAAkR,aAAA9R,CAAA;QAAA,IAAA+R,CAAA;QAAA,IAAA/R,CAAA;UAAA+R,CAAA,OAAA/R,CAAA;QAAA;UAAA+R,CAAA,GAAkC/B,UAAU;QAAA;QAAA,OAAA+B,CAAA;MAAA;MAAAhM,UAAA,EAA9B;IAAM,EAAqC;EAAE;AAClL;AACA;EAAA,QAAA7G,SAAA,oBAAAA,SAAA,KAppCqG9H,EAAE,CAAAyJ,iBAAA,CAopCXoP,IAAI,EAAc,CAAC;IACnGnP,IAAI,EAAEvJ,UAAU;IAChB+K,IAAI,EAAE,CAAC;MAAEyD,UAAU,EAAE,MAAM;MAAE+I,UAAU,EAAEkB,UAAU;MAAEjB,IAAI,EAAE;IAAG,CAAC;EACnE,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEjO,IAAI,EAAEsB,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DvB,IAAI,EAAErJ,MAAM;QACZ6K,IAAI,EAAE,CAACvH,QAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AACxB;AACA;AACA;AACA,MAAM8W,aAAa,GAAG;EAClBG,SAAS,EAAE;AACf,CAAC;;AAED;AACA;AACA;AACA,SAASC,WAAWA,CAAA,EAAG;EACnB,OAAO,IAAIC,KAAK,CAAClZ,QAAQ,CAAC+B,QAAQ,CAAC,CAAC;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMmX,KAAK,CAAC;EACR7W,WAAWA,CAACmH,IAAI,EAAE;IACd,IAAI,CAACA,IAAI,GAAGA,IAAI;EACpB;EACA;AACJ;AACA;EACI2P,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC3P,IAAI,CAAC4P,KAAK;EAC1B;EACA;AACJ;AACA;AACA;EACIC,QAAQA,CAACC,QAAQ,EAAE;IACf,IAAI,CAAC9P,IAAI,CAAC4P,KAAK,GAAGE,QAAQ,IAAI,EAAE;EACpC;EACA;IAAS,IAAI,CAAC/R,IAAI,YAAAgS,cAAAvS,CAAA;MAAA,YAAAA,CAAA,IAAyFkS,KAAK,EAnsCf9a,EAAE,CAAA4B,QAAA,CAmsC+B+B,QAAQ;IAAA,CAA6C;EAAE;EACzL;IAAS,IAAI,CAAC0F,KAAK,kBApsC8ErJ,EAAE,CAAAsJ,kBAAA;MAAAC,KAAA,EAosCYuR,KAAK;MAAAtR,OAAA,WAAA2R,cAAAvS,CAAA;QAAA,IAAA+R,CAAA;QAAA,IAAA/R,CAAA;UAAA+R,CAAA,OAAA/R,CAAA;QAAA;UAAA+R,CAAA,GAAkCE,WAAW;QAAA;QAAA,OAAAF,CAAA;MAAA;MAAAhM,UAAA,EAA/B;IAAM,EAAsC;EAAE;AACpL;AACA;EAAA,QAAA7G,SAAA,oBAAAA,SAAA,KAtsCqG9H,EAAE,CAAAyJ,iBAAA,CAssCXqR,KAAK,EAAc,CAAC;IACpGpR,IAAI,EAAEvJ,UAAU;IAChB+K,IAAI,EAAE,CAAC;MAAEyD,UAAU,EAAE,MAAM;MAAE+I,UAAU,EAAEmD,WAAW;MAAElD,IAAI,EAAE;IAAG,CAAC;EACpE,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEjO,IAAI,EAAEsB,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DvB,IAAI,EAAErJ,MAAM;QACZ6K,IAAI,EAAE,CAACvH,QAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASyX,WAAWA,CAACvU,IAAI,EAAEmK,KAAK,EAAE;EAC9B,IAAI,OAAOqK,QAAQ,KAAK,WAAW,IAAI,CAACA,QAAQ,EAAE;IAC9C;IACA;IACA;IACA;IACA,MAAMC,EAAE,GAAGrb,OAAO,CAAC,IAAI,CAAC,GAAGA,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC9Cqb,EAAE,CAACzU,IAAI,CAAC,GAAGmK,KAAK;EACpB;AACJ;AAEA,MAAMuK,GAAG,GAAG,OAAOtV,MAAM,KAAK,WAAW,IAAIA,MAAM,IAAI,CAAC,CAAC;AAEzD,MAAMuV,yBAAyB,CAAC;EAC5BvX,WAAWA,CAACwX,SAAS,EAAEC,QAAQ,EAAE;IAC7B,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EAC5B;AACJ;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,CAAC;EAClB1X,WAAWA,CAAC2X,GAAG,EAAE;IACb,IAAI,CAACC,MAAM,GAAGD,GAAG,CAACE,QAAQ,CAACnR,GAAG,CAAC9I,cAAc,CAAC;EAClD;EACA;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIka,mBAAmBA,CAACC,MAAM,EAAE;IACxB,MAAMC,MAAM,GAAGD,MAAM,IAAIA,MAAM,CAAC,QAAQ,CAAC;IACzC,MAAME,WAAW,GAAG,kBAAkB;IACtC;IACA,MAAMC,mBAAmB,GAAGZ,GAAG,CAACa,OAAO,CAACC,OAAO,IAAI,IAAI;IACvD,IAAIJ,MAAM,IAAIE,mBAAmB,EAAE;MAC/BZ,GAAG,CAACa,OAAO,CAACC,OAAO,CAACH,WAAW,CAAC;IACpC;IACA,MAAMI,KAAK,GAAGC,cAAc,CAAC,CAAC;IAC9B,IAAIb,QAAQ,GAAG,CAAC;IAChB,OAAOA,QAAQ,GAAG,CAAC,IAAKa,cAAc,CAAC,CAAC,GAAGD,KAAK,GAAI,GAAG,EAAE;MACrD,IAAI,CAACT,MAAM,CAACW,IAAI,CAAC,CAAC;MAClBd,QAAQ,EAAE;IACd;IACA,MAAMe,GAAG,GAAGF,cAAc,CAAC,CAAC;IAC5B,IAAIN,MAAM,IAAIE,mBAAmB,EAAE;MAC/BZ,GAAG,CAACa,OAAO,CAACM,UAAU,CAACR,WAAW,CAAC;IACvC;IACA,MAAMT,SAAS,GAAG,CAACgB,GAAG,GAAGH,KAAK,IAAIZ,QAAQ;IAC1CH,GAAG,CAACa,OAAO,CAACO,GAAG,CAAE,OAAMjB,QAAS,0BAAyB,CAAC;IAC1DH,GAAG,CAACa,OAAO,CAACO,GAAG,CAAE,GAAElB,SAAS,CAACmB,OAAO,CAAC,CAAC,CAAE,eAAc,CAAC;IACvD,OAAO,IAAIpB,yBAAyB,CAACC,SAAS,EAAEC,QAAQ,CAAC;EAC7D;AACJ;AACA,SAASa,cAAcA,CAAA,EAAG;EACtB,OAAOhB,GAAG,CAACsB,WAAW,IAAItB,GAAG,CAACsB,WAAW,CAACC,GAAG,GAAGvB,GAAG,CAACsB,WAAW,CAACC,GAAG,CAAC,CAAC,GACjE,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;AAC5B;AAEA,MAAMC,oBAAoB,GAAG,UAAU;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAACtB,GAAG,EAAE;EAC3BR,WAAW,CAAC6B,oBAAoB,EAAE,IAAItB,eAAe,CAACC,GAAG,CAAC,CAAC;EAC3D,OAAOA,GAAG;AACd;AACA;AACA;AACA;AACA;AACA;AACA,SAASuB,iBAAiBA,CAAA,EAAG;EACzB/B,WAAW,CAAC6B,oBAAoB,EAAE,IAAI,CAAC;AAC3C;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMG,EAAE,CAAC;EACL;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOC,GAAGA,CAAA,EAAG;IACT,OAAO,MAAM,IAAI;EACrB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOC,GAAGA,CAAC1D,QAAQ,EAAE;IACjB,OAAQ2D,YAAY,IAAK;MACrB,OAAOA,YAAY,CAACC,aAAa,IAAI,IAAI,GACrCC,cAAc,CAACF,YAAY,CAACC,aAAa,EAAE5D,QAAQ,CAAC,GACpD,KAAK;IACb,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAO8D,SAASA,CAAChU,IAAI,EAAE;IACnB,OAAQiU,SAAS,IAAKA,SAAS,CAACC,cAAc,CAACjI,OAAO,CAACjM,IAAI,CAAC,KAAK,CAAC,CAAC;EACvE;AACJ;AACA,SAAS+T,cAAcA,CAACI,CAAC,EAAEjE,QAAQ,EAAE;EACjC,IAAInW,OAAO,CAAC,CAAC,CAACgC,aAAa,CAACoY,CAAC,CAAC,EAAE;IAC5B,OAAOA,CAAC,CAACC,OAAO,IAAID,CAAC,CAACC,OAAO,CAAClE,QAAQ,CAAC,IACnCiE,CAAC,CAACE,iBAAiB,IAAIF,CAAC,CAACE,iBAAiB,CAACnE,QAAQ,CAAC,IACpDiE,CAAC,CAACG,qBAAqB,IAAIH,CAAC,CAACG,qBAAqB,CAACpE,QAAQ,CAAC;EACpE;EACA,OAAO,KAAK;AAChB;;AAEA;AACA;AACA;AACA,MAAMqE,WAAW,GAAG;EAChB;EACA,KAAK,EAAE,IAAI;EACX,UAAU,EAAE,IAAI;EAChB,SAAS,EAAE,IAAI;EACf,QAAQ,EAAE,IAAI;EACd,WAAW,EAAE,IAAI;EACjB,SAAS,EAAE,IAAI;EACf,UAAU,EAAE,IAAI;EAChB,OAAO,EAAE,IAAI;EACb,SAAS,EAAE,IAAI;EACf;EACA,OAAO,EAAE,IAAI;EACb,YAAY,EAAE,IAAI;EAClB,WAAW,EAAE,IAAI;EACjB,UAAU,EAAE,IAAI;EAChB,aAAa,EAAE,IAAI;EACnB,SAAS,EAAE,IAAI;EACf,UAAU,EAAE,IAAI;EAChB;EACA,OAAO,EAAE,IAAI;EACb,SAAS,EAAE,IAAI;EACf;EACA,QAAQ,EAAE,IAAI;EACd,aAAa,EAAE,IAAI;EACnB,YAAY,EAAE,IAAI;EAClB,WAAW,EAAE,IAAI;EACjB,cAAc,EAAE,IAAI;EACpB;EACA,OAAO,EAAE,IAAI;EACb,WAAW,EAAE,IAAI;EACjB,YAAY,EAAE,IAAI;EAClB,SAAS,EAAE,IAAI;EACf,WAAW,EAAE,IAAI;EACjB;EACA,KAAK,EAAE,IAAI;EACX,WAAW,EAAE;AACjB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,GAAG,IAAI9d,cAAc,CAAC,qBAAqB,CAAC;AACvE;AACA;AACA;AACA;AACA;AACA,MAAM+d,aAAa,GAAG,IAAI/d,cAAc,CAAC,cAAc,CAAC;AACxD;AACA;AACA;AACA;AACA;AACA,MAAMge,mBAAmB,CAAC;EACtBna,WAAWA,CAAA,EAAG;IACV;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACoa,MAAM,GAAG,EAAE;IAChB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,SAAS,GAAG,CAAC,CAAC;EACvB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,WAAWA,CAACjU,OAAO,EAAE;IACjB,MAAMkU,EAAE,GAAG,IAAIC,MAAM,CAACnU,OAAO,EAAE,IAAI,CAACoM,OAAO,CAAC;IAC5C8H,EAAE,CAAC7T,GAAG,CAAC,OAAO,CAAC,CAACG,GAAG,CAAC;MAAE4T,MAAM,EAAE;IAAK,CAAC,CAAC;IACrCF,EAAE,CAAC7T,GAAG,CAAC,QAAQ,CAAC,CAACG,GAAG,CAAC;MAAE4T,MAAM,EAAE;IAAK,CAAC,CAAC;IACtC,KAAK,MAAMnU,SAAS,IAAI,IAAI,CAAC+T,SAAS,EAAE;MACpCE,EAAE,CAAC7T,GAAG,CAACJ,SAAS,CAAC,CAACO,GAAG,CAAC,IAAI,CAACwT,SAAS,CAAC/T,SAAS,CAAC,CAAC;IACpD;IACA,OAAOiU,EAAE;EACb;EACA;IAAS,IAAI,CAACrV,IAAI,YAAAwV,4BAAA/V,CAAA;MAAA,YAAAA,CAAA,IAAyFwV,mBAAmB;IAAA,CAAoD;EAAE;EACpL;IAAS,IAAI,CAAC/U,KAAK,kBAp9C8ErJ,EAAE,CAAAsJ,kBAAA;MAAAC,KAAA,EAo9CY6U,mBAAmB;MAAA5U,OAAA,EAAnB4U,mBAAmB,CAAAjV;IAAA,EAAG;EAAE;AAC3I;AACA;EAAA,QAAArB,SAAA,oBAAAA,SAAA,KAt9CqG9H,EAAE,CAAAyJ,iBAAA,CAs9CX2U,mBAAmB,EAAc,CAAC;IAClH1U,IAAI,EAAEvJ;EACV,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA,MAAMye,oBAAoB,SAASzT,kBAAkB,CAAC;EAClDlH,WAAWA,CAACkB,GAAG,EAAE0Z,OAAO,EAAEzC,OAAO,EAAE0C,MAAM,EAAE;IACvC,KAAK,CAAC3Z,GAAG,CAAC;IACV,IAAI,CAAC0Z,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACzC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC0C,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,cAAc,GAAG,IAAI;EAC9B;EACAlU,QAAQA,CAACN,SAAS,EAAE;IAChB,IAAI,CAAC0T,WAAW,CAACe,cAAc,CAACzU,SAAS,CAAC2K,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC+J,aAAa,CAAC1U,SAAS,CAAC,EAAE;MACxF,OAAO,KAAK;IAChB;IACA,IAAI,CAACtE,MAAM,CAACwY,MAAM,IAAI,CAAC,IAAI,CAACK,MAAM,EAAE;MAChC,IAAI,OAAOhX,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;QAC/C,IAAI,CAACsU,OAAO,CAAC8C,IAAI,CAAE,QAAO3U,SAAU,mDAAkD,GACjF,iDAAgD,CAAC;MAC1D;MACA,OAAO,KAAK;IAChB;IACA,OAAO,IAAI;EACf;EACA7F,gBAAgBA,CAAC4F,OAAO,EAAEC,SAAS,EAAEC,OAAO,EAAE;IAC1C,MAAM6L,IAAI,GAAG,IAAI,CAACnM,OAAO,CAACQ,OAAO,CAAC,CAAC;IACnCH,SAAS,GAAGA,SAAS,CAAC2K,WAAW,CAAC,CAAC;IACnC;IACA;IACA,IAAI,CAACjP,MAAM,CAACwY,MAAM,IAAI,IAAI,CAACK,MAAM,EAAE;MAC/B,IAAI,CAACC,cAAc,GAAG,IAAI,CAACA,cAAc,IAAI1I,IAAI,CAACrB,iBAAiB,CAAC,MAAM,IAAI,CAAC8J,MAAM,CAAC,CAAC,CAAC;MACxF;MACA;MACA;MACA,IAAIK,kBAAkB,GAAG,KAAK;MAC9B,IAAIC,UAAU,GAAGA,CAAA,KAAM;QACnBD,kBAAkB,GAAG,IAAI;MAC7B,CAAC;MACD9I,IAAI,CAACrB,iBAAiB,CAAC,MAAM,IAAI,CAAC+J,cAAc,CAC3CM,IAAI,CAAC,MAAM;QACZ;QACA,IAAI,CAACpZ,MAAM,CAACwY,MAAM,EAAE;UAChB,IAAI,OAAO3W,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;YAC/C,IAAI,CAACsU,OAAO,CAAC8C,IAAI,CAAE,mEAAkE,CAAC;UAC1F;UACAE,UAAU,GAAGA,CAAA,KAAM,CAAE,CAAC;UACtB;QACJ;QACA,IAAI,CAACD,kBAAkB,EAAE;UACrB;UACA;UACA;UACAC,UAAU,GAAG,IAAI,CAAC1a,gBAAgB,CAAC4F,OAAO,EAAEC,SAAS,EAAEC,OAAO,CAAC;QACnE;MACJ,CAAC,CAAC,CACG8U,KAAK,CAAC,MAAM;QACb,IAAI,OAAOxX,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;UAC/C,IAAI,CAACsU,OAAO,CAAC8C,IAAI,CAAE,QAAO3U,SAAU,6CAA4C,GAC3E,0BAAyB,CAAC;QACnC;QACA6U,UAAU,GAAGA,CAAA,KAAM,CAAE,CAAC;MAC1B,CAAC,CAAC,CAAC;MACH;MACA;MACA;MACA,OAAO,MAAM;QACTA,UAAU,CAAC,CAAC;MAChB,CAAC;IACL;IACA,OAAO/I,IAAI,CAACrB,iBAAiB,CAAC,MAAM;MAChC;MACA,MAAMwJ,EAAE,GAAG,IAAI,CAACK,OAAO,CAACN,WAAW,CAACjU,OAAO,CAAC;MAC5C,MAAMpC,QAAQ,GAAG,SAAAA,CAAUqX,QAAQ,EAAE;QACjClJ,IAAI,CAAChD,UAAU,CAAC,YAAY;UACxB7I,OAAO,CAAC+U,QAAQ,CAAC;QACrB,CAAC,CAAC;MACN,CAAC;MACDf,EAAE,CAACgB,EAAE,CAACjV,SAAS,EAAErC,QAAQ,CAAC;MAC1B,OAAO,MAAM;QACTsW,EAAE,CAACiB,GAAG,CAAClV,SAAS,EAAErC,QAAQ,CAAC;QAC3B;QACA,IAAI,OAAOsW,EAAE,CAAC5N,OAAO,KAAK,UAAU,EAAE;UAClC4N,EAAE,CAAC5N,OAAO,CAAC,CAAC;QAChB;MACJ,CAAC;IACL,CAAC,CAAC;EACN;EACAqO,aAAaA,CAAC1U,SAAS,EAAE;IACrB,OAAO,IAAI,CAACsU,OAAO,CAACR,MAAM,CAAC1I,OAAO,CAACpL,SAAS,CAAC,GAAG,CAAC,CAAC;EACtD;EACA;IAAS,IAAI,CAACpB,IAAI,YAAAuW,6BAAA9W,CAAA;MAAA,YAAAA,CAAA,IAAyFgW,oBAAoB,EArjD9B5e,EAAE,CAAA4B,QAAA,CAqjD8C+B,QAAQ,GArjDxD3D,EAAE,CAAA4B,QAAA,CAqjDmEsc,qBAAqB,GArjD1Fle,EAAE,CAAA4B,QAAA,CAqjDqG5B,EAAE,CAAC8B,QAAQ,GArjDlH9B,EAAE,CAAA4B,QAAA,CAqjD6Huc,aAAa;IAAA,CAA6D;EAAE;EAC5S;IAAS,IAAI,CAAC9U,KAAK,kBAtjD8ErJ,EAAE,CAAAsJ,kBAAA;MAAAC,KAAA,EAsjDYqV,oBAAoB;MAAApV,OAAA,EAApBoV,oBAAoB,CAAAzV;IAAA,EAAG;EAAE;AAC5I;AACA;EAAA,QAAArB,SAAA,oBAAAA,SAAA,KAxjDqG9H,EAAE,CAAAyJ,iBAAA,CAwjDXmV,oBAAoB,EAAc,CAAC;IACnHlV,IAAI,EAAEvJ;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEuJ,IAAI,EAAEsB,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DvB,IAAI,EAAErJ,MAAM;QACZ6K,IAAI,EAAE,CAACvH,QAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAE+F,IAAI,EAAE0U,mBAAmB;MAAEnT,UAAU,EAAE,CAAC;QAC5CvB,IAAI,EAAErJ,MAAM;QACZ6K,IAAI,EAAE,CAACgT,qBAAqB;MAChC,CAAC;IAAE,CAAC,EAAE;MAAExU,IAAI,EAAE1J,EAAE,CAAC8B;IAAS,CAAC,EAAE;MAAE4H,IAAI,EAAEsB,SAAS;MAAEC,UAAU,EAAE,CAAC;QACzDvB,IAAI,EAAEjJ;MACV,CAAC,EAAE;QACCiJ,IAAI,EAAErJ,MAAM;QACZ6K,IAAI,EAAE,CAACiT,aAAa;MACxB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwB,YAAY,CAAC;EACf;IAAS,IAAI,CAACxW,IAAI,YAAAyW,qBAAAhX,CAAA;MAAA,YAAAA,CAAA,IAAyF+W,YAAY;IAAA,CAAkD;EAAE;EAC3K;IAAS,IAAI,CAACrH,IAAI,kBAnlD+EtY,EAAE,CAAAuY,gBAAA;MAAA7O,IAAA,EAmlDSiW;IAAY,EAAG;EAAE;EAC7H;IAAS,IAAI,CAACnH,IAAI,kBAplD+ExY,EAAE,CAAAyY,gBAAA;MAAA1B,SAAA,EAolDkC,CAC7H;QACIQ,OAAO,EAAE5N,qBAAqB;QAC9BmO,QAAQ,EAAE8G,oBAAoB;QAC9BnH,KAAK,EAAE,IAAI;QACXE,IAAI,EAAE,CAAChU,QAAQ,EAAEua,qBAAqB,EAAEpc,QAAQ,EAAE,CAAC,IAAIrB,QAAQ,CAAC,CAAC,EAAE0d,aAAa,CAAC;MACrF,CAAC,EACD;QAAE5G,OAAO,EAAE2G,qBAAqB;QAAEpG,QAAQ,EAAEsG,mBAAmB;QAAEzG,IAAI,EAAE;MAAG,CAAC;IAC9E,EAAG;EAAE;AACd;AACA;EAAA,QAAA7P,SAAA,oBAAAA,SAAA,KA9lDqG9H,EAAE,CAAAyJ,iBAAA,CA8lDXkW,YAAY,EAAc,CAAC;IAC3GjW,IAAI,EAAEhI,QAAQ;IACdwJ,IAAI,EAAE,CAAC;MACC6L,SAAS,EAAE,CACP;QACIQ,OAAO,EAAE5N,qBAAqB;QAC9BmO,QAAQ,EAAE8G,oBAAoB;QAC9BnH,KAAK,EAAE,IAAI;QACXE,IAAI,EAAE,CAAChU,QAAQ,EAAEua,qBAAqB,EAAEpc,QAAQ,EAAE,CAAC,IAAIrB,QAAQ,CAAC,CAAC,EAAE0d,aAAa,CAAC;MACrF,CAAC,EACD;QAAE5G,OAAO,EAAE2G,qBAAqB;QAAEpG,QAAQ,EAAEsG,mBAAmB;QAAEzG,IAAI,EAAE;MAAG,CAAC;IAEnF,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMkI,YAAY,CAAC;EACf;IAAS,IAAI,CAAC1W,IAAI,YAAA2W,qBAAAlX,CAAA;MAAA,YAAAA,CAAA,IAAyFiX,YAAY;IAAA,CAAoD;EAAE;EAC7K;IAAS,IAAI,CAACxW,KAAK,kBA9oD8ErJ,EAAE,CAAAsJ,kBAAA;MAAAC,KAAA,EA8oDYsW,YAAY;MAAArW,OAAA,WAAAsW,qBAAAlX,CAAA;QAAA,IAAA+R,CAAA;QAAA,IAAA/R,CAAA;UAAA+R,CAAA,QAAA/R,CAAA,IAAZiX,YAAY;QAAA;UAAAlF,CAAA,GA9oD1B3a,EAAE,CAAA4B,QAAA,CA8oD8Fme,gBAAgB;QAAA;QAAA,OAAApF,CAAA;MAAA;MAAAhM,UAAA,EAAxE;IAAM,EAAyE;EAAE;AAC9N;AACA;EAAA,QAAA7G,SAAA,oBAAAA,SAAA,KAhpDqG9H,EAAE,CAAAyJ,iBAAA,CAgpDXoW,YAAY,EAAc,CAAC;IAC3GnW,IAAI,EAAEvJ,UAAU;IAChB+K,IAAI,EAAE,CAAC;MAAEyD,UAAU,EAAE,MAAM;MAAEoJ,WAAW,EAAEhW,UAAU,CAAC,MAAMge,gBAAgB;IAAE,CAAC;EAClF,CAAC,CAAC;AAAA;AACV,SAASC,uBAAuBA,CAAClE,QAAQ,EAAE;EACvC,OAAO,IAAIiE,gBAAgB,CAACjE,QAAQ,CAACnR,GAAG,CAAChH,QAAQ,CAAC,CAAC;AACvD;AACA,MAAMoc,gBAAgB,SAASF,YAAY,CAAC;EACxC5b,WAAWA,CAACmH,IAAI,EAAE;IACd,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,IAAI,GAAGA,IAAI;EACpB;EACA6U,QAAQA,CAACC,GAAG,EAAElP,KAAK,EAAE;IACjB,IAAIA,KAAK,IAAI,IAAI,EACb,OAAO,IAAI;IACf,QAAQkP,GAAG;MACP,KAAKje,eAAe,CAACke,IAAI;QACrB,OAAOnP,KAAK;MAChB,KAAK/O,eAAe,CAACme,IAAI;QACrB,IAAIle,gCAAgC,CAAC8O,KAAK,EAAE,MAAM,CAAC,qBAAqB,CAAC,EAAE;UACvE,OAAO7O,gBAAgB,CAAC6O,KAAK,CAAC;QAClC;QACA,OAAO3O,cAAc,CAAC,IAAI,CAAC+I,IAAI,EAAEiV,MAAM,CAACrP,KAAK,CAAC,CAAC,CAACsP,QAAQ,CAAC,CAAC;MAC9D,KAAKre,eAAe,CAACse,KAAK;QACtB,IAAIre,gCAAgC,CAAC8O,KAAK,EAAE,OAAO,CAAC,sBAAsB,CAAC,EAAE;UACzE,OAAO7O,gBAAgB,CAAC6O,KAAK,CAAC;QAClC;QACA,OAAOA,KAAK;MAChB,KAAK/O,eAAe,CAACue,MAAM;QACvB,IAAIte,gCAAgC,CAAC8O,KAAK,EAAE,QAAQ,CAAC,uBAAuB,CAAC,EAAE;UAC3E,OAAO7O,gBAAgB,CAAC6O,KAAK,CAAC;QAClC;QACA,MAAM,IAAI9Q,aAAa,CAAC,IAAI,CAAC,mDAAmD,CAAC,OAAO4H,SAAS,KAAK,WAAW,IAAIA,SAAS,KAC1H,uCAAuC,CAAC;MAChD,KAAK7F,eAAe,CAACwe,GAAG;QACpB,IAAIve,gCAAgC,CAAC8O,KAAK,EAAE,KAAK,CAAC,oBAAoB,CAAC,EAAE;UACrE,OAAO7O,gBAAgB,CAAC6O,KAAK,CAAC;QAClC;QACA,OAAO5O,aAAa,CAACie,MAAM,CAACrP,KAAK,CAAC,CAAC;MACvC,KAAK/O,eAAe,CAACye,YAAY;QAC7B,IAAIxe,gCAAgC,CAAC8O,KAAK,EAAE,aAAa,CAAC,4BAA4B,CAAC,EAAE;UACrF,OAAO7O,gBAAgB,CAAC6O,KAAK,CAAC;QAClC;QACA,MAAM,IAAI9Q,aAAa,CAAC,IAAI,CAAC,yDAAyD,CAAC,OAAO4H,SAAS,KAAK,WAAW,IAAIA,SAAS,KAC/H,oDAAmD9F,iBAAkB,GAAE,CAAC;MACjF;QACI,MAAM,IAAI9B,aAAa,CAAC,IAAI,CAAC,oDAAoD,CAAC,OAAO4H,SAAS,KAAK,WAAW,IAAIA,SAAS,KAC1H,8BAA6BoY,GAAI,SAAQle,iBAAkB,GAAE,CAAC;IAC3E;EACJ;EACA2e,uBAAuBA,CAAC3P,KAAK,EAAE;IAC3B,OAAO1O,4BAA4B,CAAC0O,KAAK,CAAC;EAC9C;EACA4P,wBAAwBA,CAAC5P,KAAK,EAAE;IAC5B,OAAOzO,6BAA6B,CAACyO,KAAK,CAAC;EAC/C;EACA6P,yBAAyBA,CAAC7P,KAAK,EAAE;IAC7B,OAAOxO,8BAA8B,CAACwO,KAAK,CAAC;EAChD;EACA8P,sBAAsBA,CAAC9P,KAAK,EAAE;IAC1B,OAAOvO,2BAA2B,CAACuO,KAAK,CAAC;EAC7C;EACA+P,8BAA8BA,CAAC/P,KAAK,EAAE;IAClC,OAAOtO,mCAAmC,CAACsO,KAAK,CAAC;EACrD;EACA;IAAS,IAAI,CAAC7H,IAAI,YAAA6X,yBAAApY,CAAA;MAAA,YAAAA,CAAA,IAAyFmX,gBAAgB,EAjtD1B/f,EAAE,CAAA4B,QAAA,CAitD0C+B,QAAQ;IAAA,CAA6C;EAAE;EACpM;IAAS,IAAI,CAAC0F,KAAK,kBAltD8ErJ,EAAE,CAAAsJ,kBAAA;MAAAC,KAAA,EAktDYwW,gBAAgB;MAAAvW,OAAA,WAAAwX,yBAAApY,CAAA;QAAA,IAAA+R,CAAA;QAAA,IAAA/R,CAAA;UAAA+R,CAAA,OAAA/R,CAAA;QAAA;UAAA+R,CAAA,GAAkCqF,uBAAuB,CAltDvFhgB,EAAE,CAAA4B,QAAA,CAktDuGe,QAAQ;QAAA;QAAA,OAAAgY,CAAA;MAAA;MAAAhM,UAAA,EAArE;IAAM,EAAqE;EAAE;AAC9N;AACA;EAAA,QAAA7G,SAAA,oBAAAA,SAAA,KAptDqG9H,EAAE,CAAAyJ,iBAAA,CAotDXsW,gBAAgB,EAAc,CAAC;IAC/GrW,IAAI,EAAEvJ,UAAU;IAChB+K,IAAI,EAAE,CAAC;MAAEyD,UAAU,EAAE,MAAM;MAAE+I,UAAU,EAAEsI,uBAAuB;MAAErI,IAAI,EAAE,CAAChV,QAAQ;IAAE,CAAC;EACxF,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE+G,IAAI,EAAEsB,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DvB,IAAI,EAAErJ,MAAM;QACZ6K,IAAI,EAAE,CAACvH,QAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA;AACA;AACA,SAASsd,gBAAgBA,CAACC,IAAI,EAAEnK,SAAS,GAAG,EAAE,EAAE;EAC5C,OAAO;IAAEoK,KAAK,EAAED,IAAI;IAAEE,UAAU,EAAErK;EAAU,CAAC;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASsK,cAAcA,CAAA,EAAG;EACtB;EACA;EACA,OAAOJ,gBAAgB,CAAC,CAAC,CAAC,4CAA4C,CAAC;AAC3E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASK,uBAAuBA,CAAA,EAAG;EAC/B;EACA;EACA,OAAOL,gBAAgB,CAAC,CAAC,CAAC,8CAA8C,CAAC;AAC7E;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,kCAAkCA,CAAA,EAAG;EAC1C,OAAO,CAAC;IACAhK,OAAO,EAAE3U,uBAAuB;IAChC4U,QAAQ,EAAEA,CAAA,KAAM;MACZ,MAAMjI,MAAM,GAAG1M,MAAM,CAACxB,MAAM,CAAC;MAC7B;MACA;MACA,IAAIkO,MAAM,CAACtL,WAAW,KAAK5C,MAAM,EAAE;QAC/B,MAAM+a,OAAO,GAAGvZ,MAAM,CAACf,QAAQ,CAAC;QAChC,MAAM0f,OAAO,GAAG1e,mBAAmB,CAAC,CAAC,IAAI,CAAC,oDAAoD,iEAAiE,GAC3J,uDAAuD,GACvD,kDAAkD,CAAC;QACvD;QACAsZ,OAAO,CAAC8C,IAAI,CAACsC,OAAO,CAAC;MACzB;IACJ,CAAC;IACD/J,KAAK,EAAE;EACX,CAAC,CAAC;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASgK,sBAAsBA,CAAC,GAAGC,QAAQ,EAAE;EACzC,MAAM3K,SAAS,GAAG,EAAE;EACpB,MAAM4K,YAAY,GAAG,IAAI/V,GAAG,CAAC,CAAC;EAC9B,KAAK,MAAM;IAAEwV,UAAU;IAAED;EAAM,CAAC,IAAIO,QAAQ,EAAE;IAC1CC,YAAY,CAAC7U,GAAG,CAACqU,KAAK,CAAC;IACvB,IAAIC,UAAU,CAAC/Y,MAAM,EAAE;MACnB0O,SAAS,CAACpO,IAAI,CAACyY,UAAU,CAAC;IAC9B;EACJ;EACA,OAAOre,wBAAwB,CAAC,CAC3B,OAAO+E,SAAS,KAAK,WAAW,IAAIA,SAAS,GAAIyZ,kCAAkC,CAAC,CAAC,GAAG,EAAE,EAC1FI,YAAY,CAACjU,GAAG,CAAC,CAAC,CAAC,4CAA4C,CAAC,GAAG,EAAE,GAAG1K,iBAAiB,CAAC,CAAC,EAC3F2e,YAAY,CAACjU,GAAG,CAAC,CAAC,CAAC,8CAA8C,CAAC,GAAG,EAAE,GAAG3J,sBAAsB,CAAC,CAAC,EACnGgT,SAAS,CACZ,CAAC;AACN;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM6K,OAAO,GAAG,IAAI3e,OAAO,CAAC,SAAS,CAAC;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,GAAGC,cAAc;AACnC;AACA,MAAMC,aAAa,GAAGC,eAAe;;AAErC;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA,SAAS2U,aAAa,EAAEoF,EAAE,EAAEyC,YAAY,EAAElW,qBAAqB,EAAEC,YAAY,EAAEsU,qBAAqB,EAAEC,aAAa,EAAEC,mBAAmB,EAAEuB,YAAY,EAAE9G,IAAI,EAAEnK,kCAAkC,EAAEoM,KAAK,EAAE1X,aAAa,EAAEwe,OAAO,EAAEpL,oBAAoB,EAAEI,iBAAiB,EAAEuG,iBAAiB,EAAED,gBAAgB,EAAEha,YAAY,EAAE0U,eAAe,EAAE6J,sBAAsB,EAAEvK,+BAA+B,EAAEmK,cAAc,EAAEC,uBAAuB,EAAEld,iBAAiB,IAAIyd,kBAAkB,EAAEta,qBAAqB,IAAIua,sBAAsB,EAAE5N,eAAe,IAAI6N,gBAAgB,EAAE5S,mBAAmB,IAAI6S,oBAAoB,EAAEjC,gBAAgB,IAAIkC,iBAAiB,EAAErD,oBAAoB,IAAIsD,qBAAqB,EAAEjL,mCAAmC,IAAIkL,oCAAoC,EAAExN,eAAe,IAAIyN,gBAAgB,EAAE9W,gBAAgB,IAAI+W,iBAAiB,EAAEjL,cAAc,IAAIkL,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}