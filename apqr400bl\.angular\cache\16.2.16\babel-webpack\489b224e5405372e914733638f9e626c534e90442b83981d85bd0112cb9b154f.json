{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { PlantselectionRoutingModule } from './plantselection-routing.module';\nimport { PlantselectionComponent } from './plantselection.component';\nimport { MatIconModule } from '@angular/material/icon';\nimport * as i0 from \"@angular/core\";\nexport class PlantselectionModule {\n  static {\n    this.ɵfac = function PlantselectionModule_Factory(t) {\n      return new (t || PlantselectionModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: PlantselectionModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, PlantselectionRoutingModule, MatIconModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(PlantselectionModule, {\n    declarations: [PlantselectionComponent],\n    imports: [CommonModule, PlantselectionRoutingModule, MatIconModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "PlantselectionRoutingModule", "PlantselectionComponent", "MatIconModule", "PlantselectionModule", "declarations", "imports"], "sources": ["C:\\cal_wf\\Augment\\4-img\\apqr400bl\\src\\app\\plantselection\\plantselection.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { PlantselectionRoutingModule } from './plantselection-routing.module';\nimport { PlantselectionComponent } from './plantselection.component';\nimport { MatIconModule } from '@angular/material/icon';\n\n@NgModule({\n  declarations: [\n    PlantselectionComponent\n  ],\n  imports: [\n    CommonModule,\n    PlantselectionRoutingModule,\n    MatIconModule\n  ]\n})\nexport class PlantselectionModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,2BAA2B,QAAQ,iCAAiC;AAC7E,SAASC,uBAAuB,QAAQ,4BAA4B;AACpE,SAASC,aAAa,QAAQ,wBAAwB;;AAYtD,OAAM,MAAOC,oBAAoB;;;uBAApBA,oBAAoB;IAAA;EAAA;;;YAApBA;IAAoB;EAAA;;;gBAL7BJ,YAAY,EACZC,2BAA2B,EAC3BE,aAAa;IAAA;EAAA;;;2EAGJC,oBAAoB;IAAAC,YAAA,GAR7BH,uBAAuB;IAAAI,OAAA,GAGvBN,YAAY,EACZC,2BAA2B,EAC3BE,aAAa;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}