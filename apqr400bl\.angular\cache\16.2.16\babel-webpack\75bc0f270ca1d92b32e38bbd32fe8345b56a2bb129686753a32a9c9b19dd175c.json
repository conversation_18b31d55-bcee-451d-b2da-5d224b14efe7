{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { LayoutComponent } from './layout.component';\nimport { DashboardComponent } from './components/dashboard/dashboard.component';\nimport { ApqrFlowComponent } from './components/apqr-flow/apqr-flow.component';\nimport { ReportsComponent } from './components/reports/reports.component';\nimport { AnalyticsComponent } from './components/analytics/analytics.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: LayoutComponent,\n  children: [{\n    path: '',\n    component: DashboardComponent\n  }, {\n    path: 'apqr-flow',\n    component: ApqrFlowComponent\n  }, {\n    path: 'reports',\n    component: ReportsComponent\n  }, {\n    path: 'analytics',\n    component: AnalyticsComponent\n  }]\n}];\nexport class LayoutRoutingModule {\n  static {\n    this.ɵfac = function LayoutRoutingModule_Factory(t) {\n      return new (t || LayoutRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: LayoutRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(LayoutRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "LayoutComponent", "DashboardComponent", "ApqrFlowComponent", "ReportsComponent", "AnalyticsComponent", "routes", "path", "component", "children", "LayoutRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["C:\\cal_wf\\Augment\\4-img\\apqr400bl\\src\\app\\layout\\layout-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { LayoutComponent } from './layout.component';\nimport { DashboardComponent } from './components/dashboard/dashboard.component';\nimport { ApqrFlowComponent } from './components/apqr-flow/apqr-flow.component';\nimport { ReportsComponent } from './components/reports/reports.component';\nimport { AnalyticsComponent } from './components/analytics/analytics.component';\n\nconst routes: Routes = [\n  {\n    path: '',\n    component: LayoutComponent,\n    children: [\n      {\n        path: '',\n        component: DashboardComponent\n      },\n      {\n        path: 'apqr-flow',\n        component: ApqrFlowComponent\n      },\n      {\n        path: 'reports',\n        component: ReportsComponent\n      },\n      {\n        path: 'analytics',\n        component: AnalyticsComponent\n      }\n    ]\n  }\n];\n\n@NgModule({\n  imports: [RouterModule.forChild(routes)],\n  exports: [RouterModule]\n})\nexport class LayoutRoutingModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,eAAe,QAAQ,oBAAoB;AACpD,SAASC,kBAAkB,QAAQ,4CAA4C;AAC/E,SAASC,iBAAiB,QAAQ,4CAA4C;AAC9E,SAASC,gBAAgB,QAAQ,wCAAwC;AACzE,SAASC,kBAAkB,QAAQ,4CAA4C;;;AAE/E,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEP,eAAe;EAC1BQ,QAAQ,EAAE,CACR;IACEF,IAAI,EAAE,EAAE;IACRC,SAAS,EAAEN;GACZ,EACD;IACEK,IAAI,EAAE,WAAW;IACjBC,SAAS,EAAEL;GACZ,EACD;IACEI,IAAI,EAAE,SAAS;IACfC,SAAS,EAAEJ;GACZ,EACD;IACEG,IAAI,EAAE,WAAW;IACjBC,SAAS,EAAEH;GACZ;CAEJ,CACF;AAMD,OAAM,MAAOK,mBAAmB;;;uBAAnBA,mBAAmB;IAAA;EAAA;;;YAAnBA;IAAmB;EAAA;;;gBAHpBV,YAAY,CAACW,QAAQ,CAACL,MAAM,CAAC,EAC7BN,YAAY;IAAA;EAAA;;;2EAEXU,mBAAmB;IAAAE,OAAA,GAAAC,EAAA,CAAAb,YAAA;IAAAc,OAAA,GAFpBd,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}