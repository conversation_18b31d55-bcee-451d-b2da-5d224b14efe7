<div class="dashboard-container">
  <div class="dashboard-grid">
    <!-- Batch Stats Card -->
    <div class="dashboard-card">
      <div class="card-content">
        <div class="stat-item" *ngFor="let stat of batchStats">
          <div class="stat-label">{{ stat.label }}</div>
          <div class="stat-value">{{ stat.value }}</div>
        </div>
      </div>
      <div class="card-footer">
        <span class="footer-text">*previous day data</span>
      </div>
    </div>

    <!-- Deviation Stats Card -->
    <div class="dashboard-card">
      <div class="card-content">
        <div class="stat-item" *ngFor="let stat of deviationStats">
          <div class="stat-label">{{ stat.label }}</div>
          <div class="stat-value">{{ stat.value }}</div>
        </div>
      </div>
      <div class="card-footer">
        <span class="footer-text">*previous day data</span>
      </div>
    </div>

    <!-- APQR Stats Card -->
    <div class="dashboard-card">
      <div class="card-content">
        <div class="stat-item" *ngFor="let stat of apqrStats">
          <div class="stat-label">{{ stat.label }}</div>
          <div class="stat-value">{{ stat.value }}</div>
        </div>
      </div>
    </div>

    <!-- Task Stats Card -->
    <div class="dashboard-card">
      <div class="card-header">
        <h3>My Task</h3>
      </div>
      <div class="card-content">
        <div class="stat-item" *ngFor="let stat of taskStats">
          <div class="stat-label">{{ stat.label }}</div>
          <div class="stat-value">{{ stat.value }}</div>
        </div>
      </div>
    </div>

    <!-- APQR Scheduler Card -->
    <div class="dashboard-card wide-card">
      <div class="card-header">
        <h3>aPQR Scheduler</h3>
      </div>
      <div class="card-content">
        <div class="scheduler-header">
          <div class="month-selector">
            <button mat-icon-button><mat-icon>chevron_left</mat-icon></button>
            <span class="month">April 2025</span>
            <button mat-icon-button><mat-icon>chevron_right</mat-icon></button>
          </div>
        </div>
        <div class="scheduler-content">
          <div class="scheduler-table">
            <div class="scheduler-row header">
              <div class="scheduler-cell">Product</div>
            </div>
            <div class="scheduler-row" *ngFor="let item of schedulerItems">
              <div class="scheduler-cell">{{ item.id }}</div>
              <div class="scheduler-cell status" [ngClass]="item.status.toLowerCase().replace(' ', '-')">
                {{ item.status }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- APQR Calendar Card -->
    <div class="dashboard-card wide-card">
      <div class="card-header">
        <h3>aPQR Calender</h3>
      </div>
      <div class="card-content">
        <div class="calendar-header">
          <div class="month-selector">
            <button mat-icon-button><mat-icon>chevron_left</mat-icon></button>
            <span class="month">April 2025</span>
            <button mat-icon-button><mat-icon>chevron_right</mat-icon></button>
          </div>
        </div>
        <div class="calendar-content">
          <div class="calendar-grid">
            <div class="calendar-weekdays">
              <div class="weekday">Mo</div>
              <div class="weekday">Tu</div>
              <div class="weekday">We</div>
              <div class="weekday">Th</div>
              <div class="weekday">Fr</div>
              <div class="weekday">Sa</div>
              <div class="weekday">Su</div>
            </div>
            <div class="calendar-days">
              <!-- Previous month days -->
              <div class="day prev-month">28</div>
              <div class="day prev-month">29</div>
              <div class="day prev-month">30</div>
              <div class="day prev-month">31</div>
              
              <!-- Current month days -->
              <div class="day">1</div>
              <div class="day">2</div>
              <div class="day">3</div>
              <div class="day">4</div>
              <div class="day">5</div>
              <div class="day">6</div>
              <div class="day">7</div>
              <div class="day">8</div>
              <div class="day">9</div>
              <div class="day">10</div>
              <div class="day">11</div>
              <div class="day">12</div>
              <div class="day">13</div>
              <div class="day published">14</div>
              <div class="day">15</div>
              <div class="day">16</div>
              <div class="day">17</div>
              <div class="day">18</div>
              <div class="day">19</div>
              <div class="day delayed">20</div>
              <div class="day">21</div>
              <div class="day">22</div>
              <div class="day">23</div>
              <div class="day">24</div>
              <div class="day">25</div>
              <div class="day">26</div>
              <div class="day">27</div>
              <div class="day">28</div>
              <div class="day">29</div>
              <div class="day">30</div>
            </div>
          </div>
          <div class="calendar-legend">
            <div class="legend-item">
              <div class="legend-color published"></div>
              <div class="legend-text">Published</div>
            </div>
            <div class="legend-item">
              <div class="legend-color upcoming"></div>
              <div class="legend-text">Upcoming</div>
            </div>
            <div class="legend-item">
              <div class="legend-color delayed"></div>
              <div class="legend-text">Delayed</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
