{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../shared/selection.service\";\nimport * as i2 from \"@angular/material/icon\";\nexport class TopnavComponent {\n  constructor(selectionService) {\n    this.selectionService = selectionService;\n    this.selectedModule = '';\n    this.selectedPlant = '';\n    this.pageTitle = 'Dashboard';\n  }\n  ngOnInit() {\n    this.selectionService.selectedModule$.subscribe(module => {\n      this.selectedModule = module;\n    });\n    this.selectionService.selectedPlant$.subscribe(plant => {\n      this.selectedPlant = plant;\n    });\n  }\n  static {\n    this.ɵfac = function TopnavComponent_Factory(t) {\n      return new (t || TopnavComponent)(i0.ɵɵdirectiveInject(i1.SelectionService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TopnavComponent,\n      selectors: [[\"app-topnav\"]],\n      decls: 23,\n      vars: 2,\n      consts: [[1, \"topnav\"], [1, \"page-title\"], [1, \"info-icon\"], [1, \"topnav-actions\"], [1, \"module-info\"], [1, \"module-badge\"], [1, \"plant-badge\"], [1, \"user-info\"], [1, \"user-avatar\"], [\"src\", \"assets/user-avatar.svg\", \"alt\", \"User Avatar\"], [1, \"user-greeting\"]],\n      template: function TopnavComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n          i0.ɵɵtext(3, \"Dashboard \");\n          i0.ɵɵelementStart(4, \"mat-icon\", 2);\n          i0.ɵɵtext(5, \"info\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(6, \"div\", 3)(7, \"div\", 4)(8, \"div\", 5)(9, \"mat-icon\");\n          i0.ɵɵtext(10, \"refresh\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"div\", 6)(13, \"mat-icon\");\n          i0.ɵɵtext(14, \"location_on\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(15);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"div\", 7)(17, \"div\", 8);\n          i0.ɵɵelement(18, \"img\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"div\", 10);\n          i0.ɵɵtext(20, \" Hello, Sumathi \");\n          i0.ɵɵelementStart(21, \"mat-icon\");\n          i0.ɵɵtext(22, \"arrow_drop_down\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵtextInterpolate1(\" \", ctx.selectedModule, \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", ctx.selectedPlant, \" \");\n        }\n      },\n      dependencies: [i2.MatIcon],\n      styles: [\".topnav[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 1rem 2rem;\\n  background-color: white;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\\n}\\n\\n.page-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\nh1[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 500;\\n  margin: 0;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.info-icon[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  width: 1.2rem;\\n  height: 1.2rem;\\n  margin-left: 0.5rem;\\n  color: #2196f3;\\n  cursor: pointer;\\n}\\n\\n.topnav-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.module-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-right: 2rem;\\n}\\n\\n.module-badge[_ngcontent-%COMP%], .plant-badge[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 0.5rem 1rem;\\n  background-color: #f5f5f5;\\n  border-radius: 4px;\\n  margin-right: 0.5rem;\\n  font-size: 0.9rem;\\n}\\n\\n.module-badge[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%], .plant-badge[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  width: 1rem;\\n  height: 1rem;\\n  margin-right: 0.5rem;\\n}\\n\\n.user-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.user-avatar[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  overflow: hidden;\\n  margin-right: 0.5rem;\\n}\\n\\n.user-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n\\n.user-greeting[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  color: #333;\\n  font-size: 0.9rem;\\n  cursor: pointer;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["TopnavComponent", "constructor", "selectionService", "selectedModule", "selectedPlant", "pageTitle", "ngOnInit", "selectedModule$", "subscribe", "module", "selectedPlant$", "plant", "i0", "ɵɵdirectiveInject", "i1", "SelectionService", "selectors", "decls", "vars", "consts", "template", "TopnavComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate1"], "sources": ["C:\\cal_wf\\Augment\\4-img\\apqr400bl\\src\\app\\layout\\components\\topnav\\topnav.component.ts", "C:\\cal_wf\\Augment\\4-img\\apqr400bl\\src\\app\\layout\\components\\topnav\\topnav.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { SelectionService } from '../../../shared/selection.service';\n\n@Component({\n  selector: 'app-topnav',\n  templateUrl: './topnav.component.html',\n  styleUrls: ['./topnav.component.scss']\n})\nexport class TopnavComponent implements OnInit {\n  selectedModule = '';\n  selectedPlant = '';\n  pageTitle = 'Dashboard';\n\n  constructor(private selectionService: SelectionService) { }\n\n  ngOnInit(): void {\n    this.selectionService.selectedModule$.subscribe(module => {\n      this.selectedModule = module;\n    });\n    \n    this.selectionService.selectedPlant$.subscribe(plant => {\n      this.selectedPlant = plant;\n    });\n  }\n}\n", "<div class=\"topnav\">\n  <div class=\"page-title\">\n    <h1>Dashboard <mat-icon class=\"info-icon\">info</mat-icon></h1>\n  </div>\n  \n  <div class=\"topnav-actions\">\n    <div class=\"module-info\">\n      <div class=\"module-badge\">\n        <mat-icon>refresh</mat-icon> {{ selectedModule }}\n      </div>\n      <div class=\"plant-badge\">\n        <mat-icon>location_on</mat-icon> {{ selectedPlant }}\n      </div>\n    </div>\n    \n    <div class=\"user-info\">\n      <div class=\"user-avatar\">\n        <img src=\"assets/user-avatar.svg\" alt=\"User Avatar\">\n      </div>\n      <div class=\"user-greeting\">\n        Hello, Sumathi <mat-icon>arrow_drop_down</mat-icon>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;;AAQA,OAAM,MAAOA,eAAe;EAK1BC,YAAoBC,gBAAkC;IAAlC,KAAAA,gBAAgB,GAAhBA,gBAAgB;IAJpC,KAAAC,cAAc,GAAG,EAAE;IACnB,KAAAC,aAAa,GAAG,EAAE;IAClB,KAAAC,SAAS,GAAG,WAAW;EAEmC;EAE1DC,QAAQA,CAAA;IACN,IAAI,CAACJ,gBAAgB,CAACK,eAAe,CAACC,SAAS,CAACC,MAAM,IAAG;MACvD,IAAI,CAACN,cAAc,GAAGM,MAAM;IAC9B,CAAC,CAAC;IAEF,IAAI,CAACP,gBAAgB,CAACQ,cAAc,CAACF,SAAS,CAACG,KAAK,IAAG;MACrD,IAAI,CAACP,aAAa,GAAGO,KAAK;IAC5B,CAAC,CAAC;EACJ;;;uBAfWX,eAAe,EAAAY,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAAff,eAAe;MAAAgB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCR5BV,EAAA,CAAAY,cAAA,aAAoB;UAEZZ,EAAA,CAAAa,MAAA,iBAAU;UAAAb,EAAA,CAAAY,cAAA,kBAA4B;UAAAZ,EAAA,CAAAa,MAAA,WAAI;UAAAb,EAAA,CAAAc,YAAA,EAAW;UAG3Dd,EAAA,CAAAY,cAAA,aAA4B;UAGZZ,EAAA,CAAAa,MAAA,eAAO;UAAAb,EAAA,CAAAc,YAAA,EAAW;UAACd,EAAA,CAAAa,MAAA,IAC/B;UAAAb,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,cAAyB;UACbZ,EAAA,CAAAa,MAAA,mBAAW;UAAAb,EAAA,CAAAc,YAAA,EAAW;UAACd,EAAA,CAAAa,MAAA,IACnC;UAAAb,EAAA,CAAAc,YAAA,EAAM;UAGRd,EAAA,CAAAY,cAAA,cAAuB;UAEnBZ,EAAA,CAAAe,SAAA,cAAoD;UACtDf,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,eAA2B;UACzBZ,EAAA,CAAAa,MAAA,wBAAe;UAAAb,EAAA,CAAAY,cAAA,gBAAU;UAAAZ,EAAA,CAAAa,MAAA,uBAAe;UAAAb,EAAA,CAAAc,YAAA,EAAW;;;UAZtBd,EAAA,CAAAgB,SAAA,IAC/B;UAD+BhB,EAAA,CAAAiB,kBAAA,MAAAN,GAAA,CAAApB,cAAA,MAC/B;UAEmCS,EAAA,CAAAgB,SAAA,GACnC;UADmChB,EAAA,CAAAiB,kBAAA,MAAAN,GAAA,CAAAnB,aAAA,MACnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}