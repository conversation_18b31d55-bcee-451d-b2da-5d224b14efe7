{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, Directive, Optional, Inject, Input, NgModule } from '@angular/core';\nimport { mixinDisabled, MatCommonModule } from '@angular/material/core';\nimport * as i1 from '@angular/cdk/a11y';\nimport { Interactivity<PERSON>hecker, A11yModule } from '@angular/cdk/a11y';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { DOCUMENT } from '@angular/common';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nlet nextId = 0;\n// Boilerplate for applying mixins to MatBadge.\n/** @docs-private */\nconst _MatBadgeBase = mixinDisabled(class {});\nconst BADGE_CONTENT_CLASS = 'mat-badge-content';\n/** Directive to display a text badge. */\nclass MatBadge extends _MatBadgeBase {\n  /** The color of the badge. Can be `primary`, `accent`, or `warn`. */\n  get color() {\n    return this._color;\n  }\n  set color(value) {\n    this._setColor(value);\n    this._color = value;\n  }\n  /** Whether the badge should overlap its contents or not */\n  get overlap() {\n    return this._overlap;\n  }\n  set overlap(val) {\n    this._overlap = coerceBooleanProperty(val);\n  }\n  /** The content for the badge */\n  get content() {\n    return this._content;\n  }\n  set content(newContent) {\n    this._updateRenderedContent(newContent);\n  }\n  /** Message used to describe the decorated element via aria-describedby */\n  get description() {\n    return this._description;\n  }\n  set description(newDescription) {\n    this._updateDescription(newDescription);\n  }\n  /** Whether the badge is hidden. */\n  get hidden() {\n    return this._hidden;\n  }\n  set hidden(val) {\n    this._hidden = coerceBooleanProperty(val);\n  }\n  constructor(_ngZone, _elementRef, _ariaDescriber, _renderer, _animationMode) {\n    super();\n    this._ngZone = _ngZone;\n    this._elementRef = _elementRef;\n    this._ariaDescriber = _ariaDescriber;\n    this._renderer = _renderer;\n    this._animationMode = _animationMode;\n    this._color = 'primary';\n    this._overlap = true;\n    /**\n     * Position the badge should reside.\n     * Accepts any combination of 'above'|'below' and 'before'|'after'\n     */\n    this.position = 'above after';\n    /** Size of the badge. Can be 'small', 'medium', or 'large'. */\n    this.size = 'medium';\n    /** Unique id for the badge */\n    this._id = nextId++;\n    /** Whether the OnInit lifecycle hook has run yet */\n    this._isInitialized = false;\n    /** InteractivityChecker to determine if the badge host is focusable. */\n    this._interactivityChecker = inject(InteractivityChecker);\n    this._document = inject(DOCUMENT);\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      const nativeElement = _elementRef.nativeElement;\n      if (nativeElement.nodeType !== nativeElement.ELEMENT_NODE) {\n        throw Error('matBadge must be attached to an element node.');\n      }\n      const matIconTagName = 'mat-icon';\n      // Heads-up for developers to avoid putting matBadge on <mat-icon>\n      // as it is aria-hidden by default docs mention this at:\n      // https://material.angular.io/components/badge/overview#accessibility\n      if (nativeElement.tagName.toLowerCase() === matIconTagName && nativeElement.getAttribute('aria-hidden') === 'true') {\n        console.warn(`Detected a matBadge on an \"aria-hidden\" \"<mat-icon>\". ` + `Consider setting aria-hidden=\"false\" in order to surface the information assistive technology.` + `\\n${nativeElement.outerHTML}`);\n      }\n    }\n  }\n  /** Whether the badge is above the host or not */\n  isAbove() {\n    return this.position.indexOf('below') === -1;\n  }\n  /** Whether the badge is after the host or not */\n  isAfter() {\n    return this.position.indexOf('before') === -1;\n  }\n  /**\n   * Gets the element into which the badge's content is being rendered. Undefined if the element\n   * hasn't been created (e.g. if the badge doesn't have content).\n   */\n  getBadgeElement() {\n    return this._badgeElement;\n  }\n  ngOnInit() {\n    // We may have server-side rendered badge that we need to clear.\n    // We need to do this in ngOnInit because the full content of the component\n    // on which the badge is attached won't necessarily be in the DOM until this point.\n    this._clearExistingBadges();\n    if (this.content && !this._badgeElement) {\n      this._badgeElement = this._createBadgeElement();\n      this._updateRenderedContent(this.content);\n    }\n    this._isInitialized = true;\n  }\n  ngOnDestroy() {\n    // ViewEngine only: when creating a badge through the Renderer, Angular remembers its index.\n    // We have to destroy it ourselves, otherwise it'll be retained in memory.\n    if (this._renderer.destroyNode) {\n      this._renderer.destroyNode(this._badgeElement);\n      this._inlineBadgeDescription?.remove();\n    }\n    this._ariaDescriber.removeDescription(this._elementRef.nativeElement, this.description);\n  }\n  /** Gets whether the badge's host element is interactive. */\n  _isHostInteractive() {\n    // Ignore visibility since it requires an expensive style caluclation.\n    return this._interactivityChecker.isFocusable(this._elementRef.nativeElement, {\n      ignoreVisibility: true\n    });\n  }\n  /** Creates the badge element */\n  _createBadgeElement() {\n    const badgeElement = this._renderer.createElement('span');\n    const activeClass = 'mat-badge-active';\n    badgeElement.setAttribute('id', `mat-badge-content-${this._id}`);\n    // The badge is aria-hidden because we don't want it to appear in the page's navigation\n    // flow. Instead, we use the badge to describe the decorated element with aria-describedby.\n    badgeElement.setAttribute('aria-hidden', 'true');\n    badgeElement.classList.add(BADGE_CONTENT_CLASS);\n    if (this._animationMode === 'NoopAnimations') {\n      badgeElement.classList.add('_mat-animation-noopable');\n    }\n    this._elementRef.nativeElement.appendChild(badgeElement);\n    // animate in after insertion\n    if (typeof requestAnimationFrame === 'function' && this._animationMode !== 'NoopAnimations') {\n      this._ngZone.runOutsideAngular(() => {\n        requestAnimationFrame(() => {\n          badgeElement.classList.add(activeClass);\n        });\n      });\n    } else {\n      badgeElement.classList.add(activeClass);\n    }\n    return badgeElement;\n  }\n  /** Update the text content of the badge element in the DOM, creating the element if necessary. */\n  _updateRenderedContent(newContent) {\n    const newContentNormalized = `${newContent ?? ''}`.trim();\n    // Don't create the badge element if the directive isn't initialized because we want to\n    // append the badge element to the *end* of the host element's content for backwards\n    // compatibility.\n    if (this._isInitialized && newContentNormalized && !this._badgeElement) {\n      this._badgeElement = this._createBadgeElement();\n    }\n    if (this._badgeElement) {\n      this._badgeElement.textContent = newContentNormalized;\n    }\n    this._content = newContentNormalized;\n  }\n  /** Updates the host element's aria description via AriaDescriber. */\n  _updateDescription(newDescription) {\n    // Always start by removing the aria-describedby; we will add a new one if necessary.\n    this._ariaDescriber.removeDescription(this._elementRef.nativeElement, this.description);\n    // NOTE: We only check whether the host is interactive here, which happens during\n    // when then badge content changes. It is possible that the host changes\n    // interactivity status separate from one of these. However, watching the interactivity\n    // status of the host would require a `MutationObserver`, which is likely more code + overhead\n    // than it's worth; from usages inside Google, we see that the vats majority of badges either\n    // never change interactivity, or also set `matBadgeHidden` based on the same condition.\n    if (!newDescription || this._isHostInteractive()) {\n      this._removeInlineDescription();\n    }\n    this._description = newDescription;\n    // We don't add `aria-describedby` for non-interactive hosts elements because we\n    // instead insert the description inline.\n    if (this._isHostInteractive()) {\n      this._ariaDescriber.describe(this._elementRef.nativeElement, newDescription);\n    } else {\n      this._updateInlineDescription();\n    }\n  }\n  _updateInlineDescription() {\n    // Create the inline description element if it doesn't exist\n    if (!this._inlineBadgeDescription) {\n      this._inlineBadgeDescription = this._document.createElement('span');\n      this._inlineBadgeDescription.classList.add('cdk-visually-hidden');\n    }\n    this._inlineBadgeDescription.textContent = this.description;\n    this._badgeElement?.appendChild(this._inlineBadgeDescription);\n  }\n  _removeInlineDescription() {\n    this._inlineBadgeDescription?.remove();\n    this._inlineBadgeDescription = undefined;\n  }\n  /** Adds css theme class given the color to the component host */\n  _setColor(colorPalette) {\n    const classList = this._elementRef.nativeElement.classList;\n    classList.remove(`mat-badge-${this._color}`);\n    if (colorPalette) {\n      classList.add(`mat-badge-${colorPalette}`);\n    }\n  }\n  /** Clears any existing badges that might be left over from server-side rendering. */\n  _clearExistingBadges() {\n    // Only check direct children of this host element in order to avoid deleting\n    // any badges that might exist in descendant elements.\n    const badges = this._elementRef.nativeElement.querySelectorAll(`:scope > .${BADGE_CONTENT_CLASS}`);\n    for (const badgeElement of Array.from(badges)) {\n      if (badgeElement !== this._badgeElement) {\n        badgeElement.remove();\n      }\n    }\n  }\n  static {\n    this.ɵfac = function MatBadge_Factory(t) {\n      return new (t || MatBadge)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.AriaDescriber), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatBadge,\n      selectors: [[\"\", \"matBadge\", \"\"]],\n      hostAttrs: [1, \"mat-badge\"],\n      hostVars: 20,\n      hostBindings: function MatBadge_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-badge-overlap\", ctx.overlap)(\"mat-badge-above\", ctx.isAbove())(\"mat-badge-below\", !ctx.isAbove())(\"mat-badge-before\", !ctx.isAfter())(\"mat-badge-after\", ctx.isAfter())(\"mat-badge-small\", ctx.size === \"small\")(\"mat-badge-medium\", ctx.size === \"medium\")(\"mat-badge-large\", ctx.size === \"large\")(\"mat-badge-hidden\", ctx.hidden || !ctx.content)(\"mat-badge-disabled\", ctx.disabled);\n        }\n      },\n      inputs: {\n        disabled: [\"matBadgeDisabled\", \"disabled\"],\n        color: [\"matBadgeColor\", \"color\"],\n        overlap: [\"matBadgeOverlap\", \"overlap\"],\n        position: [\"matBadgePosition\", \"position\"],\n        content: [\"matBadge\", \"content\"],\n        description: [\"matBadgeDescription\", \"description\"],\n        size: [\"matBadgeSize\", \"size\"],\n        hidden: [\"matBadgeHidden\", \"hidden\"]\n      },\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatBadge, [{\n    type: Directive,\n    args: [{\n      selector: '[matBadge]',\n      inputs: ['disabled: matBadgeDisabled'],\n      host: {\n        'class': 'mat-badge',\n        '[class.mat-badge-overlap]': 'overlap',\n        '[class.mat-badge-above]': 'isAbove()',\n        '[class.mat-badge-below]': '!isAbove()',\n        '[class.mat-badge-before]': '!isAfter()',\n        '[class.mat-badge-after]': 'isAfter()',\n        '[class.mat-badge-small]': 'size === \"small\"',\n        '[class.mat-badge-medium]': 'size === \"medium\"',\n        '[class.mat-badge-large]': 'size === \"large\"',\n        '[class.mat-badge-hidden]': 'hidden || !content',\n        '[class.mat-badge-disabled]': 'disabled'\n      }\n    }]\n  }], function () {\n    return [{\n      type: i0.NgZone\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i1.AriaDescriber\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }];\n  }, {\n    color: [{\n      type: Input,\n      args: ['matBadgeColor']\n    }],\n    overlap: [{\n      type: Input,\n      args: ['matBadgeOverlap']\n    }],\n    position: [{\n      type: Input,\n      args: ['matBadgePosition']\n    }],\n    content: [{\n      type: Input,\n      args: ['matBadge']\n    }],\n    description: [{\n      type: Input,\n      args: ['matBadgeDescription']\n    }],\n    size: [{\n      type: Input,\n      args: ['matBadgeSize']\n    }],\n    hidden: [{\n      type: Input,\n      args: ['matBadgeHidden']\n    }]\n  });\n})();\nclass MatBadgeModule {\n  static {\n    this.ɵfac = function MatBadgeModule_Factory(t) {\n      return new (t || MatBadgeModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatBadgeModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [A11yModule, MatCommonModule, MatCommonModule]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatBadgeModule, [{\n    type: NgModule,\n    args: [{\n      imports: [A11yModule, MatCommonModule],\n      exports: [MatBadge, MatCommonModule],\n      declarations: [MatBadge]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MatBadge, MatBadgeModule };", "map": {"version": 3, "names": ["i0", "inject", "Directive", "Optional", "Inject", "Input", "NgModule", "mixinDisabled", "MatCommonModule", "i1", "InteractivityChecker", "A11yModule", "coerceBooleanProperty", "DOCUMENT", "ANIMATION_MODULE_TYPE", "nextId", "_MatBadgeBase", "BADGE_CONTENT_CLASS", "MatBadge", "color", "_color", "value", "_setColor", "overlap", "_overlap", "val", "content", "_content", "newContent", "_updateR<PERSON><PERSON><PERSON><PERSON>nt", "description", "_description", "newDescription", "_updateDescription", "hidden", "_hidden", "constructor", "_ngZone", "_elementRef", "_ariaDescriber", "_renderer", "_animationMode", "position", "size", "_id", "_isInitialized", "_interactivityC<PERSON>cker", "_document", "ngDevMode", "nativeElement", "nodeType", "ELEMENT_NODE", "Error", "matIconTagName", "tagName", "toLowerCase", "getAttribute", "console", "warn", "outerHTML", "isAbove", "indexOf", "isAfter", "getBadgeElement", "_badgeElement", "ngOnInit", "_clearExistingBadges", "_createBadgeElement", "ngOnDestroy", "destroyNode", "_inlineBadgeDescription", "remove", "removeDescription", "_isHostInteractive", "isFocusable", "ignoreVisibility", "badgeElement", "createElement", "activeClass", "setAttribute", "classList", "add", "append<PERSON><PERSON><PERSON>", "requestAnimationFrame", "runOutsideAngular", "newContentNormalized", "trim", "textContent", "_removeInlineDescription", "describe", "_updateInlineDescription", "undefined", "colorPalette", "badges", "querySelectorAll", "Array", "from", "ɵfac", "MatBadge_Factory", "t", "ɵɵdirectiveInject", "NgZone", "ElementRef", "AriaDescriber", "Renderer2", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostAttrs", "hostVars", "hostBindings", "MatBadge_HostBindings", "rf", "ctx", "ɵɵclassProp", "disabled", "inputs", "features", "ɵɵInheritDefinitionFeature", "ɵsetClassMetadata", "args", "selector", "host", "decorators", "MatBadgeModule", "MatBadgeModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["C:/cal_wf/Augment/4-img/apqr400bl/node_modules/@angular/material/fesm2022/badge.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, Directive, Optional, Inject, Input, NgModule } from '@angular/core';\nimport { mixinDisabled, MatCommonModule } from '@angular/material/core';\nimport * as i1 from '@angular/cdk/a11y';\nimport { Interactivity<PERSON>hecker, A11yModule } from '@angular/cdk/a11y';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { DOCUMENT } from '@angular/common';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\n\nlet nextId = 0;\n// Boilerplate for applying mixins to MatBadge.\n/** @docs-private */\nconst _MatBadgeBase = mixinDisabled(class {\n});\nconst BADGE_CONTENT_CLASS = 'mat-badge-content';\n/** Directive to display a text badge. */\nclass MatBadge extends _MatBadgeBase {\n    /** The color of the badge. Can be `primary`, `accent`, or `warn`. */\n    get color() {\n        return this._color;\n    }\n    set color(value) {\n        this._setColor(value);\n        this._color = value;\n    }\n    /** Whether the badge should overlap its contents or not */\n    get overlap() {\n        return this._overlap;\n    }\n    set overlap(val) {\n        this._overlap = coerceBooleanProperty(val);\n    }\n    /** The content for the badge */\n    get content() {\n        return this._content;\n    }\n    set content(newContent) {\n        this._updateRenderedContent(newContent);\n    }\n    /** Message used to describe the decorated element via aria-describedby */\n    get description() {\n        return this._description;\n    }\n    set description(newDescription) {\n        this._updateDescription(newDescription);\n    }\n    /** Whether the badge is hidden. */\n    get hidden() {\n        return this._hidden;\n    }\n    set hidden(val) {\n        this._hidden = coerceBooleanProperty(val);\n    }\n    constructor(_ngZone, _elementRef, _ariaDescriber, _renderer, _animationMode) {\n        super();\n        this._ngZone = _ngZone;\n        this._elementRef = _elementRef;\n        this._ariaDescriber = _ariaDescriber;\n        this._renderer = _renderer;\n        this._animationMode = _animationMode;\n        this._color = 'primary';\n        this._overlap = true;\n        /**\n         * Position the badge should reside.\n         * Accepts any combination of 'above'|'below' and 'before'|'after'\n         */\n        this.position = 'above after';\n        /** Size of the badge. Can be 'small', 'medium', or 'large'. */\n        this.size = 'medium';\n        /** Unique id for the badge */\n        this._id = nextId++;\n        /** Whether the OnInit lifecycle hook has run yet */\n        this._isInitialized = false;\n        /** InteractivityChecker to determine if the badge host is focusable. */\n        this._interactivityChecker = inject(InteractivityChecker);\n        this._document = inject(DOCUMENT);\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            const nativeElement = _elementRef.nativeElement;\n            if (nativeElement.nodeType !== nativeElement.ELEMENT_NODE) {\n                throw Error('matBadge must be attached to an element node.');\n            }\n            const matIconTagName = 'mat-icon';\n            // Heads-up for developers to avoid putting matBadge on <mat-icon>\n            // as it is aria-hidden by default docs mention this at:\n            // https://material.angular.io/components/badge/overview#accessibility\n            if (nativeElement.tagName.toLowerCase() === matIconTagName &&\n                nativeElement.getAttribute('aria-hidden') === 'true') {\n                console.warn(`Detected a matBadge on an \"aria-hidden\" \"<mat-icon>\". ` +\n                    `Consider setting aria-hidden=\"false\" in order to surface the information assistive technology.` +\n                    `\\n${nativeElement.outerHTML}`);\n            }\n        }\n    }\n    /** Whether the badge is above the host or not */\n    isAbove() {\n        return this.position.indexOf('below') === -1;\n    }\n    /** Whether the badge is after the host or not */\n    isAfter() {\n        return this.position.indexOf('before') === -1;\n    }\n    /**\n     * Gets the element into which the badge's content is being rendered. Undefined if the element\n     * hasn't been created (e.g. if the badge doesn't have content).\n     */\n    getBadgeElement() {\n        return this._badgeElement;\n    }\n    ngOnInit() {\n        // We may have server-side rendered badge that we need to clear.\n        // We need to do this in ngOnInit because the full content of the component\n        // on which the badge is attached won't necessarily be in the DOM until this point.\n        this._clearExistingBadges();\n        if (this.content && !this._badgeElement) {\n            this._badgeElement = this._createBadgeElement();\n            this._updateRenderedContent(this.content);\n        }\n        this._isInitialized = true;\n    }\n    ngOnDestroy() {\n        // ViewEngine only: when creating a badge through the Renderer, Angular remembers its index.\n        // We have to destroy it ourselves, otherwise it'll be retained in memory.\n        if (this._renderer.destroyNode) {\n            this._renderer.destroyNode(this._badgeElement);\n            this._inlineBadgeDescription?.remove();\n        }\n        this._ariaDescriber.removeDescription(this._elementRef.nativeElement, this.description);\n    }\n    /** Gets whether the badge's host element is interactive. */\n    _isHostInteractive() {\n        // Ignore visibility since it requires an expensive style caluclation.\n        return this._interactivityChecker.isFocusable(this._elementRef.nativeElement, {\n            ignoreVisibility: true,\n        });\n    }\n    /** Creates the badge element */\n    _createBadgeElement() {\n        const badgeElement = this._renderer.createElement('span');\n        const activeClass = 'mat-badge-active';\n        badgeElement.setAttribute('id', `mat-badge-content-${this._id}`);\n        // The badge is aria-hidden because we don't want it to appear in the page's navigation\n        // flow. Instead, we use the badge to describe the decorated element with aria-describedby.\n        badgeElement.setAttribute('aria-hidden', 'true');\n        badgeElement.classList.add(BADGE_CONTENT_CLASS);\n        if (this._animationMode === 'NoopAnimations') {\n            badgeElement.classList.add('_mat-animation-noopable');\n        }\n        this._elementRef.nativeElement.appendChild(badgeElement);\n        // animate in after insertion\n        if (typeof requestAnimationFrame === 'function' && this._animationMode !== 'NoopAnimations') {\n            this._ngZone.runOutsideAngular(() => {\n                requestAnimationFrame(() => {\n                    badgeElement.classList.add(activeClass);\n                });\n            });\n        }\n        else {\n            badgeElement.classList.add(activeClass);\n        }\n        return badgeElement;\n    }\n    /** Update the text content of the badge element in the DOM, creating the element if necessary. */\n    _updateRenderedContent(newContent) {\n        const newContentNormalized = `${newContent ?? ''}`.trim();\n        // Don't create the badge element if the directive isn't initialized because we want to\n        // append the badge element to the *end* of the host element's content for backwards\n        // compatibility.\n        if (this._isInitialized && newContentNormalized && !this._badgeElement) {\n            this._badgeElement = this._createBadgeElement();\n        }\n        if (this._badgeElement) {\n            this._badgeElement.textContent = newContentNormalized;\n        }\n        this._content = newContentNormalized;\n    }\n    /** Updates the host element's aria description via AriaDescriber. */\n    _updateDescription(newDescription) {\n        // Always start by removing the aria-describedby; we will add a new one if necessary.\n        this._ariaDescriber.removeDescription(this._elementRef.nativeElement, this.description);\n        // NOTE: We only check whether the host is interactive here, which happens during\n        // when then badge content changes. It is possible that the host changes\n        // interactivity status separate from one of these. However, watching the interactivity\n        // status of the host would require a `MutationObserver`, which is likely more code + overhead\n        // than it's worth; from usages inside Google, we see that the vats majority of badges either\n        // never change interactivity, or also set `matBadgeHidden` based on the same condition.\n        if (!newDescription || this._isHostInteractive()) {\n            this._removeInlineDescription();\n        }\n        this._description = newDescription;\n        // We don't add `aria-describedby` for non-interactive hosts elements because we\n        // instead insert the description inline.\n        if (this._isHostInteractive()) {\n            this._ariaDescriber.describe(this._elementRef.nativeElement, newDescription);\n        }\n        else {\n            this._updateInlineDescription();\n        }\n    }\n    _updateInlineDescription() {\n        // Create the inline description element if it doesn't exist\n        if (!this._inlineBadgeDescription) {\n            this._inlineBadgeDescription = this._document.createElement('span');\n            this._inlineBadgeDescription.classList.add('cdk-visually-hidden');\n        }\n        this._inlineBadgeDescription.textContent = this.description;\n        this._badgeElement?.appendChild(this._inlineBadgeDescription);\n    }\n    _removeInlineDescription() {\n        this._inlineBadgeDescription?.remove();\n        this._inlineBadgeDescription = undefined;\n    }\n    /** Adds css theme class given the color to the component host */\n    _setColor(colorPalette) {\n        const classList = this._elementRef.nativeElement.classList;\n        classList.remove(`mat-badge-${this._color}`);\n        if (colorPalette) {\n            classList.add(`mat-badge-${colorPalette}`);\n        }\n    }\n    /** Clears any existing badges that might be left over from server-side rendering. */\n    _clearExistingBadges() {\n        // Only check direct children of this host element in order to avoid deleting\n        // any badges that might exist in descendant elements.\n        const badges = this._elementRef.nativeElement.querySelectorAll(`:scope > .${BADGE_CONTENT_CLASS}`);\n        for (const badgeElement of Array.from(badges)) {\n            if (badgeElement !== this._badgeElement) {\n                badgeElement.remove();\n            }\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatBadge, deps: [{ token: i0.NgZone }, { token: i0.ElementRef }, { token: i1.AriaDescriber }, { token: i0.Renderer2 }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatBadge, selector: \"[matBadge]\", inputs: { disabled: [\"matBadgeDisabled\", \"disabled\"], color: [\"matBadgeColor\", \"color\"], overlap: [\"matBadgeOverlap\", \"overlap\"], position: [\"matBadgePosition\", \"position\"], content: [\"matBadge\", \"content\"], description: [\"matBadgeDescription\", \"description\"], size: [\"matBadgeSize\", \"size\"], hidden: [\"matBadgeHidden\", \"hidden\"] }, host: { properties: { \"class.mat-badge-overlap\": \"overlap\", \"class.mat-badge-above\": \"isAbove()\", \"class.mat-badge-below\": \"!isAbove()\", \"class.mat-badge-before\": \"!isAfter()\", \"class.mat-badge-after\": \"isAfter()\", \"class.mat-badge-small\": \"size === \\\"small\\\"\", \"class.mat-badge-medium\": \"size === \\\"medium\\\"\", \"class.mat-badge-large\": \"size === \\\"large\\\"\", \"class.mat-badge-hidden\": \"hidden || !content\", \"class.mat-badge-disabled\": \"disabled\" }, classAttribute: \"mat-badge\" }, usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatBadge, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matBadge]',\n                    inputs: ['disabled: matBadgeDisabled'],\n                    host: {\n                        'class': 'mat-badge',\n                        '[class.mat-badge-overlap]': 'overlap',\n                        '[class.mat-badge-above]': 'isAbove()',\n                        '[class.mat-badge-below]': '!isAbove()',\n                        '[class.mat-badge-before]': '!isAfter()',\n                        '[class.mat-badge-after]': 'isAfter()',\n                        '[class.mat-badge-small]': 'size === \"small\"',\n                        '[class.mat-badge-medium]': 'size === \"medium\"',\n                        '[class.mat-badge-large]': 'size === \"large\"',\n                        '[class.mat-badge-hidden]': 'hidden || !content',\n                        '[class.mat-badge-disabled]': 'disabled',\n                    },\n                }]\n        }], ctorParameters: function () { return [{ type: i0.NgZone }, { type: i0.ElementRef }, { type: i1.AriaDescriber }, { type: i0.Renderer2 }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }]; }, propDecorators: { color: [{\n                type: Input,\n                args: ['matBadgeColor']\n            }], overlap: [{\n                type: Input,\n                args: ['matBadgeOverlap']\n            }], position: [{\n                type: Input,\n                args: ['matBadgePosition']\n            }], content: [{\n                type: Input,\n                args: ['matBadge']\n            }], description: [{\n                type: Input,\n                args: ['matBadgeDescription']\n            }], size: [{\n                type: Input,\n                args: ['matBadgeSize']\n            }], hidden: [{\n                type: Input,\n                args: ['matBadgeHidden']\n            }] } });\n\nclass MatBadgeModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatBadgeModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.1.1\", ngImport: i0, type: MatBadgeModule, declarations: [MatBadge], imports: [A11yModule, MatCommonModule], exports: [MatBadge, MatCommonModule] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatBadgeModule, imports: [A11yModule, MatCommonModule, MatCommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatBadgeModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [A11yModule, MatCommonModule],\n                    exports: [MatBadge, MatCommonModule],\n                    declarations: [MatBadge],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MatBadge, MatBadgeModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AACpF,SAASC,aAAa,EAAEC,eAAe,QAAQ,wBAAwB;AACvE,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,SAASC,oBAAoB,EAAEC,UAAU,QAAQ,mBAAmB;AACpE,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,qBAAqB,QAAQ,sCAAsC;AAE5E,IAAIC,MAAM,GAAG,CAAC;AACd;AACA;AACA,MAAMC,aAAa,GAAGT,aAAa,CAAC,MAAM,EACzC,CAAC;AACF,MAAMU,mBAAmB,GAAG,mBAAmB;AAC/C;AACA,MAAMC,QAAQ,SAASF,aAAa,CAAC;EACjC;EACA,IAAIG,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACC,MAAM;EACtB;EACA,IAAID,KAAKA,CAACE,KAAK,EAAE;IACb,IAAI,CAACC,SAAS,CAACD,KAAK,CAAC;IACrB,IAAI,CAACD,MAAM,GAAGC,KAAK;EACvB;EACA;EACA,IAAIE,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,QAAQ;EACxB;EACA,IAAID,OAAOA,CAACE,GAAG,EAAE;IACb,IAAI,CAACD,QAAQ,GAAGZ,qBAAqB,CAACa,GAAG,CAAC;EAC9C;EACA;EACA,IAAIC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,QAAQ;EACxB;EACA,IAAID,OAAOA,CAACE,UAAU,EAAE;IACpB,IAAI,CAACC,sBAAsB,CAACD,UAAU,CAAC;EAC3C;EACA;EACA,IAAIE,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,YAAY;EAC5B;EACA,IAAID,WAAWA,CAACE,cAAc,EAAE;IAC5B,IAAI,CAACC,kBAAkB,CAACD,cAAc,CAAC;EAC3C;EACA;EACA,IAAIE,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACC,OAAO;EACvB;EACA,IAAID,MAAMA,CAACT,GAAG,EAAE;IACZ,IAAI,CAACU,OAAO,GAAGvB,qBAAqB,CAACa,GAAG,CAAC;EAC7C;EACAW,WAAWA,CAACC,OAAO,EAAEC,WAAW,EAAEC,cAAc,EAAEC,SAAS,EAAEC,cAAc,EAAE;IACzE,KAAK,CAAC,CAAC;IACP,IAAI,CAACJ,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACrB,MAAM,GAAG,SAAS;IACvB,IAAI,CAACI,QAAQ,GAAG,IAAI;IACpB;AACR;AACA;AACA;IACQ,IAAI,CAACkB,QAAQ,GAAG,aAAa;IAC7B;IACA,IAAI,CAACC,IAAI,GAAG,QAAQ;IACpB;IACA,IAAI,CAACC,GAAG,GAAG7B,MAAM,EAAE;IACnB;IACA,IAAI,CAAC8B,cAAc,GAAG,KAAK;IAC3B;IACA,IAAI,CAACC,qBAAqB,GAAG7C,MAAM,CAACS,oBAAoB,CAAC;IACzD,IAAI,CAACqC,SAAS,GAAG9C,MAAM,CAACY,QAAQ,CAAC;IACjC,IAAI,OAAOmC,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C,MAAMC,aAAa,GAAGX,WAAW,CAACW,aAAa;MAC/C,IAAIA,aAAa,CAACC,QAAQ,KAAKD,aAAa,CAACE,YAAY,EAAE;QACvD,MAAMC,KAAK,CAAC,+CAA+C,CAAC;MAChE;MACA,MAAMC,cAAc,GAAG,UAAU;MACjC;MACA;MACA;MACA,IAAIJ,aAAa,CAACK,OAAO,CAACC,WAAW,CAAC,CAAC,KAAKF,cAAc,IACtDJ,aAAa,CAACO,YAAY,CAAC,aAAa,CAAC,KAAK,MAAM,EAAE;QACtDC,OAAO,CAACC,IAAI,CAAE,wDAAuD,GAChE,gGAA+F,GAC/F,KAAIT,aAAa,CAACU,SAAU,EAAC,CAAC;MACvC;IACJ;EACJ;EACA;EACAC,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAAClB,QAAQ,CAACmB,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;EAChD;EACA;EACAC,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACpB,QAAQ,CAACmB,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;EACjD;EACA;AACJ;AACA;AACA;EACIE,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,aAAa;EAC7B;EACAC,QAAQA,CAAA,EAAG;IACP;IACA;IACA;IACA,IAAI,CAACC,oBAAoB,CAAC,CAAC;IAC3B,IAAI,IAAI,CAACxC,OAAO,IAAI,CAAC,IAAI,CAACsC,aAAa,EAAE;MACrC,IAAI,CAACA,aAAa,GAAG,IAAI,CAACG,mBAAmB,CAAC,CAAC;MAC/C,IAAI,CAACtC,sBAAsB,CAAC,IAAI,CAACH,OAAO,CAAC;IAC7C;IACA,IAAI,CAACmB,cAAc,GAAG,IAAI;EAC9B;EACAuB,WAAWA,CAAA,EAAG;IACV;IACA;IACA,IAAI,IAAI,CAAC5B,SAAS,CAAC6B,WAAW,EAAE;MAC5B,IAAI,CAAC7B,SAAS,CAAC6B,WAAW,CAAC,IAAI,CAACL,aAAa,CAAC;MAC9C,IAAI,CAACM,uBAAuB,EAAEC,MAAM,CAAC,CAAC;IAC1C;IACA,IAAI,CAAChC,cAAc,CAACiC,iBAAiB,CAAC,IAAI,CAAClC,WAAW,CAACW,aAAa,EAAE,IAAI,CAACnB,WAAW,CAAC;EAC3F;EACA;EACA2C,kBAAkBA,CAAA,EAAG;IACjB;IACA,OAAO,IAAI,CAAC3B,qBAAqB,CAAC4B,WAAW,CAAC,IAAI,CAACpC,WAAW,CAACW,aAAa,EAAE;MAC1E0B,gBAAgB,EAAE;IACtB,CAAC,CAAC;EACN;EACA;EACAR,mBAAmBA,CAAA,EAAG;IAClB,MAAMS,YAAY,GAAG,IAAI,CAACpC,SAAS,CAACqC,aAAa,CAAC,MAAM,CAAC;IACzD,MAAMC,WAAW,GAAG,kBAAkB;IACtCF,YAAY,CAACG,YAAY,CAAC,IAAI,EAAG,qBAAoB,IAAI,CAACnC,GAAI,EAAC,CAAC;IAChE;IACA;IACAgC,YAAY,CAACG,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;IAChDH,YAAY,CAACI,SAAS,CAACC,GAAG,CAAChE,mBAAmB,CAAC;IAC/C,IAAI,IAAI,CAACwB,cAAc,KAAK,gBAAgB,EAAE;MAC1CmC,YAAY,CAACI,SAAS,CAACC,GAAG,CAAC,yBAAyB,CAAC;IACzD;IACA,IAAI,CAAC3C,WAAW,CAACW,aAAa,CAACiC,WAAW,CAACN,YAAY,CAAC;IACxD;IACA,IAAI,OAAOO,qBAAqB,KAAK,UAAU,IAAI,IAAI,CAAC1C,cAAc,KAAK,gBAAgB,EAAE;MACzF,IAAI,CAACJ,OAAO,CAAC+C,iBAAiB,CAAC,MAAM;QACjCD,qBAAqB,CAAC,MAAM;UACxBP,YAAY,CAACI,SAAS,CAACC,GAAG,CAACH,WAAW,CAAC;QAC3C,CAAC,CAAC;MACN,CAAC,CAAC;IACN,CAAC,MACI;MACDF,YAAY,CAACI,SAAS,CAACC,GAAG,CAACH,WAAW,CAAC;IAC3C;IACA,OAAOF,YAAY;EACvB;EACA;EACA/C,sBAAsBA,CAACD,UAAU,EAAE;IAC/B,MAAMyD,oBAAoB,GAAI,GAAEzD,UAAU,IAAI,EAAG,EAAC,CAAC0D,IAAI,CAAC,CAAC;IACzD;IACA;IACA;IACA,IAAI,IAAI,CAACzC,cAAc,IAAIwC,oBAAoB,IAAI,CAAC,IAAI,CAACrB,aAAa,EAAE;MACpE,IAAI,CAACA,aAAa,GAAG,IAAI,CAACG,mBAAmB,CAAC,CAAC;IACnD;IACA,IAAI,IAAI,CAACH,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAACuB,WAAW,GAAGF,oBAAoB;IACzD;IACA,IAAI,CAAC1D,QAAQ,GAAG0D,oBAAoB;EACxC;EACA;EACApD,kBAAkBA,CAACD,cAAc,EAAE;IAC/B;IACA,IAAI,CAACO,cAAc,CAACiC,iBAAiB,CAAC,IAAI,CAAClC,WAAW,CAACW,aAAa,EAAE,IAAI,CAACnB,WAAW,CAAC;IACvF;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACE,cAAc,IAAI,IAAI,CAACyC,kBAAkB,CAAC,CAAC,EAAE;MAC9C,IAAI,CAACe,wBAAwB,CAAC,CAAC;IACnC;IACA,IAAI,CAACzD,YAAY,GAAGC,cAAc;IAClC;IACA;IACA,IAAI,IAAI,CAACyC,kBAAkB,CAAC,CAAC,EAAE;MAC3B,IAAI,CAAClC,cAAc,CAACkD,QAAQ,CAAC,IAAI,CAACnD,WAAW,CAACW,aAAa,EAAEjB,cAAc,CAAC;IAChF,CAAC,MACI;MACD,IAAI,CAAC0D,wBAAwB,CAAC,CAAC;IACnC;EACJ;EACAA,wBAAwBA,CAAA,EAAG;IACvB;IACA,IAAI,CAAC,IAAI,CAACpB,uBAAuB,EAAE;MAC/B,IAAI,CAACA,uBAAuB,GAAG,IAAI,CAACvB,SAAS,CAAC8B,aAAa,CAAC,MAAM,CAAC;MACnE,IAAI,CAACP,uBAAuB,CAACU,SAAS,CAACC,GAAG,CAAC,qBAAqB,CAAC;IACrE;IACA,IAAI,CAACX,uBAAuB,CAACiB,WAAW,GAAG,IAAI,CAACzD,WAAW;IAC3D,IAAI,CAACkC,aAAa,EAAEkB,WAAW,CAAC,IAAI,CAACZ,uBAAuB,CAAC;EACjE;EACAkB,wBAAwBA,CAAA,EAAG;IACvB,IAAI,CAAClB,uBAAuB,EAAEC,MAAM,CAAC,CAAC;IACtC,IAAI,CAACD,uBAAuB,GAAGqB,SAAS;EAC5C;EACA;EACArE,SAASA,CAACsE,YAAY,EAAE;IACpB,MAAMZ,SAAS,GAAG,IAAI,CAAC1C,WAAW,CAACW,aAAa,CAAC+B,SAAS;IAC1DA,SAAS,CAACT,MAAM,CAAE,aAAY,IAAI,CAACnD,MAAO,EAAC,CAAC;IAC5C,IAAIwE,YAAY,EAAE;MACdZ,SAAS,CAACC,GAAG,CAAE,aAAYW,YAAa,EAAC,CAAC;IAC9C;EACJ;EACA;EACA1B,oBAAoBA,CAAA,EAAG;IACnB;IACA;IACA,MAAM2B,MAAM,GAAG,IAAI,CAACvD,WAAW,CAACW,aAAa,CAAC6C,gBAAgB,CAAE,aAAY7E,mBAAoB,EAAC,CAAC;IAClG,KAAK,MAAM2D,YAAY,IAAImB,KAAK,CAACC,IAAI,CAACH,MAAM,CAAC,EAAE;MAC3C,IAAIjB,YAAY,KAAK,IAAI,CAACZ,aAAa,EAAE;QACrCY,YAAY,CAACL,MAAM,CAAC,CAAC;MACzB;IACJ;EACJ;EACA;IAAS,IAAI,CAAC0B,IAAI,YAAAC,iBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFjF,QAAQ,EAAlBlB,EAAE,CAAAoG,iBAAA,CAAkCpG,EAAE,CAACqG,MAAM,GAA7CrG,EAAE,CAAAoG,iBAAA,CAAwDpG,EAAE,CAACsG,UAAU,GAAvEtG,EAAE,CAAAoG,iBAAA,CAAkF3F,EAAE,CAAC8F,aAAa,GAApGvG,EAAE,CAAAoG,iBAAA,CAA+GpG,EAAE,CAACwG,SAAS,GAA7HxG,EAAE,CAAAoG,iBAAA,CAAwItF,qBAAqB;IAAA,CAA4D;EAAE;EAC7T;IAAS,IAAI,CAAC2F,IAAI,kBAD8EzG,EAAE,CAAA0G,iBAAA;MAAAC,IAAA,EACJzF,QAAQ;MAAA0F,SAAA;MAAAC,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADNjH,EAAE,CAAAmH,WAAA,sBAAAD,GAAA,CAAA3F,OAAA,qBAAA2F,GAAA,CAAAtD,OAAA,wBAAAsD,GAAA,CAAAtD,OAAA,yBAAAsD,GAAA,CAAApD,OAAA,uBAAAoD,GAAA,CAAApD,OAAA,uBAAAoD,GAAA,CAAAvE,IAAA,kCAAAuE,GAAA,CAAAvE,IAAA,kCAAAuE,GAAA,CAAAvE,IAAA,kCAAAuE,GAAA,CAAAhF,MAAA,KAAAgF,GAAA,CAAAxF,OAAA,wBAAAwF,GAAA,CAAAE,QAAA;QAAA;MAAA;MAAAC,MAAA;QAAAD,QAAA;QAAAjG,KAAA;QAAAI,OAAA;QAAAmB,QAAA;QAAAhB,OAAA;QAAAI,WAAA;QAAAa,IAAA;QAAAT,MAAA;MAAA;MAAAoF,QAAA,GAAFtH,EAAE,CAAAuH,0BAAA;IAAA,EACg3B;EAAE;AACx9B;AACA;EAAA,QAAAvE,SAAA,oBAAAA,SAAA,KAHoGhD,EAAE,CAAAwH,iBAAA,CAGXtG,QAAQ,EAAc,CAAC;IACtGyF,IAAI,EAAEzG,SAAS;IACfuH,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,YAAY;MACtBL,MAAM,EAAE,CAAC,4BAA4B,CAAC;MACtCM,IAAI,EAAE;QACF,OAAO,EAAE,WAAW;QACpB,2BAA2B,EAAE,SAAS;QACtC,yBAAyB,EAAE,WAAW;QACtC,yBAAyB,EAAE,YAAY;QACvC,0BAA0B,EAAE,YAAY;QACxC,yBAAyB,EAAE,WAAW;QACtC,yBAAyB,EAAE,kBAAkB;QAC7C,0BAA0B,EAAE,mBAAmB;QAC/C,yBAAyB,EAAE,kBAAkB;QAC7C,0BAA0B,EAAE,oBAAoB;QAChD,4BAA4B,EAAE;MAClC;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEhB,IAAI,EAAE3G,EAAE,CAACqG;IAAO,CAAC,EAAE;MAAEM,IAAI,EAAE3G,EAAE,CAACsG;IAAW,CAAC,EAAE;MAAEK,IAAI,EAAElG,EAAE,CAAC8F;IAAc,CAAC,EAAE;MAAEI,IAAI,EAAE3G,EAAE,CAACwG;IAAU,CAAC,EAAE;MAAEG,IAAI,EAAEhB,SAAS;MAAEiC,UAAU,EAAE,CAAC;QAChKjB,IAAI,EAAExG;MACV,CAAC,EAAE;QACCwG,IAAI,EAAEvG,MAAM;QACZqH,IAAI,EAAE,CAAC3G,qBAAqB;MAChC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEK,KAAK,EAAE,CAAC;MACpCwF,IAAI,EAAEtG,KAAK;MACXoH,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC,CAAC;IAAElG,OAAO,EAAE,CAAC;MACVoF,IAAI,EAAEtG,KAAK;MACXoH,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAE/E,QAAQ,EAAE,CAAC;MACXiE,IAAI,EAAEtG,KAAK;MACXoH,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAE/F,OAAO,EAAE,CAAC;MACViF,IAAI,EAAEtG,KAAK;MACXoH,IAAI,EAAE,CAAC,UAAU;IACrB,CAAC,CAAC;IAAE3F,WAAW,EAAE,CAAC;MACd6E,IAAI,EAAEtG,KAAK;MACXoH,IAAI,EAAE,CAAC,qBAAqB;IAChC,CAAC,CAAC;IAAE9E,IAAI,EAAE,CAAC;MACPgE,IAAI,EAAEtG,KAAK;MACXoH,IAAI,EAAE,CAAC,cAAc;IACzB,CAAC,CAAC;IAAEvF,MAAM,EAAE,CAAC;MACTyE,IAAI,EAAEtG,KAAK;MACXoH,IAAI,EAAE,CAAC,gBAAgB;IAC3B,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMI,cAAc,CAAC;EACjB;IAAS,IAAI,CAAC5B,IAAI,YAAA6B,uBAAA3B,CAAA;MAAA,YAAAA,CAAA,IAAwF0B,cAAc;IAAA,CAAkD;EAAE;EAC5K;IAAS,IAAI,CAACE,IAAI,kBApD8E/H,EAAE,CAAAgI,gBAAA;MAAArB,IAAA,EAoDSkB;IAAc,EAA2G;EAAE;EACtO;IAAS,IAAI,CAACI,IAAI,kBArD8EjI,EAAE,CAAAkI,gBAAA;MAAAC,OAAA,GAqDmCxH,UAAU,EAAEH,eAAe,EAAEA,eAAe;IAAA,EAAI;EAAE;AAC3L;AACA;EAAA,QAAAwC,SAAA,oBAAAA,SAAA,KAvDoGhD,EAAE,CAAAwH,iBAAA,CAuDXK,cAAc,EAAc,CAAC;IAC5GlB,IAAI,EAAErG,QAAQ;IACdmH,IAAI,EAAE,CAAC;MACCU,OAAO,EAAE,CAACxH,UAAU,EAAEH,eAAe,CAAC;MACtC4H,OAAO,EAAE,CAAClH,QAAQ,EAAEV,eAAe,CAAC;MACpC6H,YAAY,EAAE,CAACnH,QAAQ;IAC3B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASA,QAAQ,EAAE2G,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}