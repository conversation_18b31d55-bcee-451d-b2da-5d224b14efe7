{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class AppComponent {\n  constructor() {\n    this.title = 'apqr400bl';\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      decls: 22,\n      vars: 2,\n      consts: [[1, \"content\", 2, \"text-align\", \"center\"], [2, \"display\", \"block\"], [\"width\", \"300\", \"alt\", \"Angular Logo\", \"src\", \"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNTAgMjUwIj4KICAgIDxwYXRoIGZpbGw9IiNERDAwMzEiIGQ9Ik0xMjUgMzBMMzEuOSA2My4ybDE0LjIgMTIzLjFMMTI1IDIzMGw3OC45LTQzLjcgMTQuMi0xMjMuMXoiIC8+CiAgICA8cGF0aCBmaWxsPSIjQzMwMDJGIiBkPSJNMTI1IDMwdjIyLjItLjFWMjMwbDc4LjktNDMuNyAxNC4yLTEyMy4xTDEyNSAzMHoiIC8+CiAgICA8cGF0aCAgZmlsbD0iI0ZGRkZGRiIgZD0iTTEyNSA1Mi4xTDY2LjggMTgyLjZoMjEuN2wxMS43LTI5LjJoNDkuNGwxMS43IDI5LjJIMTgzTDEyNSA1Mi4xem0xNyA4My4zaC0zNGwxNy00MC45IDE3IDQwLjl6IiAvPgogIDwvc3ZnPg==\"], [\"target\", \"_blank\", \"rel\", \"noopener\", \"href\", \"https://angular.io/tutorial\"], [\"target\", \"_blank\", \"rel\", \"noopener\", \"href\", \"https://angular.io/cli\"], [\"target\", \"_blank\", \"rel\", \"noopener\", \"href\", \"https://blog.angular.io/\"]],\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h1\");\n          i0.ɵɵtext(2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"span\", 1);\n          i0.ɵɵtext(4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(5, \"img\", 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"h2\");\n          i0.ɵɵtext(7, \"Here are some links to help you start: \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"ul\")(9, \"li\")(10, \"h2\")(11, \"a\", 3);\n          i0.ɵɵtext(12, \"Tour of Heroes\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"li\")(14, \"h2\")(15, \"a\", 4);\n          i0.ɵɵtext(16, \"CLI Documentation\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"li\")(18, \"h2\")(19, \"a\", 5);\n          i0.ɵɵtext(20, \"Angular blog\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelement(21, \"router-outlet\");\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" Welcome to \", ctx.title, \"! \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\"\", ctx.title, \" app is running!\");\n        }\n      },\n      dependencies: [i1.RouterOutlet],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["AppComponent", "constructor", "title", "selectors", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate1"], "sources": ["C:\\cal_wf\\Augment\\3-img\\apqr400bl\\src\\app\\app.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-root',\n  template: `\n    <!--The content below is only a placeholder and can be replaced.-->\n    <div style=\"text-align:center\" class=\"content\">\n      <h1>\n        Welcome to {{title}}!\n      </h1>\n      <span style=\"display: block\">{{ title }} app is running!</span>\n      <img width=\"300\" alt=\"Angular Logo\" src=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNTAgMjUwIj4KICAgIDxwYXRoIGZpbGw9IiNERDAwMzEiIGQ9Ik0xMjUgMzBMMzEuOSA2My4ybDE0LjIgMTIzLjFMMTI1IDIzMGw3OC45LTQzLjcgMTQuMi0xMjMuMXoiIC8+CiAgICA8cGF0aCBmaWxsPSIjQzMwMDJGIiBkPSJNMTI1IDMwdjIyLjItLjFWMjMwbDc4LjktNDMuNyAxNC4yLTEyMy4xTDEyNSAzMHoiIC8+CiAgICA8cGF0aCAgZmlsbD0iI0ZGRkZGRiIgZD0iTTEyNSA1Mi4xTDY2LjggMTgyLjZoMjEuN2wxMS43LTI5LjJoNDkuNGwxMS43IDI5LjJIMTgzTDEyNSA1Mi4xem0xNyA4My4zaC0zNGwxNy00MC45IDE3IDQwLjl6IiAvPgogIDwvc3ZnPg==\">\n    </div>\n    <h2>Here are some links to help you start: </h2>\n    <ul>\n      <li>\n        <h2><a target=\"_blank\" rel=\"noopener\" href=\"https://angular.io/tutorial\">Tour of Heroes</a></h2>\n      </li>\n      <li>\n        <h2><a target=\"_blank\" rel=\"noopener\" href=\"https://angular.io/cli\">CLI Documentation</a></h2>\n      </li>\n      <li>\n        <h2><a target=\"_blank\" rel=\"noopener\" href=\"https://blog.angular.io/\">Angular blog</a></h2>\n      </li>\n    </ul>\n    <router-outlet></router-outlet>\n  `,\n  styles: []\n})\nexport class AppComponent {\n  title = 'apqr400bl';\n}\n"], "mappings": ";;AA6BA,OAAM,MAAOA,YAAY;EA3BzBC,YAAA;IA4BE,KAAAC,KAAK,GAAG,WAAW;;;;uBADRF,YAAY;IAAA;EAAA;;;YAAZA,YAAY;MAAAG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAvBrBE,EAAA,CAAAC,cAAA,aAA+C;UAE3CD,EAAA,CAAAE,MAAA,GACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,cAA6B;UAAAD,EAAA,CAAAE,MAAA,GAA2B;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC/DH,EAAA,CAAAI,SAAA,aAAqjB;UACvjBJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,SAAI;UAAAD,EAAA,CAAAE,MAAA,8CAAuC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAChDH,EAAA,CAAAC,cAAA,SAAI;UAEyED,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAE7FH,EAAA,CAAAC,cAAA,UAAI;UACkED,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAE3FH,EAAA,CAAAC,cAAA,UAAI;UACoED,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAG1FH,EAAA,CAAAI,SAAA,qBAA+B;;;UAjB3BJ,EAAA,CAAAK,SAAA,GACF;UADEL,EAAA,CAAAM,kBAAA,iBAAAP,GAAA,CAAAR,KAAA,OACF;UAC6BS,EAAA,CAAAK,SAAA,GAA2B;UAA3BL,EAAA,CAAAM,kBAAA,KAAAP,GAAA,CAAAR,KAAA,qBAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}