{"ast": null, "code": "import { iterator as Symbol_iterator } from '../symbol/iterator';\nimport { isFunction } from './isFunction';\nexport function isIterable(input) {\n  return isFunction(input === null || input === void 0 ? void 0 : input[Symbol_iterator]);\n}", "map": {"version": 3, "names": ["iterator", "Symbol_iterator", "isFunction", "isIterable", "input"], "sources": ["C:/cal_wf/Augment/3-img/apqr400bl/node_modules/rxjs/dist/esm/internal/util/isIterable.js"], "sourcesContent": ["import { iterator as Symbol_iterator } from '../symbol/iterator';\nimport { isFunction } from './isFunction';\nexport function isIterable(input) {\n    return isFunction(input === null || input === void 0 ? void 0 : input[Symbol_iterator]);\n}\n"], "mappings": "AAAA,SAASA,QAAQ,IAAIC,eAAe,QAAQ,oBAAoB;AAChE,SAASC,UAAU,QAAQ,cAAc;AACzC,OAAO,SAASC,UAAUA,CAACC,KAAK,EAAE;EAC9B,OAAOF,UAAU,CAACE,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACH,eAAe,CAAC,CAAC;AAC3F", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}