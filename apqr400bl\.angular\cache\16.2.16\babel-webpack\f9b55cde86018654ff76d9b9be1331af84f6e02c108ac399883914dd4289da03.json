{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class ReportsComponent {\n  constructor() {}\n  ngOnInit() {}\n  static {\n    this.ɵfac = function ReportsComponent_Factory(t) {\n      return new (t || ReportsComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ReportsComponent,\n      selectors: [[\"app-reports\"]],\n      decls: 5,\n      vars: 0,\n      consts: [[1, \"component-container\"]],\n      template: function ReportsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h2\");\n          i0.ɵɵtext(2, \"Reports Component\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"p\");\n          i0.ɵɵtext(4, \"This is the Reports component content.\");\n          i0.ɵɵelementEnd()();\n        }\n      },\n      styles: [\".component-container[_ngcontent-%COMP%] {\\n  padding: 2rem;\\n  background-color: white;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\\n}\\n\\nh2[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n  color: #333;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbGF5b3V0L2NvbXBvbmVudHMvcmVwb3J0cy9yZXBvcnRzLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsYUFBQTtFQUNBLHVCQUFBO0VBQ0Esa0JBQUE7RUFDQSx5Q0FBQTtBQUNGOztBQUVBO0VBQ0UsYUFBQTtFQUNBLFdBQUE7QUFDRiIsInNvdXJjZXNDb250ZW50IjpbIi5jb21wb25lbnQtY29udGFpbmVyIHtcbiAgcGFkZGluZzogMnJlbTtcbiAgYmFja2dyb3VuZC1jb2xvcjogd2hpdGU7XG4gIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgYm94LXNoYWRvdzogMCAycHggNHB4IHJnYmEoMCwgMCwgMCwgMC4wNSk7XG59XG5cbmgyIHtcbiAgbWFyZ2luLXRvcDogMDtcbiAgY29sb3I6ICMzMzM7XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["ReportsComponent", "constructor", "ngOnInit", "selectors", "decls", "vars", "consts", "template", "ReportsComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\cal_wf\\Augment\\4-img\\apqr400bl\\src\\app\\layout\\components\\reports\\reports.component.ts", "C:\\cal_wf\\Augment\\4-img\\apqr400bl\\src\\app\\layout\\components\\reports\\reports.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\n\n@Component({\n  selector: 'app-reports',\n  templateUrl: './reports.component.html',\n  styleUrls: ['./reports.component.scss']\n})\nexport class ReportsComponent implements OnInit {\n\n  constructor() { }\n\n  ngOnInit(): void {\n  }\n\n}\n", "<div class=\"component-container\">\n  <h2>Reports Component</h2>\n  <p>This is the Reports component content.</p>\n</div>\n"], "mappings": ";AAOA,OAAM,MAAOA,gBAAgB;EAE3BC,YAAA,GAAgB;EAEhBC,QAAQA,CAAA,GACR;;;uBALWF,gBAAgB;IAAA;EAAA;;;YAAhBA,gBAAgB;MAAAG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP7BE,EAAA,CAAAC,cAAA,aAAiC;UAC3BD,EAAA,CAAAE,MAAA,wBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC1BH,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAE,MAAA,6CAAsC;UAAAF,EAAA,CAAAG,YAAA,EAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}