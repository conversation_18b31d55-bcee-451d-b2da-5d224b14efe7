{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport class SelectionService {\n  constructor() {\n    this.selectedModuleSource = new BehaviorSubject('Caliber aPQR');\n    this.selectedPlantSource = new BehaviorSubject('CaliberPlant-1');\n    this.selectedModule$ = this.selectedModuleSource.asObservable();\n    this.selectedPlant$ = this.selectedPlantSource.asObservable();\n  }\n  setSelectedModule(module) {\n    this.selectedModuleSource.next(module);\n  }\n  setSelectedPlant(plant) {\n    this.selectedPlantSource.next(plant);\n  }\n  static {\n    this.ɵfac = function SelectionService_Factory(t) {\n      return new (t || SelectionService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: SelectionService,\n      factory: SelectionService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "SelectionService", "constructor", "selectedModuleSource", "selectedPlantSource", "selectedModule$", "asObservable", "selectedPlant$", "setSelectedModule", "module", "next", "setSelectedPlant", "plant", "factory", "ɵfac", "providedIn"], "sources": ["C:\\cal_wf\\Augment\\4-img\\apqr400bl\\src\\app\\shared\\selection.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { BehaviorSubject } from 'rxjs';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class SelectionService {\n  private selectedModuleSource = new BehaviorSubject<string>('Caliber aPQR');\n  private selectedPlantSource = new BehaviorSubject<string>('CaliberPlant-1');\n\n  selectedModule$ = this.selectedModuleSource.asObservable();\n  selectedPlant$ = this.selectedPlantSource.asObservable();\n\n  constructor() { }\n\n  setSelectedModule(module: string): void {\n    this.selectedModuleSource.next(module);\n  }\n\n  setSelectedPlant(plant: string): void {\n    this.selectedPlantSource.next(plant);\n  }\n}\n"], "mappings": "AACA,SAASA,eAAe,QAAQ,MAAM;;AAKtC,OAAM,MAAOC,gBAAgB;EAO3BC,YAAA;IANQ,KAAAC,oBAAoB,GAAG,IAAIH,eAAe,CAAS,cAAc,CAAC;IAClE,KAAAI,mBAAmB,GAAG,IAAIJ,eAAe,CAAS,gBAAgB,CAAC;IAE3E,KAAAK,eAAe,GAAG,IAAI,CAACF,oBAAoB,CAACG,YAAY,EAAE;IAC1D,KAAAC,cAAc,GAAG,IAAI,CAACH,mBAAmB,CAACE,YAAY,EAAE;EAExC;EAEhBE,iBAAiBA,CAACC,MAAc;IAC9B,IAAI,CAACN,oBAAoB,CAACO,IAAI,CAACD,MAAM,CAAC;EACxC;EAEAE,gBAAgBA,CAACC,KAAa;IAC5B,IAAI,CAACR,mBAAmB,CAACM,IAAI,CAACE,KAAK,CAAC;EACtC;;;uBAfWX,gBAAgB;IAAA;EAAA;;;aAAhBA,gBAAgB;MAAAY,OAAA,EAAhBZ,gBAAgB,CAAAa,IAAA;MAAAC,UAAA,EAFf;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}