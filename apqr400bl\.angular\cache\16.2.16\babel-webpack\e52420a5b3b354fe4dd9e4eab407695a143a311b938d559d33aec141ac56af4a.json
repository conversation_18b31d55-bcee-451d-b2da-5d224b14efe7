{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"./components/sidebar/sidebar.component\";\nimport * as i3 from \"./components/topnav/topnav.component\";\nexport class LayoutComponent {\n  constructor() {}\n  ngOnInit() {}\n  static {\n    this.ɵfac = function LayoutComponent_Factory(t) {\n      return new (t || LayoutComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LayoutComponent,\n      selectors: [[\"app-layout\"]],\n      decls: 6,\n      vars: 0,\n      consts: [[1, \"layout-container\"], [1, \"main-content\"], [1, \"page-content\"]],\n      template: function LayoutComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"app-sidebar\");\n          i0.ɵɵelementStart(2, \"div\", 1);\n          i0.ɵɵelement(3, \"app-topnav\");\n          i0.ɵɵelementStart(4, \"div\", 2);\n          i0.ɵɵelement(5, \"router-outlet\");\n          i0.ɵɵelementEnd()()();\n        }\n      },\n      dependencies: [i1.RouterOutlet, i2.SidebarComponent, i3.TopnavComponent],\n      styles: [\".layout-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  height: 100vh;\\n  width: 100%;\\n  overflow: hidden;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n  background-color: #f5f5f5;\\n}\\n\\n.page-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 20px;\\n  overflow-y: auto;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbGF5b3V0L2xheW91dC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGFBQUE7RUFDQSxhQUFBO0VBQ0EsV0FBQTtFQUNBLGdCQUFBO0FBQ0Y7O0FBRUE7RUFDRSxPQUFBO0VBQ0EsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsZ0JBQUE7RUFDQSx5QkFBQTtBQUNGOztBQUVBO0VBQ0UsT0FBQTtFQUNBLGFBQUE7RUFDQSxnQkFBQTtBQUNGIiwic291cmNlc0NvbnRlbnQiOlsiLmxheW91dC1jb250YWluZXIge1xuICBkaXNwbGF5OiBmbGV4O1xuICBoZWlnaHQ6IDEwMHZoO1xuICB3aWR0aDogMTAwJTtcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcbn1cblxuLm1haW4tY29udGVudCB7XG4gIGZsZXg6IDE7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gIG92ZXJmbG93OiBoaWRkZW47XG4gIGJhY2tncm91bmQtY29sb3I6ICNmNWY1ZjU7XG59XG5cbi5wYWdlLWNvbnRlbnQge1xuICBmbGV4OiAxO1xuICBwYWRkaW5nOiAyMHB4O1xuICBvdmVyZmxvdy15OiBhdXRvO1xufVxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["LayoutComponent", "constructor", "ngOnInit", "selectors", "decls", "vars", "consts", "template", "LayoutComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd"], "sources": ["C:\\cal_wf\\Augment\\4-img\\apqr400bl\\src\\app\\layout\\layout.component.ts", "C:\\cal_wf\\Augment\\4-img\\apqr400bl\\src\\app\\layout\\layout.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\n\n@Component({\n  selector: 'app-layout',\n  templateUrl: './layout.component.html',\n  styleUrls: ['./layout.component.scss']\n})\nexport class LayoutComponent implements OnInit {\n  constructor() { }\n\n  ngOnInit(): void {\n  }\n}\n", "<div class=\"layout-container\">\n  <app-sidebar></app-sidebar>\n  <div class=\"main-content\">\n    <app-topnav></app-topnav>\n    <div class=\"page-content\">\n      <router-outlet></router-outlet>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;;;AAOA,OAAM,MAAOA,eAAe;EAC1BC,YAAA,GAAgB;EAEhBC,QAAQA,CAAA,GACR;;;uBAJWF,eAAe;IAAA;EAAA;;;YAAfA,eAAe;MAAAG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP5BE,EAAA,CAAAC,cAAA,aAA8B;UAC5BD,EAAA,CAAAE,SAAA,kBAA2B;UAC3BF,EAAA,CAAAC,cAAA,aAA0B;UACxBD,EAAA,CAAAE,SAAA,iBAAyB;UACzBF,EAAA,CAAAC,cAAA,aAA0B;UACxBD,EAAA,CAAAE,SAAA,oBAA+B;UACjCF,EAAA,CAAAG,YAAA,EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}