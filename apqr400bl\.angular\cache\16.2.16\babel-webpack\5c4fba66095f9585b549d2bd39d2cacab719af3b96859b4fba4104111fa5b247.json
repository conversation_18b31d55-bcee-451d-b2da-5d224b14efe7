{"ast": null, "code": "import { Observable } from '../Observable';\nexport function scheduleArray(input, scheduler) {\n  return new Observable(subscriber => {\n    let i = 0;\n    return scheduler.schedule(function () {\n      if (i === input.length) {\n        subscriber.complete();\n      } else {\n        subscriber.next(input[i++]);\n        if (!subscriber.closed) {\n          this.schedule();\n        }\n      }\n    });\n  });\n}", "map": {"version": 3, "names": ["Observable", "scheduleArray", "input", "scheduler", "subscriber", "i", "schedule", "length", "complete", "next", "closed"], "sources": ["C:/cal_wf/Augment/3-img/apqr400bl/node_modules/rxjs/dist/esm/internal/scheduled/scheduleArray.js"], "sourcesContent": ["import { Observable } from '../Observable';\nexport function scheduleArray(input, scheduler) {\n    return new Observable((subscriber) => {\n        let i = 0;\n        return scheduler.schedule(function () {\n            if (i === input.length) {\n                subscriber.complete();\n            }\n            else {\n                subscriber.next(input[i++]);\n                if (!subscriber.closed) {\n                    this.schedule();\n                }\n            }\n        });\n    });\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,OAAO,SAASC,aAAaA,CAACC,KAAK,EAAEC,SAAS,EAAE;EAC5C,OAAO,IAAIH,UAAU,CAAEI,UAAU,IAAK;IAClC,IAAIC,CAAC,GAAG,CAAC;IACT,OAAOF,SAAS,CAACG,QAAQ,CAAC,YAAY;MAClC,IAAID,CAAC,KAAKH,KAAK,CAACK,MAAM,EAAE;QACpBH,UAAU,CAACI,QAAQ,CAAC,CAAC;MACzB,CAAC,MACI;QACDJ,UAAU,CAACK,IAAI,CAACP,KAAK,CAACG,CAAC,EAAE,CAAC,CAAC;QAC3B,IAAI,CAACD,UAAU,CAACM,MAAM,EAAE;UACpB,IAAI,CAACJ,QAAQ,CAAC,CAAC;QACnB;MACJ;IACJ,CAAC,CAAC;EACN,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}