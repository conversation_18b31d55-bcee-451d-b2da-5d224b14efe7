{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ModuleRoutingModule } from './module-routing.module';\nimport { ModuleComponent } from './module.component';\nimport { MatIconModule } from '@angular/material/icon';\nimport * as i0 from \"@angular/core\";\nexport class ModuleModule {\n  static {\n    this.ɵfac = function ModuleModule_Factory(t) {\n      return new (t || ModuleModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ModuleModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, ModuleRoutingModule, MatIconModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ModuleModule, {\n    declarations: [ModuleComponent],\n    imports: [CommonModule, ModuleRoutingModule, MatIconModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ModuleRoutingModule", "ModuleComponent", "MatIconModule", "ModuleModule", "declarations", "imports"], "sources": ["C:\\cal_wf\\Augment\\4-img\\apqr400bl\\src\\app\\module\\module.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ModuleRoutingModule } from './module-routing.module';\nimport { ModuleComponent } from './module.component';\nimport { MatIconModule } from '@angular/material/icon';\n\n@NgModule({\n  declarations: [\n    ModuleComponent\n  ],\n  imports: [\n    CommonModule,\n    ModuleRoutingModule,\n    MatIconModule\n  ]\n})\nexport class ModuleModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,SAASC,eAAe,QAAQ,oBAAoB;AACpD,SAASC,aAAa,QAAQ,wBAAwB;;AAYtD,OAAM,MAAOC,YAAY;;;uBAAZA,YAAY;IAAA;EAAA;;;YAAZA;IAAY;EAAA;;;gBALrBJ,YAAY,EACZC,mBAAmB,EACnBE,aAAa;IAAA;EAAA;;;2EAGJC,YAAY;IAAAC,YAAA,GARrBH,eAAe;IAAAI,OAAA,GAGfN,YAAY,EACZC,mBAAmB,EACnBE,aAAa;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}