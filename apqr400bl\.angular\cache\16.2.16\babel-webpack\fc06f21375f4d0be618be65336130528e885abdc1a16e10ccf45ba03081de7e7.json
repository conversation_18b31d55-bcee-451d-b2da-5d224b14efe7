{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { LayoutRoutingModule } from './layout-routing.module';\nimport { LayoutComponent } from './layout.component';\nimport { SidebarComponent } from './components/sidebar/sidebar.component';\nimport { TopnavComponent } from './components/topnav/topnav.component';\nimport { DashboardComponent } from './components/dashboard/dashboard.component';\nimport { ApqrFlowComponent } from './components/apqr-flow/apqr-flow.component';\nimport { ReportsComponent } from './components/reports/reports.component';\nimport { AnalyticsComponent } from './components/analytics/analytics.component';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatBadgeModule } from '@angular/material/badge';\nimport * as i0 from \"@angular/core\";\nexport class LayoutModule {\n  static {\n    this.ɵfac = function LayoutModule_Factory(t) {\n      return new (t || LayoutModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: LayoutModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, LayoutRoutingModule, MatIconModule, MatButtonModule, MatTooltipModule, MatBadgeModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(LayoutModule, {\n    declarations: [LayoutComponent, SidebarComponent, TopnavComponent, DashboardComponent, ApqrFlowComponent, ReportsComponent, AnalyticsComponent],\n    imports: [CommonModule, LayoutRoutingModule, MatIconModule, MatButtonModule, MatTooltipModule, MatBadgeModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "LayoutRoutingModule", "LayoutComponent", "SidebarComponent", "TopnavComponent", "DashboardComponent", "ApqrFlowComponent", "ReportsComponent", "AnalyticsComponent", "MatIconModule", "MatButtonModule", "MatTooltipModule", "MatBadgeModule", "LayoutModule", "declarations", "imports"], "sources": ["C:\\cal_wf\\Augment\\4-img\\apqr400bl\\src\\app\\layout\\layout.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { LayoutRoutingModule } from './layout-routing.module';\nimport { LayoutComponent } from './layout.component';\nimport { SidebarComponent } from './components/sidebar/sidebar.component';\nimport { TopnavComponent } from './components/topnav/topnav.component';\nimport { DashboardComponent } from './components/dashboard/dashboard.component';\nimport { ApqrFlowComponent } from './components/apqr-flow/apqr-flow.component';\nimport { ReportsComponent } from './components/reports/reports.component';\nimport { AnalyticsComponent } from './components/analytics/analytics.component';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatBadgeModule } from '@angular/material/badge';\n\n@NgModule({\n  declarations: [\n    LayoutComponent,\n    SidebarComponent,\n    TopnavComponent,\n    DashboardComponent,\n    Apqr<PERSON>lowComponent,\n    ReportsComponent,\n    AnalyticsComponent\n  ],\n  imports: [\n    CommonModule,\n    LayoutRoutingModule,\n    MatIconModule,\n    MatButtonModule,\n    MatTooltipModule,\n    MatBadgeModule\n  ]\n})\nexport class LayoutModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,SAASC,eAAe,QAAQ,oBAAoB;AACpD,SAASC,gBAAgB,QAAQ,wCAAwC;AACzE,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAASC,kBAAkB,QAAQ,4CAA4C;AAC/E,SAASC,iBAAiB,QAAQ,4CAA4C;AAC9E,SAASC,gBAAgB,QAAQ,wCAAwC;AACzE,SAASC,kBAAkB,QAAQ,4CAA4C;AAC/E,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,cAAc,QAAQ,yBAAyB;;AAqBxD,OAAM,MAAOC,YAAY;;;uBAAZA,YAAY;IAAA;EAAA;;;YAAZA;IAAY;EAAA;;;gBARrBb,YAAY,EACZC,mBAAmB,EACnBQ,aAAa,EACbC,eAAe,EACfC,gBAAgB,EAChBC,cAAc;IAAA;EAAA;;;2EAGLC,YAAY;IAAAC,YAAA,GAjBrBZ,eAAe,EACfC,gBAAgB,EAChBC,eAAe,EACfC,kBAAkB,EAClBC,iBAAiB,EACjBC,gBAAgB,EAChBC,kBAAkB;IAAAO,OAAA,GAGlBf,YAAY,EACZC,mBAAmB,EACnBQ,aAAa,EACbC,eAAe,EACfC,gBAAgB,EAChBC,cAAc;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}