import { Component, OnInit } from '@angular/core';
import { SelectionService } from '../../../shared/selection.service';

@Component({
  selector: 'app-topnav',
  templateUrl: './topnav.component.html',
  styleUrls: ['./topnav.component.scss']
})
export class TopnavComponent implements OnInit {
  selectedModule = '';
  selectedPlant = '';
  pageTitle = 'Dashboard';

  constructor(private selectionService: SelectionService) { }

  ngOnInit(): void {
    this.selectionService.selectedModule$.subscribe(module => {
      this.selectedModule = module;
    });
    
    this.selectionService.selectedPlant$.subscribe(plant => {
      this.selectedPlant = plant;
    });
  }
}
