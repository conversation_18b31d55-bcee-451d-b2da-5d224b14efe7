<div class="module-selection-container">
  <div class="left-panel">
    <div class="quari-logo">
      <div class="logo-image">
        <img src="assets/quariDark.png" alt="QUARI Logo">
      </div>
    </div>
  </div>
  <div class="right-panel">
    <div class="module-grid">
      <div class="module-card" *ngFor="let module of modules" (click)="onModuleSelect(module.name)">
        <div class="card-content">
          <div class="icon-container">
            <mat-icon [style.color]="module.color">{{module.icon}}</mat-icon>
          </div>
          <div class="module-name">
            <span class="caliber">Caliber</span> <span class="product-name">{{module.name.replace('Caliber ', '')}}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="footer">
      <div class="footer-links">
        <a href="#">Contact Admin</a> |
        <a href="#"><mat-icon class="language-icon">language</mat-icon> Choose Language</a>
      </div>
    </div>
  </div>
</div>
