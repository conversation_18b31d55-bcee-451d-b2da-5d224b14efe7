<div class="plantselection-container">
  <div class="left-panel">
    <div class="quari-logo">
      <div class="logo-image">
        <img src="assets/quariDark.png" alt="QUARI Logo">
      </div>
    </div>
  </div>
  <div class="right-panel">
    <div class="user-info">
      <div class="user-avatar">
        <img src="assets/user-avatar.svg" alt="User Avatar">
      </div>
      <div class="user-greeting">
        Hello, Sumathi <mat-icon>arrow_drop_down</mat-icon>
      </div>
    </div>

    <div class="plant-selection-content">
      <h2>Select a plant</h2>

      <div class="plant-list">
        <div class="plant-item" *ngFor="let plant of plants" (click)="onPlantSelect(plant.name)">
          <div class="plant-name">{{ plant.name }}</div>
          <mat-icon>chevron_right</mat-icon>
        </div>
      </div>
    </div>

    <div class="footer">
      <div class="footer-links">
        <a href="#">Contact Admin</a> |
        <a href="#"><mat-icon class="language-icon">language</mat-icon> Choose Language</a>
      </div>
    </div>
  </div>
</div>
