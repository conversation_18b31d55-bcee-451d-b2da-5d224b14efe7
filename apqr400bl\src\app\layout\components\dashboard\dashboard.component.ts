import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit {
  batchStats = [
    { label: 'Batch Released', value: '23' },
    { label: 'Batch Rejected', value: '02' },
    { label: 'Batch in process', value: '14' }
  ];

  deviationStats = [
    { label: 'Deviation', value: '03' },
    { label: 'CC', value: '05' },
    { label: 'CAPA', value: '06' },
    { label: 'Complaint', value: '02' }
  ];

  apqrStats = [
    { label: 'aPQR till date', value: '33' },
    { label: 'aPQR year long', value: '05' },
    { label: 'Products', value: '300' }
  ];

  taskStats = [
    { label: 'Assignments', value: '05' },
    { label: 'Spreadsheet Return', value: '03' },
    { label: 'Sections Update', value: '02' }
  ];

  schedulerItems = [
    { id: '29_aPQR/2140/02-Apr-2025', status: 'Completed' },
    { id: '29_aPQR/2140/02-Apr-2025', status: 'Completed' },
    { id: '29_aPQR/2140/02-Apr-2025', status: 'In Progress' },
    { id: '29_aPQR/2140/02-Apr-2025', status: 'In Progress' },
    { id: '29_aPQR/2140/02-Apr-2025', status: 'Not Defined' }
  ];

  constructor() { }

  ngOnInit(): void {
  }
}
