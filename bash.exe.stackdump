Stack trace:
Frame         Function      Args
0007FFFFBBA0  00021006118E (00021028DEE8, 000210272B3E, 0007FFFFBBA0, 0007FFFFAAA0) msys-2.0.dll+0x2118E
0007FFFFBBA0  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFFBE78) msys-2.0.dll+0x69BA
0007FFFFBBA0  0002100469F2 (00021028DF99, 0007FFFFBA58, 0007FFFFBBA0, 000000000000) msys-2.0.dll+0x69F2
0007FFFFBBA0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFBBA0  00021006A545 (0007FFFFBBB0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFFBE80  00021006B9A5 (0007FFFFBBB0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFE74A30000 ntdll.dll
7FFE74510000 KERNEL32.DLL
7FFE72640000 KERNELBASE.dll
7FFE74270000 USER32.dll
7FFE724C0000 win32u.dll
7FFE73FF0000 GDI32.dll
000210040000 msys-2.0.dll
7FFE72940000 gdi32full.dll
7FFE725A0000 msvcp_win.dll
7FFE720D0000 ucrtbase.dll
7FFE745E0000 advapi32.dll
7FFE748F0000 msvcrt.dll
7FFE740F0000 sechost.dll
7FFE73CE0000 RPCRT4.dll
7FFE72260000 bcrypt.dll
7FFE71980000 CRYPTBASE.DLL
7FFE721D0000 bcryptPrimitives.dll
7FFE734E0000 IMM32.DLL
