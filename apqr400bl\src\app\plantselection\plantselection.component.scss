.plantselection-container {
  display: flex;
  min-height: 100vh;
  width: 100%;
  font-family: 'Roboto', sans-serif;
}

.left-panel {
  width: 60%;
  background-color: #002333;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0;
  color: white;
  overflow: hidden;
}

.quari-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.logo-image {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.logo-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.caliber {
  color: #00a99d;
  font-weight: 500;
}

.right-panel {
  width: 40%;
  display: flex;
  flex-direction: column;
  padding: 2rem;
  background-color: #f8f9fa;
}

.user-info {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1rem 0;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 0.5rem;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-greeting {
  display: flex;
  align-items: center;
  color: #333;
  font-size: 0.9rem;
}

.plant-selection-content {
  flex: 1;
}

h2 {
  font-size: 1.5rem;
  font-weight: 500;
  color: #333;
  margin-bottom: 2rem;
}

.plant-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.plant-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
  border-bottom: 1px solid #e0e0e0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.plant-item:hover {
  background-color: #f0f0f0;
}

.plant-name {
  font-size: 1rem;
  color: #333;
}

.footer {
  margin-top: auto;
  text-align: right;
  padding-top: 1rem;
  border-top: 1px solid #eee;
}

.footer-links a {
  color: #666;
  text-decoration: none;
  font-size: 0.9rem;
  display: inline-flex;
  align-items: center;
}

.footer-links a:hover {
  color: #00a99d;
}

.language-icon {
  font-size: 1.2rem;
  height: 1.2rem;
  width: 1.2rem;
  margin-right: 0.2rem;
}

/* Responsive styles */
@media (min-width: 1600px) {
  .logo-image img {
    object-position: center;
  }
}

@media (max-width: 992px) {
  .plantselection-container {
    flex-direction: column;
  }

  .left-panel, .right-panel {
    width: 100%;
  }

  .left-panel {
    min-height: 40vh;
    padding: 0;
  }

  .quari-logo {
    min-height: 40vh;
  }

  .right-panel {
    min-height: auto;
  }

  .logo-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
  }
}

@media (max-width: 576px) {
  .left-panel {
    padding: 0;
  }

  .right-panel {
    padding: 1.5rem 1rem;
    min-height: auto; /* Allow it to size to content on small screens */
  }

  .footer-links {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.5rem;
  }
}
