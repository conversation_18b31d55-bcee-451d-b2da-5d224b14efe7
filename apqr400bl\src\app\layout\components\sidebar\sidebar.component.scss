.sidebar {
  width: 110px;
  height: 100%;
  background-color: #002333;
  color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem 0;
}

.logo-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 2rem;
  text-align: center;
}

.logo {
  width: 50px;
  height: 50px;
  margin-bottom: 0.5rem;
}

.logo img {
  width: 100%;
  height: 100%;
}

.module-name {
  font-size: 0.9rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.caliber {
  color: #00a99d;
  font-weight: 500;
}

.product-name {
  font-weight: 500;
}

.menu-container {
  flex: 1;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.main-menu {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  flex: 1;
  position: relative;
}

.menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 1rem 0;
  cursor: pointer;
  transition: background-color 0.2s;
  position: relative;
}

.menu-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.menu-item.active {
  background-color: rgba(255, 255, 255, 0.15);
}

.menu-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 4px;
  background-color: #00a99d;
}

.menu-item mat-icon {
  font-size: 24px;
  width: 24px;
  height: 24px;
  margin-bottom: 0.5rem;
}

.menu-text {
  font-size: 0.8rem;
  text-align: center;
}

.bottom-menu {
  position: absolute;
  bottom: 0;
}

.bottom-menu:nth-last-child(1) {
  bottom: 0;
}

.bottom-menu:nth-last-child(2) {
  bottom: 70px;
}
