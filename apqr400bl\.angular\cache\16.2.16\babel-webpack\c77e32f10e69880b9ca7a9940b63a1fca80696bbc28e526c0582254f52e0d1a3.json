{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Version, InjectionToken, inject, NgModule, Optional, Inject, LOCALE_ID, Injectable, Directive, Input, Component, ViewEncapsulation, ChangeDetectionStrategy, EventEmitter, Output, ViewChild, ANIMATION_MODULE_TYPE as ANIMATION_MODULE_TYPE$1, NgZone, ElementRef } from '@angular/core';\nimport * as i1 from '@angular/cdk/a11y';\nimport { isFakeMousedownFromScreenReader, isFakeTouchstartFromScreenReader } from '@angular/cdk/a11y';\nimport { BidiModule } from '@angular/cdk/bidi';\nimport { VERSION as VERSION$1 } from '@angular/cdk';\nimport * as i3 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i1$1 from '@angular/cdk/platform';\nimport { Platform, _isTestEnvironment, normalizePassiveListenerOptions, _getEventTarget } from '@angular/cdk/platform';\nimport { coerceBooleanProperty, coerceNumberProperty, coerceElement } from '@angular/cdk/coercion';\nimport { Observable, Subject } from 'rxjs';\nimport { startWith } from 'rxjs/operators';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport { ENTER, SPACE, hasModifierKey } from '@angular/cdk/keycodes';\n\n/** Current version of Angular Material. */\nconst _c0 = [\"*\", [[\"mat-option\"], [\"ng-container\"]]];\nconst _c1 = [\"*\", \"mat-option, ng-container\"];\nconst _c2 = [\"text\"];\nfunction MatOption_mat_pseudo_checkbox_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-pseudo-checkbox\", 6);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r0.disabled)(\"state\", ctx_r0.selected ? \"checked\" : \"unchecked\");\n  }\n}\nfunction MatOption_mat_pseudo_checkbox_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-pseudo-checkbox\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.disabled);\n  }\n}\nfunction MatOption_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 8);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r3.group.label, \")\");\n  }\n}\nconst _c3 = [[[\"mat-icon\"]], \"*\"];\nconst _c4 = [\"mat-icon\", \"*\"];\nconst VERSION = new Version('16.2.14');\n\n/** @docs-private */\nclass AnimationCurves {\n  static {\n    this.STANDARD_CURVE = 'cubic-bezier(0.4,0.0,0.2,1)';\n  }\n  static {\n    this.DECELERATION_CURVE = 'cubic-bezier(0.0,0.0,0.2,1)';\n  }\n  static {\n    this.ACCELERATION_CURVE = 'cubic-bezier(0.4,0.0,1,1)';\n  }\n  static {\n    this.SHARP_CURVE = 'cubic-bezier(0.4,0.0,0.6,1)';\n  }\n}\n/** @docs-private */\nclass AnimationDurations {\n  static {\n    this.COMPLEX = '375ms';\n  }\n  static {\n    this.ENTERING = '225ms';\n  }\n  static {\n    this.EXITING = '195ms';\n  }\n}\n\n/** @docs-private */\nfunction MATERIAL_SANITY_CHECKS_FACTORY() {\n  return true;\n}\n/** Injection token that configures whether the Material sanity checks are enabled. */\nconst MATERIAL_SANITY_CHECKS = new InjectionToken('mat-sanity-checks', {\n  providedIn: 'root',\n  factory: MATERIAL_SANITY_CHECKS_FACTORY\n});\n/**\n * Module that captures anything that should be loaded and/or run for *all* Angular Material\n * components. This includes Bidi, etc.\n *\n * This module should be imported to each top-level component module (e.g., MatTabsModule).\n */\nclass MatCommonModule {\n  constructor(highContrastModeDetector, _sanityChecks, _document) {\n    this._sanityChecks = _sanityChecks;\n    this._document = _document;\n    /** Whether we've done the global sanity checks (e.g. a theme is loaded, there is a doctype). */\n    this._hasDoneGlobalChecks = false;\n    // While A11yModule also does this, we repeat it here to avoid importing A11yModule\n    // in MatCommonModule.\n    highContrastModeDetector._applyBodyHighContrastModeCssClasses();\n    if (!this._hasDoneGlobalChecks) {\n      this._hasDoneGlobalChecks = true;\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        // Inject in here so the reference to `Platform` can be removed in production mode.\n        const platform = inject(Platform, {\n          optional: true\n        });\n        if (this._checkIsEnabled('doctype')) {\n          _checkDoctypeIsDefined(this._document);\n        }\n        if (this._checkIsEnabled('theme')) {\n          _checkThemeIsPresent(this._document, !!platform?.isBrowser);\n        }\n        if (this._checkIsEnabled('version')) {\n          _checkCdkVersionMatch();\n        }\n      }\n    }\n  }\n  /** Gets whether a specific sanity check is enabled. */\n  _checkIsEnabled(name) {\n    if (_isTestEnvironment()) {\n      return false;\n    }\n    if (typeof this._sanityChecks === 'boolean') {\n      return this._sanityChecks;\n    }\n    return !!this._sanityChecks[name];\n  }\n  static {\n    this.ɵfac = function MatCommonModule_Factory(t) {\n      return new (t || MatCommonModule)(i0.ɵɵinject(i1.HighContrastModeDetector), i0.ɵɵinject(MATERIAL_SANITY_CHECKS, 8), i0.ɵɵinject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatCommonModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [BidiModule, BidiModule]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCommonModule, [{\n    type: NgModule,\n    args: [{\n      imports: [BidiModule],\n      exports: [BidiModule]\n    }]\n  }], function () {\n    return [{\n      type: i1.HighContrastModeDetector\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MATERIAL_SANITY_CHECKS]\n      }]\n    }, {\n      type: Document,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n/** Checks that the page has a doctype. */\nfunction _checkDoctypeIsDefined(doc) {\n  if (!doc.doctype) {\n    console.warn('Current document does not have a doctype. This may cause ' + 'some Angular Material components not to behave as expected.');\n  }\n}\n/** Checks that a theme has been included. */\nfunction _checkThemeIsPresent(doc, isBrowser) {\n  // We need to assert that the `body` is defined, because these checks run very early\n  // and the `body` won't be defined if the consumer put their scripts in the `head`.\n  if (!doc.body || !isBrowser) {\n    return;\n  }\n  const testElement = doc.createElement('div');\n  testElement.classList.add('mat-theme-loaded-marker');\n  doc.body.appendChild(testElement);\n  const computedStyle = getComputedStyle(testElement);\n  // In some situations the computed style of the test element can be null. For example in\n  // Firefox, the computed style is null if an application is running inside of a hidden iframe.\n  // See: https://bugzilla.mozilla.org/show_bug.cgi?id=548397\n  if (computedStyle && computedStyle.display !== 'none') {\n    console.warn('Could not find Angular Material core theme. Most Material ' + 'components may not work as expected. For more info refer ' + 'to the theming guide: https://material.angular.io/guide/theming');\n  }\n  testElement.remove();\n}\n/** Checks whether the Material version matches the CDK version. */\nfunction _checkCdkVersionMatch() {\n  if (VERSION.full !== VERSION$1.full) {\n    console.warn('The Angular Material version (' + VERSION.full + ') does not match ' + 'the Angular CDK version (' + VERSION$1.full + ').\\n' + 'Please ensure the versions of these two packages exactly match.');\n  }\n}\nfunction mixinDisabled(base) {\n  return class extends base {\n    get disabled() {\n      return this._disabled;\n    }\n    set disabled(value) {\n      this._disabled = coerceBooleanProperty(value);\n    }\n    constructor(...args) {\n      super(...args);\n      this._disabled = false;\n    }\n  };\n}\nfunction mixinColor(base, defaultColor) {\n  return class extends base {\n    get color() {\n      return this._color;\n    }\n    set color(value) {\n      const colorPalette = value || this.defaultColor;\n      if (colorPalette !== this._color) {\n        if (this._color) {\n          this._elementRef.nativeElement.classList.remove(`mat-${this._color}`);\n        }\n        if (colorPalette) {\n          this._elementRef.nativeElement.classList.add(`mat-${colorPalette}`);\n        }\n        this._color = colorPalette;\n      }\n    }\n    constructor(...args) {\n      super(...args);\n      this.defaultColor = defaultColor;\n      // Set the default color that can be specified from the mixin.\n      this.color = defaultColor;\n    }\n  };\n}\nfunction mixinDisableRipple(base) {\n  return class extends base {\n    /** Whether the ripple effect is disabled or not. */\n    get disableRipple() {\n      return this._disableRipple;\n    }\n    set disableRipple(value) {\n      this._disableRipple = coerceBooleanProperty(value);\n    }\n    constructor(...args) {\n      super(...args);\n      this._disableRipple = false;\n    }\n  };\n}\nfunction mixinTabIndex(base, defaultTabIndex = 0) {\n  return class extends base {\n    get tabIndex() {\n      return this.disabled ? -1 : this._tabIndex;\n    }\n    set tabIndex(value) {\n      // If the specified tabIndex value is null or undefined, fall back to the default value.\n      this._tabIndex = value != null ? coerceNumberProperty(value) : this.defaultTabIndex;\n    }\n    constructor(...args) {\n      super(...args);\n      this._tabIndex = defaultTabIndex;\n      this.defaultTabIndex = defaultTabIndex;\n    }\n  };\n}\nfunction mixinErrorState(base) {\n  return class extends base {\n    /** Updates the error state based on the provided error state matcher. */\n    updateErrorState() {\n      const oldState = this.errorState;\n      const parent = this._parentFormGroup || this._parentForm;\n      const matcher = this.errorStateMatcher || this._defaultErrorStateMatcher;\n      const control = this.ngControl ? this.ngControl.control : null;\n      const newState = matcher.isErrorState(control, parent);\n      if (newState !== oldState) {\n        this.errorState = newState;\n        this.stateChanges.next();\n      }\n    }\n    constructor(...args) {\n      super(...args);\n      /** Whether the component is in an error state. */\n      this.errorState = false;\n    }\n  };\n}\n\n/** Mixin to augment a directive with an initialized property that will emits when ngOnInit ends. */\nfunction mixinInitialized(base) {\n  return class extends base {\n    constructor(...args) {\n      super(...args);\n      /** Whether this directive has been marked as initialized. */\n      this._isInitialized = false;\n      /**\n       * List of subscribers that subscribed before the directive was initialized. Should be notified\n       * during _markInitialized. Set to null after pending subscribers are notified, and should\n       * not expect to be populated after.\n       */\n      this._pendingSubscribers = [];\n      /**\n       * Observable stream that emits when the directive initializes. If already initialized, the\n       * subscriber is stored to be notified once _markInitialized is called.\n       */\n      this.initialized = new Observable(subscriber => {\n        // If initialized, immediately notify the subscriber. Otherwise store the subscriber to notify\n        // when _markInitialized is called.\n        if (this._isInitialized) {\n          this._notifySubscriber(subscriber);\n        } else {\n          this._pendingSubscribers.push(subscriber);\n        }\n      });\n    }\n    /**\n     * Marks the state as initialized and notifies pending subscribers. Should be called at the end\n     * of ngOnInit.\n     * @docs-private\n     */\n    _markInitialized() {\n      if (this._isInitialized && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('This directive has already been marked as initialized and ' + 'should not be called twice.');\n      }\n      this._isInitialized = true;\n      this._pendingSubscribers.forEach(this._notifySubscriber);\n      this._pendingSubscribers = null;\n    }\n    /** Emits and completes the subscriber stream (should only emit once). */\n    _notifySubscriber(subscriber) {\n      subscriber.next();\n      subscriber.complete();\n    }\n  };\n}\n\n/** InjectionToken for datepicker that can be used to override default locale code. */\nconst MAT_DATE_LOCALE = new InjectionToken('MAT_DATE_LOCALE', {\n  providedIn: 'root',\n  factory: MAT_DATE_LOCALE_FACTORY\n});\n/** @docs-private */\nfunction MAT_DATE_LOCALE_FACTORY() {\n  return inject(LOCALE_ID);\n}\n/** Adapts type `D` to be usable as a date by cdk-based components that work with dates. */\nclass DateAdapter {\n  constructor() {\n    this._localeChanges = new Subject();\n    /** A stream that emits when the locale changes. */\n    this.localeChanges = this._localeChanges;\n  }\n  /**\n   * Given a potential date object, returns that same date object if it is\n   * a valid date, or `null` if it's not a valid date.\n   * @param obj The object to check.\n   * @returns A date or `null`.\n   */\n  getValidDateOrNull(obj) {\n    return this.isDateInstance(obj) && this.isValid(obj) ? obj : null;\n  }\n  /**\n   * Attempts to deserialize a value to a valid date object. This is different from parsing in that\n   * deserialize should only accept non-ambiguous, locale-independent formats (e.g. a ISO 8601\n   * string). The default implementation does not allow any deserialization, it simply checks that\n   * the given value is already a valid date object or null. The `<mat-datepicker>` will call this\n   * method on all of its `@Input()` properties that accept dates. It is therefore possible to\n   * support passing values from your backend directly to these properties by overriding this method\n   * to also deserialize the format used by your backend.\n   * @param value The value to be deserialized into a date object.\n   * @returns The deserialized date object, either a valid date, null if the value can be\n   *     deserialized into a null date (e.g. the empty string), or an invalid date.\n   */\n  deserialize(value) {\n    if (value == null || this.isDateInstance(value) && this.isValid(value)) {\n      return value;\n    }\n    return this.invalid();\n  }\n  /**\n   * Sets the locale used for all dates.\n   * @param locale The new locale.\n   */\n  setLocale(locale) {\n    this.locale = locale;\n    this._localeChanges.next();\n  }\n  /**\n   * Compares two dates.\n   * @param first The first date to compare.\n   * @param second The second date to compare.\n   * @returns 0 if the dates are equal, a number less than 0 if the first date is earlier,\n   *     a number greater than 0 if the first date is later.\n   */\n  compareDate(first, second) {\n    return this.getYear(first) - this.getYear(second) || this.getMonth(first) - this.getMonth(second) || this.getDate(first) - this.getDate(second);\n  }\n  /**\n   * Checks if two dates are equal.\n   * @param first The first date to check.\n   * @param second The second date to check.\n   * @returns Whether the two dates are equal.\n   *     Null dates are considered equal to other null dates.\n   */\n  sameDate(first, second) {\n    if (first && second) {\n      let firstValid = this.isValid(first);\n      let secondValid = this.isValid(second);\n      if (firstValid && secondValid) {\n        return !this.compareDate(first, second);\n      }\n      return firstValid == secondValid;\n    }\n    return first == second;\n  }\n  /**\n   * Clamp the given date between min and max dates.\n   * @param date The date to clamp.\n   * @param min The minimum value to allow. If null or omitted no min is enforced.\n   * @param max The maximum value to allow. If null or omitted no max is enforced.\n   * @returns `min` if `date` is less than `min`, `max` if date is greater than `max`,\n   *     otherwise `date`.\n   */\n  clampDate(date, min, max) {\n    if (min && this.compareDate(date, min) < 0) {\n      return min;\n    }\n    if (max && this.compareDate(date, max) > 0) {\n      return max;\n    }\n    return date;\n  }\n}\nconst MAT_DATE_FORMATS = new InjectionToken('mat-date-formats');\n\n/**\n * Matches strings that have the form of a valid RFC 3339 string\n * (https://tools.ietf.org/html/rfc3339). Note that the string may not actually be a valid date\n * because the regex will match strings an with out of bounds month, date, etc.\n */\nconst ISO_8601_REGEX = /^\\d{4}-\\d{2}-\\d{2}(?:T\\d{2}:\\d{2}:\\d{2}(?:\\.\\d+)?(?:Z|(?:(?:\\+|-)\\d{2}:\\d{2}))?)?$/;\n/** Creates an array and fills it with values. */\nfunction range(length, valueFunction) {\n  const valuesArray = Array(length);\n  for (let i = 0; i < length; i++) {\n    valuesArray[i] = valueFunction(i);\n  }\n  return valuesArray;\n}\n/** Adapts the native JS Date for use with cdk-based components that work with dates. */\nclass NativeDateAdapter extends DateAdapter {\n  constructor(matDateLocale,\n  /**\n   * @deprecated No longer being used. To be removed.\n   * @breaking-change 14.0.0\n   */\n  _platform) {\n    super();\n    /**\n     * @deprecated No longer being used. To be removed.\n     * @breaking-change 14.0.0\n     */\n    this.useUtcForDisplay = false;\n    super.setLocale(matDateLocale);\n  }\n  getYear(date) {\n    return date.getFullYear();\n  }\n  getMonth(date) {\n    return date.getMonth();\n  }\n  getDate(date) {\n    return date.getDate();\n  }\n  getDayOfWeek(date) {\n    return date.getDay();\n  }\n  getMonthNames(style) {\n    const dtf = new Intl.DateTimeFormat(this.locale, {\n      month: style,\n      timeZone: 'utc'\n    });\n    return range(12, i => this._format(dtf, new Date(2017, i, 1)));\n  }\n  getDateNames() {\n    const dtf = new Intl.DateTimeFormat(this.locale, {\n      day: 'numeric',\n      timeZone: 'utc'\n    });\n    return range(31, i => this._format(dtf, new Date(2017, 0, i + 1)));\n  }\n  getDayOfWeekNames(style) {\n    const dtf = new Intl.DateTimeFormat(this.locale, {\n      weekday: style,\n      timeZone: 'utc'\n    });\n    return range(7, i => this._format(dtf, new Date(2017, 0, i + 1)));\n  }\n  getYearName(date) {\n    const dtf = new Intl.DateTimeFormat(this.locale, {\n      year: 'numeric',\n      timeZone: 'utc'\n    });\n    return this._format(dtf, date);\n  }\n  getFirstDayOfWeek() {\n    // We can't tell using native JS Date what the first day of the week is, we default to Sunday.\n    return 0;\n  }\n  getNumDaysInMonth(date) {\n    return this.getDate(this._createDateWithOverflow(this.getYear(date), this.getMonth(date) + 1, 0));\n  }\n  clone(date) {\n    return new Date(date.getTime());\n  }\n  createDate(year, month, date) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      // Check for invalid month and date (except upper bound on date which we have to check after\n      // creating the Date).\n      if (month < 0 || month > 11) {\n        throw Error(`Invalid month index \"${month}\". Month index has to be between 0 and 11.`);\n      }\n      if (date < 1) {\n        throw Error(`Invalid date \"${date}\". Date has to be greater than 0.`);\n      }\n    }\n    let result = this._createDateWithOverflow(year, month, date);\n    // Check that the date wasn't above the upper bound for the month, causing the month to overflow\n    if (result.getMonth() != month && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error(`Invalid date \"${date}\" for month with index \"${month}\".`);\n    }\n    return result;\n  }\n  today() {\n    return new Date();\n  }\n  parse(value, parseFormat) {\n    // We have no way using the native JS Date to set the parse format or locale, so we ignore these\n    // parameters.\n    if (typeof value == 'number') {\n      return new Date(value);\n    }\n    return value ? new Date(Date.parse(value)) : null;\n  }\n  format(date, displayFormat) {\n    if (!this.isValid(date)) {\n      throw Error('NativeDateAdapter: Cannot format invalid date.');\n    }\n    const dtf = new Intl.DateTimeFormat(this.locale, {\n      ...displayFormat,\n      timeZone: 'utc'\n    });\n    return this._format(dtf, date);\n  }\n  addCalendarYears(date, years) {\n    return this.addCalendarMonths(date, years * 12);\n  }\n  addCalendarMonths(date, months) {\n    let newDate = this._createDateWithOverflow(this.getYear(date), this.getMonth(date) + months, this.getDate(date));\n    // It's possible to wind up in the wrong month if the original month has more days than the new\n    // month. In this case we want to go to the last day of the desired month.\n    // Note: the additional + 12 % 12 ensures we end up with a positive number, since JS % doesn't\n    // guarantee this.\n    if (this.getMonth(newDate) != ((this.getMonth(date) + months) % 12 + 12) % 12) {\n      newDate = this._createDateWithOverflow(this.getYear(newDate), this.getMonth(newDate), 0);\n    }\n    return newDate;\n  }\n  addCalendarDays(date, days) {\n    return this._createDateWithOverflow(this.getYear(date), this.getMonth(date), this.getDate(date) + days);\n  }\n  toIso8601(date) {\n    return [date.getUTCFullYear(), this._2digit(date.getUTCMonth() + 1), this._2digit(date.getUTCDate())].join('-');\n  }\n  /**\n   * Returns the given value if given a valid Date or null. Deserializes valid ISO 8601 strings\n   * (https://www.ietf.org/rfc/rfc3339.txt) into valid Dates and empty string into null. Returns an\n   * invalid date for all other values.\n   */\n  deserialize(value) {\n    if (typeof value === 'string') {\n      if (!value) {\n        return null;\n      }\n      // The `Date` constructor accepts formats other than ISO 8601, so we need to make sure the\n      // string is the right format first.\n      if (ISO_8601_REGEX.test(value)) {\n        let date = new Date(value);\n        if (this.isValid(date)) {\n          return date;\n        }\n      }\n    }\n    return super.deserialize(value);\n  }\n  isDateInstance(obj) {\n    return obj instanceof Date;\n  }\n  isValid(date) {\n    return !isNaN(date.getTime());\n  }\n  invalid() {\n    return new Date(NaN);\n  }\n  /** Creates a date but allows the month and date to overflow. */\n  _createDateWithOverflow(year, month, date) {\n    // Passing the year to the constructor causes year numbers <100 to be converted to 19xx.\n    // To work around this we use `setFullYear` and `setHours` instead.\n    const d = new Date();\n    d.setFullYear(year, month, date);\n    d.setHours(0, 0, 0, 0);\n    return d;\n  }\n  /**\n   * Pads a number to make it two digits.\n   * @param n The number to pad.\n   * @returns The padded number.\n   */\n  _2digit(n) {\n    return ('00' + n).slice(-2);\n  }\n  /**\n   * When converting Date object to string, javascript built-in functions may return wrong\n   * results because it applies its internal DST rules. The DST rules around the world change\n   * very frequently, and the current valid rule is not always valid in previous years though.\n   * We work around this problem building a new Date object which has its internal UTC\n   * representation with the local date and time.\n   * @param dtf Intl.DateTimeFormat object, containing the desired string format. It must have\n   *    timeZone set to 'utc' to work fine.\n   * @param date Date from which we want to get the string representation according to dtf\n   * @returns A Date object with its UTC representation based on the passed in date info\n   */\n  _format(dtf, date) {\n    // Passing the year to the constructor causes year numbers <100 to be converted to 19xx.\n    // To work around this we use `setUTCFullYear` and `setUTCHours` instead.\n    const d = new Date();\n    d.setUTCFullYear(date.getFullYear(), date.getMonth(), date.getDate());\n    d.setUTCHours(date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds());\n    return dtf.format(d);\n  }\n  static {\n    this.ɵfac = function NativeDateAdapter_Factory(t) {\n      return new (t || NativeDateAdapter)(i0.ɵɵinject(MAT_DATE_LOCALE, 8), i0.ɵɵinject(i1$1.Platform));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NativeDateAdapter,\n      factory: NativeDateAdapter.ɵfac\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NativeDateAdapter, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_DATE_LOCALE]\n      }]\n    }, {\n      type: i1$1.Platform\n    }];\n  }, null);\n})();\nconst MAT_NATIVE_DATE_FORMATS = {\n  parse: {\n    dateInput: null\n  },\n  display: {\n    dateInput: {\n      year: 'numeric',\n      month: 'numeric',\n      day: 'numeric'\n    },\n    monthYearLabel: {\n      year: 'numeric',\n      month: 'short'\n    },\n    dateA11yLabel: {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    },\n    monthYearA11yLabel: {\n      year: 'numeric',\n      month: 'long'\n    }\n  }\n};\nclass NativeDateModule {\n  static {\n    this.ɵfac = function NativeDateModule_Factory(t) {\n      return new (t || NativeDateModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NativeDateModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [{\n        provide: DateAdapter,\n        useClass: NativeDateAdapter\n      }]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NativeDateModule, [{\n    type: NgModule,\n    args: [{\n      providers: [{\n        provide: DateAdapter,\n        useClass: NativeDateAdapter\n      }]\n    }]\n  }], null, null);\n})();\nclass MatNativeDateModule {\n  static {\n    this.ɵfac = function MatNativeDateModule_Factory(t) {\n      return new (t || MatNativeDateModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatNativeDateModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [{\n        provide: MAT_DATE_FORMATS,\n        useValue: MAT_NATIVE_DATE_FORMATS\n      }],\n      imports: [NativeDateModule]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatNativeDateModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NativeDateModule],\n      providers: [{\n        provide: MAT_DATE_FORMATS,\n        useValue: MAT_NATIVE_DATE_FORMATS\n      }]\n    }]\n  }], null, null);\n})();\n\n/** Error state matcher that matches when a control is invalid and dirty. */\nclass ShowOnDirtyErrorStateMatcher {\n  isErrorState(control, form) {\n    return !!(control && control.invalid && (control.dirty || form && form.submitted));\n  }\n  static {\n    this.ɵfac = function ShowOnDirtyErrorStateMatcher_Factory(t) {\n      return new (t || ShowOnDirtyErrorStateMatcher)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ShowOnDirtyErrorStateMatcher,\n      factory: ShowOnDirtyErrorStateMatcher.ɵfac\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ShowOnDirtyErrorStateMatcher, [{\n    type: Injectable\n  }], null, null);\n})();\n/** Provider that defines how form controls behave with regards to displaying error messages. */\nclass ErrorStateMatcher {\n  isErrorState(control, form) {\n    return !!(control && control.invalid && (control.touched || form && form.submitted));\n  }\n  static {\n    this.ɵfac = function ErrorStateMatcher_Factory(t) {\n      return new (t || ErrorStateMatcher)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ErrorStateMatcher,\n      factory: ErrorStateMatcher.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ErrorStateMatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\n/**\n * Shared directive to count lines inside a text area, such as a list item.\n * Line elements can be extracted with a @ContentChildren(MatLine) query, then\n * counted by checking the query list's length.\n */\nclass MatLine {\n  static {\n    this.ɵfac = function MatLine_Factory(t) {\n      return new (t || MatLine)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatLine,\n      selectors: [[\"\", \"mat-line\", \"\"], [\"\", \"matLine\", \"\"]],\n      hostAttrs: [1, \"mat-line\"]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatLine, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-line], [matLine]',\n      host: {\n        'class': 'mat-line'\n      }\n    }]\n  }], null, null);\n})();\n/**\n * Helper that takes a query list of lines and sets the correct class on the host.\n * @docs-private\n */\nfunction setLines(lines, element, prefix = 'mat') {\n  // Note: doesn't need to unsubscribe, because `changes`\n  // gets completed by Angular when the view is destroyed.\n  lines.changes.pipe(startWith(lines)).subscribe(({\n    length\n  }) => {\n    setClass(element, `${prefix}-2-line`, false);\n    setClass(element, `${prefix}-3-line`, false);\n    setClass(element, `${prefix}-multi-line`, false);\n    if (length === 2 || length === 3) {\n      setClass(element, `${prefix}-${length}-line`, true);\n    } else if (length > 3) {\n      setClass(element, `${prefix}-multi-line`, true);\n    }\n  });\n}\n/** Adds or removes a class from an element. */\nfunction setClass(element, className, isAdd) {\n  element.nativeElement.classList.toggle(className, isAdd);\n}\nclass MatLineModule {\n  static {\n    this.ɵfac = function MatLineModule_Factory(t) {\n      return new (t || MatLineModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatLineModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCommonModule, MatCommonModule]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatLineModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule],\n      exports: [MatLine, MatCommonModule],\n      declarations: [MatLine]\n    }]\n  }], null, null);\n})();\n\n/**\n * Reference to a previously launched ripple element.\n */\nclass RippleRef {\n  constructor(_renderer, /** Reference to the ripple HTML element. */\n  element, /** Ripple configuration used for the ripple. */\n  config, /* Whether animations are forcibly disabled for ripples through CSS. */\n  _animationForciblyDisabledThroughCss = false) {\n    this._renderer = _renderer;\n    this.element = element;\n    this.config = config;\n    this._animationForciblyDisabledThroughCss = _animationForciblyDisabledThroughCss;\n    /** Current state of the ripple. */\n    this.state = 3 /* RippleState.HIDDEN */;\n  }\n  /** Fades out the ripple element. */\n  fadeOut() {\n    this._renderer.fadeOutRipple(this);\n  }\n}\n\n/** Options used to bind a passive capturing event. */\nconst passiveCapturingEventOptions$1 = normalizePassiveListenerOptions({\n  passive: true,\n  capture: true\n});\n/** Manages events through delegation so that as few event handlers as possible are bound. */\nclass RippleEventManager {\n  constructor() {\n    this._events = new Map();\n    /** Event handler that is bound and which dispatches the events to the different targets. */\n    this._delegateEventHandler = event => {\n      const target = _getEventTarget(event);\n      if (target) {\n        this._events.get(event.type)?.forEach((handlers, element) => {\n          if (element === target || element.contains(target)) {\n            handlers.forEach(handler => handler.handleEvent(event));\n          }\n        });\n      }\n    };\n  }\n  /** Adds an event handler. */\n  addHandler(ngZone, name, element, handler) {\n    const handlersForEvent = this._events.get(name);\n    if (handlersForEvent) {\n      const handlersForElement = handlersForEvent.get(element);\n      if (handlersForElement) {\n        handlersForElement.add(handler);\n      } else {\n        handlersForEvent.set(element, new Set([handler]));\n      }\n    } else {\n      this._events.set(name, new Map([[element, new Set([handler])]]));\n      ngZone.runOutsideAngular(() => {\n        document.addEventListener(name, this._delegateEventHandler, passiveCapturingEventOptions$1);\n      });\n    }\n  }\n  /** Removes an event handler. */\n  removeHandler(name, element, handler) {\n    const handlersForEvent = this._events.get(name);\n    if (!handlersForEvent) {\n      return;\n    }\n    const handlersForElement = handlersForEvent.get(element);\n    if (!handlersForElement) {\n      return;\n    }\n    handlersForElement.delete(handler);\n    if (handlersForElement.size === 0) {\n      handlersForEvent.delete(element);\n    }\n    if (handlersForEvent.size === 0) {\n      this._events.delete(name);\n      document.removeEventListener(name, this._delegateEventHandler, passiveCapturingEventOptions$1);\n    }\n  }\n}\n\n/**\n * Default ripple animation configuration for ripples without an explicit\n * animation config specified.\n */\nconst defaultRippleAnimationConfig = {\n  enterDuration: 225,\n  exitDuration: 150\n};\n/**\n * Timeout for ignoring mouse events. Mouse events will be temporary ignored after touch\n * events to avoid synthetic mouse events.\n */\nconst ignoreMouseEventsTimeout = 800;\n/** Options used to bind a passive capturing event. */\nconst passiveCapturingEventOptions = normalizePassiveListenerOptions({\n  passive: true,\n  capture: true\n});\n/** Events that signal that the pointer is down. */\nconst pointerDownEvents = ['mousedown', 'touchstart'];\n/** Events that signal that the pointer is up. */\nconst pointerUpEvents = ['mouseup', 'mouseleave', 'touchend', 'touchcancel'];\n/**\n * Helper service that performs DOM manipulations. Not intended to be used outside this module.\n * The constructor takes a reference to the ripple directive's host element and a map of DOM\n * event handlers to be installed on the element that triggers ripple animations.\n * This will eventually become a custom renderer once Angular support exists.\n * @docs-private\n */\nclass RippleRenderer {\n  static {\n    this._eventManager = new RippleEventManager();\n  }\n  constructor(_target, _ngZone, elementOrElementRef, _platform) {\n    this._target = _target;\n    this._ngZone = _ngZone;\n    this._platform = _platform;\n    /** Whether the pointer is currently down or not. */\n    this._isPointerDown = false;\n    /**\n     * Map of currently active ripple references.\n     * The ripple reference is mapped to its element event listeners.\n     * The reason why `| null` is used is that event listeners are added only\n     * when the condition is truthy (see the `_startFadeOutTransition` method).\n     */\n    this._activeRipples = new Map();\n    /** Whether pointer-up event listeners have been registered. */\n    this._pointerUpEventsRegistered = false;\n    // Only do anything if we're on the browser.\n    if (_platform.isBrowser) {\n      this._containerElement = coerceElement(elementOrElementRef);\n    }\n  }\n  /**\n   * Fades in a ripple at the given coordinates.\n   * @param x Coordinate within the element, along the X axis at which to start the ripple.\n   * @param y Coordinate within the element, along the Y axis at which to start the ripple.\n   * @param config Extra ripple options.\n   */\n  fadeInRipple(x, y, config = {}) {\n    const containerRect = this._containerRect = this._containerRect || this._containerElement.getBoundingClientRect();\n    const animationConfig = {\n      ...defaultRippleAnimationConfig,\n      ...config.animation\n    };\n    if (config.centered) {\n      x = containerRect.left + containerRect.width / 2;\n      y = containerRect.top + containerRect.height / 2;\n    }\n    const radius = config.radius || distanceToFurthestCorner(x, y, containerRect);\n    const offsetX = x - containerRect.left;\n    const offsetY = y - containerRect.top;\n    const enterDuration = animationConfig.enterDuration;\n    const ripple = document.createElement('div');\n    ripple.classList.add('mat-ripple-element');\n    ripple.style.left = `${offsetX - radius}px`;\n    ripple.style.top = `${offsetY - radius}px`;\n    ripple.style.height = `${radius * 2}px`;\n    ripple.style.width = `${radius * 2}px`;\n    // If a custom color has been specified, set it as inline style. If no color is\n    // set, the default color will be applied through the ripple theme styles.\n    if (config.color != null) {\n      ripple.style.backgroundColor = config.color;\n    }\n    ripple.style.transitionDuration = `${enterDuration}ms`;\n    this._containerElement.appendChild(ripple);\n    // By default the browser does not recalculate the styles of dynamically created\n    // ripple elements. This is critical to ensure that the `scale` animates properly.\n    // We enforce a style recalculation by calling `getComputedStyle` and *accessing* a property.\n    // See: https://gist.github.com/paulirish/5d52fb081b3570c81e3a\n    const computedStyles = window.getComputedStyle(ripple);\n    const userTransitionProperty = computedStyles.transitionProperty;\n    const userTransitionDuration = computedStyles.transitionDuration;\n    // Note: We detect whether animation is forcibly disabled through CSS (e.g. through\n    // `transition: none` or `display: none`). This is technically unexpected since animations are\n    // controlled through the animation config, but this exists for backwards compatibility. This\n    // logic does not need to be super accurate since it covers some edge cases which can be easily\n    // avoided by users.\n    const animationForciblyDisabledThroughCss = userTransitionProperty === 'none' ||\n    // Note: The canonical unit for serialized CSS `<time>` properties is seconds. Additionally\n    // some browsers expand the duration for every property (in our case `opacity` and `transform`).\n    userTransitionDuration === '0s' || userTransitionDuration === '0s, 0s' ||\n    // If the container is 0x0, it's likely `display: none`.\n    containerRect.width === 0 && containerRect.height === 0;\n    // Exposed reference to the ripple that will be returned.\n    const rippleRef = new RippleRef(this, ripple, config, animationForciblyDisabledThroughCss);\n    // Start the enter animation by setting the transform/scale to 100%. The animation will\n    // execute as part of this statement because we forced a style recalculation before.\n    // Note: We use a 3d transform here in order to avoid an issue in Safari where\n    // the ripples aren't clipped when inside the shadow DOM (see #24028).\n    ripple.style.transform = 'scale3d(1, 1, 1)';\n    rippleRef.state = 0 /* RippleState.FADING_IN */;\n    if (!config.persistent) {\n      this._mostRecentTransientRipple = rippleRef;\n    }\n    let eventListeners = null;\n    // Do not register the `transition` event listener if fade-in and fade-out duration\n    // are set to zero. The events won't fire anyway and we can save resources here.\n    if (!animationForciblyDisabledThroughCss && (enterDuration || animationConfig.exitDuration)) {\n      this._ngZone.runOutsideAngular(() => {\n        const onTransitionEnd = () => this._finishRippleTransition(rippleRef);\n        const onTransitionCancel = () => this._destroyRipple(rippleRef);\n        ripple.addEventListener('transitionend', onTransitionEnd);\n        // If the transition is cancelled (e.g. due to DOM removal), we destroy the ripple\n        // directly as otherwise we would keep it part of the ripple container forever.\n        // https://www.w3.org/TR/css-transitions-1/#:~:text=no%20longer%20in%20the%20document.\n        ripple.addEventListener('transitioncancel', onTransitionCancel);\n        eventListeners = {\n          onTransitionEnd,\n          onTransitionCancel\n        };\n      });\n    }\n    // Add the ripple reference to the list of all active ripples.\n    this._activeRipples.set(rippleRef, eventListeners);\n    // In case there is no fade-in transition duration, we need to manually call the transition\n    // end listener because `transitionend` doesn't fire if there is no transition.\n    if (animationForciblyDisabledThroughCss || !enterDuration) {\n      this._finishRippleTransition(rippleRef);\n    }\n    return rippleRef;\n  }\n  /** Fades out a ripple reference. */\n  fadeOutRipple(rippleRef) {\n    // For ripples already fading out or hidden, this should be a noop.\n    if (rippleRef.state === 2 /* RippleState.FADING_OUT */ || rippleRef.state === 3 /* RippleState.HIDDEN */) {\n      return;\n    }\n    const rippleEl = rippleRef.element;\n    const animationConfig = {\n      ...defaultRippleAnimationConfig,\n      ...rippleRef.config.animation\n    };\n    // This starts the fade-out transition and will fire the transition end listener that\n    // removes the ripple element from the DOM.\n    rippleEl.style.transitionDuration = `${animationConfig.exitDuration}ms`;\n    rippleEl.style.opacity = '0';\n    rippleRef.state = 2 /* RippleState.FADING_OUT */;\n    // In case there is no fade-out transition duration, we need to manually call the\n    // transition end listener because `transitionend` doesn't fire if there is no transition.\n    if (rippleRef._animationForciblyDisabledThroughCss || !animationConfig.exitDuration) {\n      this._finishRippleTransition(rippleRef);\n    }\n  }\n  /** Fades out all currently active ripples. */\n  fadeOutAll() {\n    this._getActiveRipples().forEach(ripple => ripple.fadeOut());\n  }\n  /** Fades out all currently active non-persistent ripples. */\n  fadeOutAllNonPersistent() {\n    this._getActiveRipples().forEach(ripple => {\n      if (!ripple.config.persistent) {\n        ripple.fadeOut();\n      }\n    });\n  }\n  /** Sets up the trigger event listeners */\n  setupTriggerEvents(elementOrElementRef) {\n    const element = coerceElement(elementOrElementRef);\n    if (!this._platform.isBrowser || !element || element === this._triggerElement) {\n      return;\n    }\n    // Remove all previously registered event listeners from the trigger element.\n    this._removeTriggerEvents();\n    this._triggerElement = element;\n    // Use event delegation for the trigger events since they're\n    // set up during creation and are performance-sensitive.\n    pointerDownEvents.forEach(type => {\n      RippleRenderer._eventManager.addHandler(this._ngZone, type, element, this);\n    });\n  }\n  /**\n   * Handles all registered events.\n   * @docs-private\n   */\n  handleEvent(event) {\n    if (event.type === 'mousedown') {\n      this._onMousedown(event);\n    } else if (event.type === 'touchstart') {\n      this._onTouchStart(event);\n    } else {\n      this._onPointerUp();\n    }\n    // If pointer-up events haven't been registered yet, do so now.\n    // We do this on-demand in order to reduce the total number of event listeners\n    // registered by the ripples, which speeds up the rendering time for large UIs.\n    if (!this._pointerUpEventsRegistered) {\n      // The events for hiding the ripple are bound directly on the trigger, because:\n      // 1. Some of them occur frequently (e.g. `mouseleave`) and any advantage we get from\n      // delegation will be diminished by having to look through all the data structures often.\n      // 2. They aren't as performance-sensitive, because they're bound only after the user\n      // has interacted with an element.\n      this._ngZone.runOutsideAngular(() => {\n        pointerUpEvents.forEach(type => {\n          this._triggerElement.addEventListener(type, this, passiveCapturingEventOptions);\n        });\n      });\n      this._pointerUpEventsRegistered = true;\n    }\n  }\n  /** Method that will be called if the fade-in or fade-in transition completed. */\n  _finishRippleTransition(rippleRef) {\n    if (rippleRef.state === 0 /* RippleState.FADING_IN */) {\n      this._startFadeOutTransition(rippleRef);\n    } else if (rippleRef.state === 2 /* RippleState.FADING_OUT */) {\n      this._destroyRipple(rippleRef);\n    }\n  }\n  /**\n   * Starts the fade-out transition of the given ripple if it's not persistent and the pointer\n   * is not held down anymore.\n   */\n  _startFadeOutTransition(rippleRef) {\n    const isMostRecentTransientRipple = rippleRef === this._mostRecentTransientRipple;\n    const {\n      persistent\n    } = rippleRef.config;\n    rippleRef.state = 1 /* RippleState.VISIBLE */;\n    // When the timer runs out while the user has kept their pointer down, we want to\n    // keep only the persistent ripples and the latest transient ripple. We do this,\n    // because we don't want stacked transient ripples to appear after their enter\n    // animation has finished.\n    if (!persistent && (!isMostRecentTransientRipple || !this._isPointerDown)) {\n      rippleRef.fadeOut();\n    }\n  }\n  /** Destroys the given ripple by removing it from the DOM and updating its state. */\n  _destroyRipple(rippleRef) {\n    const eventListeners = this._activeRipples.get(rippleRef) ?? null;\n    this._activeRipples.delete(rippleRef);\n    // Clear out the cached bounding rect if we have no more ripples.\n    if (!this._activeRipples.size) {\n      this._containerRect = null;\n    }\n    // If the current ref is the most recent transient ripple, unset it\n    // avoid memory leaks.\n    if (rippleRef === this._mostRecentTransientRipple) {\n      this._mostRecentTransientRipple = null;\n    }\n    rippleRef.state = 3 /* RippleState.HIDDEN */;\n    if (eventListeners !== null) {\n      rippleRef.element.removeEventListener('transitionend', eventListeners.onTransitionEnd);\n      rippleRef.element.removeEventListener('transitioncancel', eventListeners.onTransitionCancel);\n    }\n    rippleRef.element.remove();\n  }\n  /** Function being called whenever the trigger is being pressed using mouse. */\n  _onMousedown(event) {\n    // Screen readers will fire fake mouse events for space/enter. Skip launching a\n    // ripple in this case for consistency with the non-screen-reader experience.\n    const isFakeMousedown = isFakeMousedownFromScreenReader(event);\n    const isSyntheticEvent = this._lastTouchStartEvent && Date.now() < this._lastTouchStartEvent + ignoreMouseEventsTimeout;\n    if (!this._target.rippleDisabled && !isFakeMousedown && !isSyntheticEvent) {\n      this._isPointerDown = true;\n      this.fadeInRipple(event.clientX, event.clientY, this._target.rippleConfig);\n    }\n  }\n  /** Function being called whenever the trigger is being pressed using touch. */\n  _onTouchStart(event) {\n    if (!this._target.rippleDisabled && !isFakeTouchstartFromScreenReader(event)) {\n      // Some browsers fire mouse events after a `touchstart` event. Those synthetic mouse\n      // events will launch a second ripple if we don't ignore mouse events for a specific\n      // time after a touchstart event.\n      this._lastTouchStartEvent = Date.now();\n      this._isPointerDown = true;\n      // Use `changedTouches` so we skip any touches where the user put\n      // their finger down, but used another finger to tap the element again.\n      const touches = event.changedTouches;\n      // According to the typings the touches should always be defined, but in some cases\n      // the browser appears to not assign them in tests which leads to flakes.\n      if (touches) {\n        for (let i = 0; i < touches.length; i++) {\n          this.fadeInRipple(touches[i].clientX, touches[i].clientY, this._target.rippleConfig);\n        }\n      }\n    }\n  }\n  /** Function being called whenever the trigger is being released. */\n  _onPointerUp() {\n    if (!this._isPointerDown) {\n      return;\n    }\n    this._isPointerDown = false;\n    // Fade-out all ripples that are visible and not persistent.\n    this._getActiveRipples().forEach(ripple => {\n      // By default, only ripples that are completely visible will fade out on pointer release.\n      // If the `terminateOnPointerUp` option is set, ripples that still fade in will also fade out.\n      const isVisible = ripple.state === 1 /* RippleState.VISIBLE */ || ripple.config.terminateOnPointerUp && ripple.state === 0 /* RippleState.FADING_IN */;\n      if (!ripple.config.persistent && isVisible) {\n        ripple.fadeOut();\n      }\n    });\n  }\n  _getActiveRipples() {\n    return Array.from(this._activeRipples.keys());\n  }\n  /** Removes previously registered event listeners from the trigger element. */\n  _removeTriggerEvents() {\n    const trigger = this._triggerElement;\n    if (trigger) {\n      pointerDownEvents.forEach(type => RippleRenderer._eventManager.removeHandler(type, trigger, this));\n      if (this._pointerUpEventsRegistered) {\n        pointerUpEvents.forEach(type => trigger.removeEventListener(type, this, passiveCapturingEventOptions));\n      }\n    }\n  }\n}\n/**\n * Returns the distance from the point (x, y) to the furthest corner of a rectangle.\n */\nfunction distanceToFurthestCorner(x, y, rect) {\n  const distX = Math.max(Math.abs(x - rect.left), Math.abs(x - rect.right));\n  const distY = Math.max(Math.abs(y - rect.top), Math.abs(y - rect.bottom));\n  return Math.sqrt(distX * distX + distY * distY);\n}\n\n/** Injection token that can be used to specify the global ripple options. */\nconst MAT_RIPPLE_GLOBAL_OPTIONS = new InjectionToken('mat-ripple-global-options');\nclass MatRipple {\n  /**\n   * Whether click events will not trigger the ripple. Ripples can be still launched manually\n   * by using the `launch()` method.\n   */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    if (value) {\n      this.fadeOutAllNonPersistent();\n    }\n    this._disabled = value;\n    this._setupTriggerEventsIfEnabled();\n  }\n  /**\n   * The element that triggers the ripple when click events are received.\n   * Defaults to the directive's host element.\n   */\n  get trigger() {\n    return this._trigger || this._elementRef.nativeElement;\n  }\n  set trigger(trigger) {\n    this._trigger = trigger;\n    this._setupTriggerEventsIfEnabled();\n  }\n  constructor(_elementRef, ngZone, platform, globalOptions, _animationMode) {\n    this._elementRef = _elementRef;\n    this._animationMode = _animationMode;\n    /**\n     * If set, the radius in pixels of foreground ripples when fully expanded. If unset, the radius\n     * will be the distance from the center of the ripple to the furthest corner of the host element's\n     * bounding rectangle.\n     */\n    this.radius = 0;\n    this._disabled = false;\n    /** @docs-private Whether ripple directive is initialized and the input bindings are set. */\n    this._isInitialized = false;\n    this._globalOptions = globalOptions || {};\n    this._rippleRenderer = new RippleRenderer(this, ngZone, _elementRef, platform);\n  }\n  ngOnInit() {\n    this._isInitialized = true;\n    this._setupTriggerEventsIfEnabled();\n  }\n  ngOnDestroy() {\n    this._rippleRenderer._removeTriggerEvents();\n  }\n  /** Fades out all currently showing ripple elements. */\n  fadeOutAll() {\n    this._rippleRenderer.fadeOutAll();\n  }\n  /** Fades out all currently showing non-persistent ripple elements. */\n  fadeOutAllNonPersistent() {\n    this._rippleRenderer.fadeOutAllNonPersistent();\n  }\n  /**\n   * Ripple configuration from the directive's input values.\n   * @docs-private Implemented as part of RippleTarget\n   */\n  get rippleConfig() {\n    return {\n      centered: this.centered,\n      radius: this.radius,\n      color: this.color,\n      animation: {\n        ...this._globalOptions.animation,\n        ...(this._animationMode === 'NoopAnimations' ? {\n          enterDuration: 0,\n          exitDuration: 0\n        } : {}),\n        ...this.animation\n      },\n      terminateOnPointerUp: this._globalOptions.terminateOnPointerUp\n    };\n  }\n  /**\n   * Whether ripples on pointer-down are disabled or not.\n   * @docs-private Implemented as part of RippleTarget\n   */\n  get rippleDisabled() {\n    return this.disabled || !!this._globalOptions.disabled;\n  }\n  /** Sets up the trigger event listeners if ripples are enabled. */\n  _setupTriggerEventsIfEnabled() {\n    if (!this.disabled && this._isInitialized) {\n      this._rippleRenderer.setupTriggerEvents(this.trigger);\n    }\n  }\n  /** Launches a manual ripple at the specified coordinated or just by the ripple config. */\n  launch(configOrX, y = 0, config) {\n    if (typeof configOrX === 'number') {\n      return this._rippleRenderer.fadeInRipple(configOrX, y, {\n        ...this.rippleConfig,\n        ...config\n      });\n    } else {\n      return this._rippleRenderer.fadeInRipple(0, 0, {\n        ...this.rippleConfig,\n        ...configOrX\n      });\n    }\n  }\n  static {\n    this.ɵfac = function MatRipple_Factory(t) {\n      return new (t || MatRipple)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1$1.Platform), i0.ɵɵdirectiveInject(MAT_RIPPLE_GLOBAL_OPTIONS, 8), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatRipple,\n      selectors: [[\"\", \"mat-ripple\", \"\"], [\"\", \"matRipple\", \"\"]],\n      hostAttrs: [1, \"mat-ripple\"],\n      hostVars: 2,\n      hostBindings: function MatRipple_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-ripple-unbounded\", ctx.unbounded);\n        }\n      },\n      inputs: {\n        color: [\"matRippleColor\", \"color\"],\n        unbounded: [\"matRippleUnbounded\", \"unbounded\"],\n        centered: [\"matRippleCentered\", \"centered\"],\n        radius: [\"matRippleRadius\", \"radius\"],\n        animation: [\"matRippleAnimation\", \"animation\"],\n        disabled: [\"matRippleDisabled\", \"disabled\"],\n        trigger: [\"matRippleTrigger\", \"trigger\"]\n      },\n      exportAs: [\"matRipple\"]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRipple, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-ripple], [matRipple]',\n      exportAs: 'matRipple',\n      host: {\n        'class': 'mat-ripple',\n        '[class.mat-ripple-unbounded]': 'unbounded'\n      }\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i1$1.Platform\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_RIPPLE_GLOBAL_OPTIONS]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }];\n  }, {\n    color: [{\n      type: Input,\n      args: ['matRippleColor']\n    }],\n    unbounded: [{\n      type: Input,\n      args: ['matRippleUnbounded']\n    }],\n    centered: [{\n      type: Input,\n      args: ['matRippleCentered']\n    }],\n    radius: [{\n      type: Input,\n      args: ['matRippleRadius']\n    }],\n    animation: [{\n      type: Input,\n      args: ['matRippleAnimation']\n    }],\n    disabled: [{\n      type: Input,\n      args: ['matRippleDisabled']\n    }],\n    trigger: [{\n      type: Input,\n      args: ['matRippleTrigger']\n    }]\n  });\n})();\nclass MatRippleModule {\n  static {\n    this.ɵfac = function MatRippleModule_Factory(t) {\n      return new (t || MatRippleModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatRippleModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCommonModule, MatCommonModule]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRippleModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule],\n      exports: [MatRipple, MatCommonModule],\n      declarations: [MatRipple]\n    }]\n  }], null, null);\n})();\n\n/**\n * Component that shows a simplified checkbox without including any kind of \"real\" checkbox.\n * Meant to be used when the checkbox is purely decorative and a large number of them will be\n * included, such as for the options in a multi-select. Uses no SVGs or complex animations.\n * Note that theming is meant to be handled by the parent element, e.g.\n * `mat-primary .mat-pseudo-checkbox`.\n *\n * Note that this component will be completely invisible to screen-reader users. This is *not*\n * interchangeable with `<mat-checkbox>` and should *not* be used if the user would directly\n * interact with the checkbox. The pseudo-checkbox should only be used as an implementation detail\n * of more complex components that appropriately handle selected / checked state.\n * @docs-private\n */\nclass MatPseudoCheckbox {\n  constructor(_animationMode) {\n    this._animationMode = _animationMode;\n    /** Display state of the checkbox. */\n    this.state = 'unchecked';\n    /** Whether the checkbox is disabled. */\n    this.disabled = false;\n    /**\n     * Appearance of the pseudo checkbox. Default appearance of 'full' renders a checkmark/mixedmark\n     * indicator inside a square box. 'minimal' appearance only renders the checkmark/mixedmark.\n     */\n    this.appearance = 'full';\n  }\n  static {\n    this.ɵfac = function MatPseudoCheckbox_Factory(t) {\n      return new (t || MatPseudoCheckbox)(i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatPseudoCheckbox,\n      selectors: [[\"mat-pseudo-checkbox\"]],\n      hostAttrs: [1, \"mat-pseudo-checkbox\"],\n      hostVars: 12,\n      hostBindings: function MatPseudoCheckbox_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-pseudo-checkbox-indeterminate\", ctx.state === \"indeterminate\")(\"mat-pseudo-checkbox-checked\", ctx.state === \"checked\")(\"mat-pseudo-checkbox-disabled\", ctx.disabled)(\"mat-pseudo-checkbox-minimal\", ctx.appearance === \"minimal\")(\"mat-pseudo-checkbox-full\", ctx.appearance === \"full\")(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\");\n        }\n      },\n      inputs: {\n        state: \"state\",\n        disabled: \"disabled\",\n        appearance: \"appearance\"\n      },\n      decls: 0,\n      vars: 0,\n      template: function MatPseudoCheckbox_Template(rf, ctx) {},\n      styles: [\".mat-pseudo-checkbox{border-radius:2px;cursor:pointer;display:inline-block;vertical-align:middle;box-sizing:border-box;position:relative;flex-shrink:0;transition:border-color 90ms cubic-bezier(0, 0, 0.2, 0.1),background-color 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox::after{position:absolute;opacity:0;content:\\\"\\\";border-bottom:2px solid currentColor;transition:opacity 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox._mat-animation-noopable{transition:none !important;animation:none !important}.mat-pseudo-checkbox._mat-animation-noopable::after{transition:none}.mat-pseudo-checkbox-disabled{cursor:default}.mat-pseudo-checkbox-indeterminate::after{left:1px;opacity:1;border-radius:2px}.mat-pseudo-checkbox-checked::after{left:1px;border-left:2px solid currentColor;transform:rotate(-45deg);opacity:1;box-sizing:content-box}.mat-pseudo-checkbox-full{border:2px solid}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate{border-color:rgba(0,0,0,0)}.mat-pseudo-checkbox{width:18px;height:18px}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after{width:14px;height:6px;transform-origin:center;top:-4.2426406871px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{top:8px;width:16px}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked::after{width:10px;height:4px;transform-origin:center;top:-2.8284271247px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate::after{top:6px;width:12px}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatPseudoCheckbox, [{\n    type: Component,\n    args: [{\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      selector: 'mat-pseudo-checkbox',\n      template: '',\n      host: {\n        'class': 'mat-pseudo-checkbox',\n        '[class.mat-pseudo-checkbox-indeterminate]': 'state === \"indeterminate\"',\n        '[class.mat-pseudo-checkbox-checked]': 'state === \"checked\"',\n        '[class.mat-pseudo-checkbox-disabled]': 'disabled',\n        '[class.mat-pseudo-checkbox-minimal]': 'appearance === \"minimal\"',\n        '[class.mat-pseudo-checkbox-full]': 'appearance === \"full\"',\n        '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"'\n      },\n      styles: [\".mat-pseudo-checkbox{border-radius:2px;cursor:pointer;display:inline-block;vertical-align:middle;box-sizing:border-box;position:relative;flex-shrink:0;transition:border-color 90ms cubic-bezier(0, 0, 0.2, 0.1),background-color 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox::after{position:absolute;opacity:0;content:\\\"\\\";border-bottom:2px solid currentColor;transition:opacity 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox._mat-animation-noopable{transition:none !important;animation:none !important}.mat-pseudo-checkbox._mat-animation-noopable::after{transition:none}.mat-pseudo-checkbox-disabled{cursor:default}.mat-pseudo-checkbox-indeterminate::after{left:1px;opacity:1;border-radius:2px}.mat-pseudo-checkbox-checked::after{left:1px;border-left:2px solid currentColor;transform:rotate(-45deg);opacity:1;box-sizing:content-box}.mat-pseudo-checkbox-full{border:2px solid}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate{border-color:rgba(0,0,0,0)}.mat-pseudo-checkbox{width:18px;height:18px}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after{width:14px;height:6px;transform-origin:center;top:-4.2426406871px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{top:8px;width:16px}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked::after{width:10px;height:4px;transform-origin:center;top:-2.8284271247px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate::after{top:6px;width:12px}\"]\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }];\n  }, {\n    state: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    appearance: [{\n      type: Input\n    }]\n  });\n})();\nclass MatPseudoCheckboxModule {\n  static {\n    this.ɵfac = function MatPseudoCheckboxModule_Factory(t) {\n      return new (t || MatPseudoCheckboxModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatPseudoCheckboxModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCommonModule]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatPseudoCheckboxModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule],\n      exports: [MatPseudoCheckbox],\n      declarations: [MatPseudoCheckbox]\n    }]\n  }], null, null);\n})();\n\n/**\n * Injection token used to provide the parent component to options.\n */\nconst MAT_OPTION_PARENT_COMPONENT = new InjectionToken('MAT_OPTION_PARENT_COMPONENT');\n\n// Notes on the accessibility pattern used for `mat-optgroup`.\n// The option group has two different \"modes\": regular and inert. The regular mode uses the\n// recommended a11y pattern which has `role=\"group\"` on the group element with `aria-labelledby`\n// pointing to the label. This works for `mat-select`, but it seems to hit a bug for autocomplete\n// under VoiceOver where the group doesn't get read out at all. The bug appears to be that if\n// there's __any__ a11y-related attribute on the group (e.g. `role` or `aria-labelledby`),\n// VoiceOver on Safari won't read it out.\n// We've introduced the `inert` mode as a workaround. Under this mode, all a11y attributes are\n// removed from the group, and we get the screen reader to read out the group label by mirroring it\n// inside an invisible element in the option. This is sub-optimal, because the screen reader will\n// repeat the group label on each navigation, whereas the default pattern only reads the group when\n// the user enters a new group. The following alternate approaches were considered:\n// 1. Reading out the group label using the `LiveAnnouncer` solves the problem, but we can't control\n//    when the text will be read out so sometimes it comes in too late or never if the user\n//    navigates quickly.\n// 2. `<mat-option aria-describedby=\"groupLabel\"` - This works on Safari, but VoiceOver in Chrome\n//    won't read out the description at all.\n// 3. `<mat-option aria-labelledby=\"optionLabel groupLabel\"` - This works on Chrome, but Safari\n//     doesn't read out the text at all. Furthermore, on\n// Boilerplate for applying mixins to MatOptgroup.\n/** @docs-private */\nconst _MatOptgroupMixinBase = mixinDisabled(class {});\n// Counter for unique group ids.\nlet _uniqueOptgroupIdCounter = 0;\nclass _MatOptgroupBase extends _MatOptgroupMixinBase {\n  constructor(parent) {\n    super();\n    /** Unique id for the underlying label. */\n    this._labelId = `mat-optgroup-label-${_uniqueOptgroupIdCounter++}`;\n    this._inert = parent?.inertGroups ?? false;\n  }\n  static {\n    this.ɵfac = function _MatOptgroupBase_Factory(t) {\n      return new (t || _MatOptgroupBase)(i0.ɵɵdirectiveInject(MAT_OPTION_PARENT_COMPONENT, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: _MatOptgroupBase,\n      inputs: {\n        label: \"label\"\n      },\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatOptgroupBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_OPTION_PARENT_COMPONENT]\n      }, {\n        type: Optional\n      }]\n    }];\n  }, {\n    label: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * Injection token that can be used to reference instances of `MatOptgroup`. It serves as\n * alternative token to the actual `MatOptgroup` class which could cause unnecessary\n * retention of the class and its component metadata.\n */\nconst MAT_OPTGROUP = new InjectionToken('MatOptgroup');\n/**\n * Component that is used to group instances of `mat-option`.\n */\nclass MatOptgroup extends _MatOptgroupBase {\n  static {\n    this.ɵfac = /* @__PURE__ */function () {\n      let ɵMatOptgroup_BaseFactory;\n      return function MatOptgroup_Factory(t) {\n        return (ɵMatOptgroup_BaseFactory || (ɵMatOptgroup_BaseFactory = i0.ɵɵgetInheritedFactory(MatOptgroup)))(t || MatOptgroup);\n      };\n    }();\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatOptgroup,\n      selectors: [[\"mat-optgroup\"]],\n      hostAttrs: [1, \"mat-mdc-optgroup\"],\n      hostVars: 3,\n      hostBindings: function MatOptgroup_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"role\", ctx._inert ? null : \"group\")(\"aria-disabled\", ctx._inert ? null : ctx.disabled.toString())(\"aria-labelledby\", ctx._inert ? null : ctx._labelId);\n        }\n      },\n      inputs: {\n        disabled: \"disabled\"\n      },\n      exportAs: [\"matOptgroup\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_OPTGROUP,\n        useExisting: MatOptgroup\n      }]), i0.ɵɵInheritDefinitionFeature],\n      ngContentSelectors: _c1,\n      decls: 5,\n      vars: 4,\n      consts: [[\"role\", \"presentation\", 1, \"mat-mdc-optgroup-label\", 3, \"id\"], [1, \"mdc-list-item__primary-text\"]],\n      template: function MatOptgroup_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c0);\n          i0.ɵɵelementStart(0, \"span\", 0)(1, \"span\", 1);\n          i0.ɵɵtext(2);\n          i0.ɵɵprojection(3);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵprojection(4, 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mdc-list-item--disabled\", ctx.disabled);\n          i0.ɵɵproperty(\"id\", ctx._labelId);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\"\", ctx.label, \" \");\n        }\n      },\n      styles: [\".mat-mdc-optgroup{color:var(--mat-optgroup-label-text-color);font-family:var(--mat-optgroup-label-text-font);line-height:var(--mat-optgroup-label-text-line-height);font-size:var(--mat-optgroup-label-text-size);letter-spacing:var(--mat-optgroup-label-text-tracking);font-weight:var(--mat-optgroup-label-text-weight)}.mat-mdc-optgroup-label{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:16px;padding-right:16px;min-height:48px}.mat-mdc-optgroup-label:focus{outline:none}[dir=rtl] .mat-mdc-optgroup-label,.mat-mdc-optgroup-label[dir=rtl]{padding-left:16px;padding-right:16px}.mat-mdc-optgroup-label.mdc-list-item--disabled{opacity:.38}.mat-mdc-optgroup-label .mdc-list-item__primary-text{font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;white-space:normal}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatOptgroup, [{\n    type: Component,\n    args: [{\n      selector: 'mat-optgroup',\n      exportAs: 'matOptgroup',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      inputs: ['disabled'],\n      host: {\n        'class': 'mat-mdc-optgroup',\n        '[attr.role]': '_inert ? null : \"group\"',\n        '[attr.aria-disabled]': '_inert ? null : disabled.toString()',\n        '[attr.aria-labelledby]': '_inert ? null : _labelId'\n      },\n      providers: [{\n        provide: MAT_OPTGROUP,\n        useExisting: MatOptgroup\n      }],\n      template: \"<span\\n  class=\\\"mat-mdc-optgroup-label\\\"\\n  role=\\\"presentation\\\"\\n  [class.mdc-list-item--disabled]=\\\"disabled\\\"\\n  [id]=\\\"_labelId\\\">\\n  <span class=\\\"mdc-list-item__primary-text\\\">{{ label }} <ng-content></ng-content></span>\\n</span>\\n\\n<ng-content select=\\\"mat-option, ng-container\\\"></ng-content>\\n\",\n      styles: [\".mat-mdc-optgroup{color:var(--mat-optgroup-label-text-color);font-family:var(--mat-optgroup-label-text-font);line-height:var(--mat-optgroup-label-text-line-height);font-size:var(--mat-optgroup-label-text-size);letter-spacing:var(--mat-optgroup-label-text-tracking);font-weight:var(--mat-optgroup-label-text-weight)}.mat-mdc-optgroup-label{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:16px;padding-right:16px;min-height:48px}.mat-mdc-optgroup-label:focus{outline:none}[dir=rtl] .mat-mdc-optgroup-label,.mat-mdc-optgroup-label[dir=rtl]{padding-left:16px;padding-right:16px}.mat-mdc-optgroup-label.mdc-list-item--disabled{opacity:.38}.mat-mdc-optgroup-label .mdc-list-item__primary-text{font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;white-space:normal}\"]\n    }]\n  }], null, null);\n})();\n\n/**\n * Option IDs need to be unique across components, so this counter exists outside of\n * the component definition.\n */\nlet _uniqueIdCounter = 0;\n/** Event object emitted by MatOption when selected or deselected. */\nclass MatOptionSelectionChange {\n  constructor( /** Reference to the option that emitted the event. */\n  source, /** Whether the change in the option's value was a result of a user action. */\n  isUserInput = false) {\n    this.source = source;\n    this.isUserInput = isUserInput;\n  }\n}\nclass _MatOptionBase {\n  /** Whether the wrapping component is in multiple selection mode. */\n  get multiple() {\n    return this._parent && this._parent.multiple;\n  }\n  /** Whether or not the option is currently selected. */\n  get selected() {\n    return this._selected;\n  }\n  /** Whether the option is disabled. */\n  get disabled() {\n    return this.group && this.group.disabled || this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = coerceBooleanProperty(value);\n  }\n  /** Whether ripples for the option are disabled. */\n  get disableRipple() {\n    return !!(this._parent && this._parent.disableRipple);\n  }\n  /** Whether to display checkmark for single-selection. */\n  get hideSingleSelectionIndicator() {\n    return !!(this._parent && this._parent.hideSingleSelectionIndicator);\n  }\n  constructor(_element, _changeDetectorRef, _parent, group) {\n    this._element = _element;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._parent = _parent;\n    this.group = group;\n    this._selected = false;\n    this._active = false;\n    this._disabled = false;\n    this._mostRecentViewValue = '';\n    /** The unique ID of the option. */\n    this.id = `mat-option-${_uniqueIdCounter++}`;\n    /** Event emitted when the option is selected or deselected. */\n    // tslint:disable-next-line:no-output-on-prefix\n    this.onSelectionChange = new EventEmitter();\n    /** Emits when the state of the option changes and any parents have to be notified. */\n    this._stateChanges = new Subject();\n  }\n  /**\n   * Whether or not the option is currently active and ready to be selected.\n   * An active option displays styles as if it is focused, but the\n   * focus is actually retained somewhere else. This comes in handy\n   * for components like autocomplete where focus must remain on the input.\n   */\n  get active() {\n    return this._active;\n  }\n  /**\n   * The displayed value of the option. It is necessary to show the selected option in the\n   * select's trigger.\n   */\n  get viewValue() {\n    // TODO(kara): Add input property alternative for node envs.\n    return (this._text?.nativeElement.textContent || '').trim();\n  }\n  /** Selects the option. */\n  select(emitEvent = true) {\n    if (!this._selected) {\n      this._selected = true;\n      this._changeDetectorRef.markForCheck();\n      if (emitEvent) {\n        this._emitSelectionChangeEvent();\n      }\n    }\n  }\n  /** Deselects the option. */\n  deselect(emitEvent = true) {\n    if (this._selected) {\n      this._selected = false;\n      this._changeDetectorRef.markForCheck();\n      if (emitEvent) {\n        this._emitSelectionChangeEvent();\n      }\n    }\n  }\n  /** Sets focus onto this option. */\n  focus(_origin, options) {\n    // Note that we aren't using `_origin`, but we need to keep it because some internal consumers\n    // use `MatOption` in a `FocusKeyManager` and we need it to match `FocusableOption`.\n    const element = this._getHostElement();\n    if (typeof element.focus === 'function') {\n      element.focus(options);\n    }\n  }\n  /**\n   * This method sets display styles on the option to make it appear\n   * active. This is used by the ActiveDescendantKeyManager so key\n   * events will display the proper options as active on arrow key events.\n   */\n  setActiveStyles() {\n    if (!this._active) {\n      this._active = true;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /**\n   * This method removes display styles on the option that made it appear\n   * active. This is used by the ActiveDescendantKeyManager so key\n   * events will display the proper options as active on arrow key events.\n   */\n  setInactiveStyles() {\n    if (this._active) {\n      this._active = false;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /** Gets the label to be used when determining whether the option should be focused. */\n  getLabel() {\n    return this.viewValue;\n  }\n  /** Ensures the option is selected when activated from the keyboard. */\n  _handleKeydown(event) {\n    if ((event.keyCode === ENTER || event.keyCode === SPACE) && !hasModifierKey(event)) {\n      this._selectViaInteraction();\n      // Prevent the page from scrolling down and form submits.\n      event.preventDefault();\n    }\n  }\n  /**\n   * `Selects the option while indicating the selection came from the user. Used to\n   * determine if the select's view -> model callback should be invoked.`\n   */\n  _selectViaInteraction() {\n    if (!this.disabled) {\n      this._selected = this.multiple ? !this._selected : true;\n      this._changeDetectorRef.markForCheck();\n      this._emitSelectionChangeEvent(true);\n    }\n  }\n  /** Returns the correct tabindex for the option depending on disabled state. */\n  // This method is only used by `MatLegacyOption`. Keeping it here to avoid breaking the types.\n  // That's because `MatLegacyOption` use `MatOption` type in a few places such as\n  // `MatOptionSelectionChange`. It is safe to delete this when `MatLegacyOption` is deleted.\n  _getTabIndex() {\n    return this.disabled ? '-1' : '0';\n  }\n  /** Gets the host DOM element. */\n  _getHostElement() {\n    return this._element.nativeElement;\n  }\n  ngAfterViewChecked() {\n    // Since parent components could be using the option's label to display the selected values\n    // (e.g. `mat-select`) and they don't have a way of knowing if the option's label has changed\n    // we have to check for changes in the DOM ourselves and dispatch an event. These checks are\n    // relatively cheap, however we still limit them only to selected options in order to avoid\n    // hitting the DOM too often.\n    if (this._selected) {\n      const viewValue = this.viewValue;\n      if (viewValue !== this._mostRecentViewValue) {\n        if (this._mostRecentViewValue) {\n          this._stateChanges.next();\n        }\n        this._mostRecentViewValue = viewValue;\n      }\n    }\n  }\n  ngOnDestroy() {\n    this._stateChanges.complete();\n  }\n  /** Emits the selection change event. */\n  _emitSelectionChangeEvent(isUserInput = false) {\n    this.onSelectionChange.emit(new MatOptionSelectionChange(this, isUserInput));\n  }\n  static {\n    this.ɵfac = function _MatOptionBase_Factory(t) {\n      i0.ɵɵinvalidFactory();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: _MatOptionBase,\n      viewQuery: function _MatOptionBase_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c2, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._text = _t.first);\n        }\n      },\n      inputs: {\n        value: \"value\",\n        id: \"id\",\n        disabled: \"disabled\"\n      },\n      outputs: {\n        onSelectionChange: \"onSelectionChange\"\n      }\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatOptionBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: undefined\n    }, {\n      type: _MatOptgroupBase\n    }];\n  }, {\n    value: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    onSelectionChange: [{\n      type: Output\n    }],\n    _text: [{\n      type: ViewChild,\n      args: ['text', {\n        static: true\n      }]\n    }]\n  });\n})();\n/**\n * Single option inside of a `<mat-select>` element.\n */\nclass MatOption extends _MatOptionBase {\n  constructor(element, changeDetectorRef, parent, group) {\n    super(element, changeDetectorRef, parent, group);\n  }\n  static {\n    this.ɵfac = function MatOption_Factory(t) {\n      return new (t || MatOption)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MAT_OPTION_PARENT_COMPONENT, 8), i0.ɵɵdirectiveInject(MAT_OPTGROUP, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatOption,\n      selectors: [[\"mat-option\"]],\n      hostAttrs: [\"role\", \"option\", 1, \"mat-mdc-option\", \"mdc-list-item\"],\n      hostVars: 11,\n      hostBindings: function MatOption_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function MatOption_click_HostBindingHandler() {\n            return ctx._selectViaInteraction();\n          })(\"keydown\", function MatOption_keydown_HostBindingHandler($event) {\n            return ctx._handleKeydown($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"id\", ctx.id);\n          i0.ɵɵattribute(\"aria-selected\", ctx.selected)(\"aria-disabled\", ctx.disabled.toString());\n          i0.ɵɵclassProp(\"mdc-list-item--selected\", ctx.selected)(\"mat-mdc-option-multiple\", ctx.multiple)(\"mat-mdc-option-active\", ctx.active)(\"mdc-list-item--disabled\", ctx.disabled);\n        }\n      },\n      exportAs: [\"matOption\"],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      ngContentSelectors: _c4,\n      decls: 8,\n      vars: 5,\n      consts: [[\"class\", \"mat-mdc-option-pseudo-checkbox\", \"aria-hidden\", \"true\", 3, \"disabled\", \"state\", 4, \"ngIf\"], [1, \"mdc-list-item__primary-text\"], [\"text\", \"\"], [\"class\", \"mat-mdc-option-pseudo-checkbox\", \"state\", \"checked\", \"aria-hidden\", \"true\", \"appearance\", \"minimal\", 3, \"disabled\", 4, \"ngIf\"], [\"class\", \"cdk-visually-hidden\", 4, \"ngIf\"], [\"aria-hidden\", \"true\", \"mat-ripple\", \"\", 1, \"mat-mdc-option-ripple\", \"mat-mdc-focus-indicator\", 3, \"matRippleTrigger\", \"matRippleDisabled\"], [\"aria-hidden\", \"true\", 1, \"mat-mdc-option-pseudo-checkbox\", 3, \"disabled\", \"state\"], [\"state\", \"checked\", \"aria-hidden\", \"true\", \"appearance\", \"minimal\", 1, \"mat-mdc-option-pseudo-checkbox\", 3, \"disabled\"], [1, \"cdk-visually-hidden\"]],\n      template: function MatOption_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c3);\n          i0.ɵɵtemplate(0, MatOption_mat_pseudo_checkbox_0_Template, 1, 2, \"mat-pseudo-checkbox\", 0);\n          i0.ɵɵprojection(1);\n          i0.ɵɵelementStart(2, \"span\", 1, 2);\n          i0.ɵɵprojection(4, 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(5, MatOption_mat_pseudo_checkbox_5_Template, 1, 1, \"mat-pseudo-checkbox\", 3);\n          i0.ɵɵtemplate(6, MatOption_span_6_Template, 2, 1, \"span\", 4);\n          i0.ɵɵelement(7, \"div\", 5);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.multiple);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", !ctx.multiple && ctx.selected && !ctx.hideSingleSelectionIndicator);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.group && ctx.group._inert);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"matRippleTrigger\", ctx._getHostElement())(\"matRippleDisabled\", ctx.disabled || ctx.disableRipple);\n        }\n      },\n      dependencies: [MatRipple, i3.NgIf, MatPseudoCheckbox],\n      styles: [\".mat-mdc-option{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:16px;padding-right:16px;-webkit-user-select:none;user-select:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;cursor:pointer;-webkit-tap-highlight-color:rgba(0,0,0,0);color:var(--mat-option-label-text-color);font-family:var(--mat-option-label-text-font);line-height:var(--mat-option-label-text-line-height);font-size:var(--mat-option-label-text-size);letter-spacing:var(--mat-option-label-text-tracking);font-weight:var(--mat-option-label-text-weight);min-height:48px}.mat-mdc-option:focus{outline:none}[dir=rtl] .mat-mdc-option,.mat-mdc-option[dir=rtl]{padding-left:16px;padding-right:16px}.mat-mdc-option:hover:not(.mdc-list-item--disabled){background-color:var(--mat-option-hover-state-layer-color)}.mat-mdc-option:focus.mdc-list-item,.mat-mdc-option.mat-mdc-option-active.mdc-list-item{background-color:var(--mat-option-focus-state-layer-color)}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled) .mdc-list-item__primary-text{color:var(--mat-option-selected-state-label-text-color)}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-mdc-option-multiple){background-color:var(--mat-option-selected-state-layer-color)}.mat-mdc-option.mdc-list-item{align-items:center}.mat-mdc-option.mdc-list-item--disabled{cursor:default;pointer-events:none}.mat-mdc-option.mdc-list-item--disabled .mat-mdc-option-pseudo-checkbox,.mat-mdc-option.mdc-list-item--disabled .mdc-list-item__primary-text,.mat-mdc-option.mdc-list-item--disabled>mat-icon{opacity:.38}.mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:32px}[dir=rtl] .mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:16px;padding-right:32px}.mat-mdc-option .mat-icon,.mat-mdc-option .mat-pseudo-checkbox-full{margin-right:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-icon,[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-full{margin-right:0;margin-left:16px}.mat-mdc-option .mat-pseudo-checkbox-minimal{margin-left:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-minimal{margin-right:16px;margin-left:0}.mat-mdc-option .mat-mdc-option-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-option .mdc-list-item__primary-text{white-space:normal;font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;margin-right:auto}[dir=rtl] .mat-mdc-option .mdc-list-item__primary-text{margin-right:0;margin-left:auto}.cdk-high-contrast-active .mat-mdc-option.mdc-list-item--selected:not(.mat-mdc-option-multiple)::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}[dir=rtl] .cdk-high-contrast-active .mat-mdc-option.mdc-list-item--selected:not(.mat-mdc-option-multiple)::after{right:auto;left:16px}.mat-mdc-option-active .mat-mdc-focus-indicator::before{content:\\\"\\\"}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatOption, [{\n    type: Component,\n    args: [{\n      selector: 'mat-option',\n      exportAs: 'matOption',\n      host: {\n        'role': 'option',\n        '[class.mdc-list-item--selected]': 'selected',\n        '[class.mat-mdc-option-multiple]': 'multiple',\n        '[class.mat-mdc-option-active]': 'active',\n        '[class.mdc-list-item--disabled]': 'disabled',\n        '[id]': 'id',\n        // Set aria-selected to false for non-selected items and true for selected items. Conform to\n        // [WAI ARIA Listbox authoring practices guide](\n        //  https://www.w3.org/WAI/ARIA/apg/patterns/listbox/), \"If any options are selected, each\n        // selected option has either aria-selected or aria-checked  set to true. All options that are\n        // selectable but not selected have either aria-selected or aria-checked set to false.\" Align\n        // aria-selected implementation of Chips and List components.\n        //\n        // Set `aria-selected=\"false\"` on not-selected listbox options to fix VoiceOver announcing\n        // every option as \"selected\" (#21491).\n        '[attr.aria-selected]': 'selected',\n        '[attr.aria-disabled]': 'disabled.toString()',\n        '(click)': '_selectViaInteraction()',\n        '(keydown)': '_handleKeydown($event)',\n        'class': 'mat-mdc-option mdc-list-item'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<!-- Set aria-hidden=\\\"true\\\" to this DOM node and other decorative nodes in this file. This might\\n be contributing to issue where sometimes VoiceOver focuses on a TextNode in the a11y tree instead\\n of the Option node (#23202). Most assistive technology will generally ignore non-role,\\n non-text-content elements. Adding aria-hidden seems to make VoiceOver behave more consistently. -->\\n<mat-pseudo-checkbox *ngIf=\\\"multiple\\\" class=\\\"mat-mdc-option-pseudo-checkbox\\\" [disabled]=\\\"disabled\\\"\\n    [state]=\\\"selected ? 'checked' : 'unchecked'\\\" aria-hidden=\\\"true\\\"></mat-pseudo-checkbox>\\n\\n<ng-content select=\\\"mat-icon\\\"></ng-content>\\n\\n<span class=\\\"mdc-list-item__primary-text\\\" #text><ng-content></ng-content></span>\\n\\n<!-- Render checkmark at the end for single-selection. -->\\n<mat-pseudo-checkbox *ngIf=\\\"!multiple && selected && !hideSingleSelectionIndicator\\\"\\n    class=\\\"mat-mdc-option-pseudo-checkbox\\\" [disabled]=\\\"disabled\\\" state=\\\"checked\\\"\\n    aria-hidden=\\\"true\\\" appearance=\\\"minimal\\\"></mat-pseudo-checkbox>\\n\\n<!-- See a11y notes inside optgroup.ts for context behind this element. -->\\n<span class=\\\"cdk-visually-hidden\\\" *ngIf=\\\"group && group._inert\\\">({{ group.label }})</span>\\n\\n<div class=\\\"mat-mdc-option-ripple mat-mdc-focus-indicator\\\" aria-hidden=\\\"true\\\" mat-ripple\\n     [matRippleTrigger]=\\\"_getHostElement()\\\" [matRippleDisabled]=\\\"disabled || disableRipple\\\">\\n</div>\\n\",\n      styles: [\".mat-mdc-option{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:16px;padding-right:16px;-webkit-user-select:none;user-select:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;cursor:pointer;-webkit-tap-highlight-color:rgba(0,0,0,0);color:var(--mat-option-label-text-color);font-family:var(--mat-option-label-text-font);line-height:var(--mat-option-label-text-line-height);font-size:var(--mat-option-label-text-size);letter-spacing:var(--mat-option-label-text-tracking);font-weight:var(--mat-option-label-text-weight);min-height:48px}.mat-mdc-option:focus{outline:none}[dir=rtl] .mat-mdc-option,.mat-mdc-option[dir=rtl]{padding-left:16px;padding-right:16px}.mat-mdc-option:hover:not(.mdc-list-item--disabled){background-color:var(--mat-option-hover-state-layer-color)}.mat-mdc-option:focus.mdc-list-item,.mat-mdc-option.mat-mdc-option-active.mdc-list-item{background-color:var(--mat-option-focus-state-layer-color)}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled) .mdc-list-item__primary-text{color:var(--mat-option-selected-state-label-text-color)}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-mdc-option-multiple){background-color:var(--mat-option-selected-state-layer-color)}.mat-mdc-option.mdc-list-item{align-items:center}.mat-mdc-option.mdc-list-item--disabled{cursor:default;pointer-events:none}.mat-mdc-option.mdc-list-item--disabled .mat-mdc-option-pseudo-checkbox,.mat-mdc-option.mdc-list-item--disabled .mdc-list-item__primary-text,.mat-mdc-option.mdc-list-item--disabled>mat-icon{opacity:.38}.mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:32px}[dir=rtl] .mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:16px;padding-right:32px}.mat-mdc-option .mat-icon,.mat-mdc-option .mat-pseudo-checkbox-full{margin-right:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-icon,[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-full{margin-right:0;margin-left:16px}.mat-mdc-option .mat-pseudo-checkbox-minimal{margin-left:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-minimal{margin-right:16px;margin-left:0}.mat-mdc-option .mat-mdc-option-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-option .mdc-list-item__primary-text{white-space:normal;font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;margin-right:auto}[dir=rtl] .mat-mdc-option .mdc-list-item__primary-text{margin-right:0;margin-left:auto}.cdk-high-contrast-active .mat-mdc-option.mdc-list-item--selected:not(.mat-mdc-option-multiple)::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}[dir=rtl] .cdk-high-contrast-active .mat-mdc-option.mdc-list-item--selected:not(.mat-mdc-option-multiple)::after{right:auto;left:16px}.mat-mdc-option-active .mat-mdc-focus-indicator::before{content:\\\"\\\"}\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_OPTION_PARENT_COMPONENT]\n      }]\n    }, {\n      type: MatOptgroup,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_OPTGROUP]\n      }]\n    }];\n  }, null);\n})();\n/**\n * Counts the amount of option group labels that precede the specified option.\n * @param optionIndex Index of the option at which to start counting.\n * @param options Flat list of all of the options.\n * @param optionGroups Flat list of all of the option groups.\n * @docs-private\n */\nfunction _countGroupLabelsBeforeOption(optionIndex, options, optionGroups) {\n  if (optionGroups.length) {\n    let optionsArray = options.toArray();\n    let groups = optionGroups.toArray();\n    let groupCounter = 0;\n    for (let i = 0; i < optionIndex + 1; i++) {\n      if (optionsArray[i].group && optionsArray[i].group === groups[groupCounter]) {\n        groupCounter++;\n      }\n    }\n    return groupCounter;\n  }\n  return 0;\n}\n/**\n * Determines the position to which to scroll a panel in order for an option to be into view.\n * @param optionOffset Offset of the option from the top of the panel.\n * @param optionHeight Height of the options.\n * @param currentScrollPosition Current scroll position of the panel.\n * @param panelHeight Height of the panel.\n * @docs-private\n */\nfunction _getOptionScrollPosition(optionOffset, optionHeight, currentScrollPosition, panelHeight) {\n  if (optionOffset < currentScrollPosition) {\n    return optionOffset;\n  }\n  if (optionOffset + optionHeight > currentScrollPosition + panelHeight) {\n    return Math.max(0, optionOffset - panelHeight + optionHeight);\n  }\n  return currentScrollPosition;\n}\nclass MatOptionModule {\n  static {\n    this.ɵfac = function MatOptionModule_Factory(t) {\n      return new (t || MatOptionModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatOptionModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatRippleModule, CommonModule, MatCommonModule, MatPseudoCheckboxModule]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatOptionModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatRippleModule, CommonModule, MatCommonModule, MatPseudoCheckboxModule],\n      exports: [MatOption, MatOptgroup],\n      declarations: [MatOption, MatOptgroup]\n    }]\n  }], null, null);\n})();\n\n/** The options for the MatRippleLoader's event listeners. */\nconst eventListenerOptions = {\n  capture: true\n};\n/** The events that should trigger the initialization of the ripple. */\nconst rippleInteractionEvents = ['focus', 'click', 'mouseenter', 'touchstart'];\n/** The attribute attached to a component whose ripple has not yet been initialized. */\nconst matRippleUninitialized = 'mat-ripple-loader-uninitialized';\n/** Additional classes that should be added to the ripple when it is rendered. */\nconst matRippleClassName = 'mat-ripple-loader-class-name';\n/** Whether the ripple should be centered. */\nconst matRippleCentered = 'mat-ripple-loader-centered';\n/** Whether the ripple should be disabled. */\nconst matRippleDisabled = 'mat-ripple-loader-disabled';\n/**\n * Handles attaching ripples on demand.\n *\n * This service allows us to avoid eagerly creating & attaching MatRipples.\n * It works by creating & attaching a ripple only when a component is first interacted with.\n *\n * @docs-private\n */\nclass MatRippleLoader {\n  constructor() {\n    this._document = inject(DOCUMENT, {\n      optional: true\n    });\n    this._animationMode = inject(ANIMATION_MODULE_TYPE$1, {\n      optional: true\n    });\n    this._globalRippleOptions = inject(MAT_RIPPLE_GLOBAL_OPTIONS, {\n      optional: true\n    });\n    this._platform = inject(Platform);\n    this._ngZone = inject(NgZone);\n    this._hosts = new Map();\n    /** Handles creating and attaching component internals when a component it is initially interacted with. */\n    this._onInteraction = event => {\n      if (!(event.target instanceof HTMLElement)) {\n        return;\n      }\n      const eventTarget = event.target;\n      // TODO(wagnermaciel): Consider batching these events to improve runtime performance.\n      const element = eventTarget.closest(`[${matRippleUninitialized}]`);\n      if (element) {\n        this._createRipple(element);\n      }\n    };\n    this._ngZone.runOutsideAngular(() => {\n      for (const event of rippleInteractionEvents) {\n        this._document?.addEventListener(event, this._onInteraction, eventListenerOptions);\n      }\n    });\n  }\n  ngOnDestroy() {\n    const hosts = this._hosts.keys();\n    for (const host of hosts) {\n      this.destroyRipple(host);\n    }\n    for (const event of rippleInteractionEvents) {\n      this._document?.removeEventListener(event, this._onInteraction, eventListenerOptions);\n    }\n  }\n  /**\n   * Configures the ripple that will be rendered by the ripple loader.\n   *\n   * Stores the given information about how the ripple should be configured on the host\n   * element so that it can later be retrived & used when the ripple is actually created.\n   */\n  configureRipple(host, config) {\n    // Indicates that the ripple has not yet been rendered for this component.\n    host.setAttribute(matRippleUninitialized, '');\n    // Store the additional class name(s) that should be added to the ripple element.\n    if (config.className || !host.hasAttribute(matRippleClassName)) {\n      host.setAttribute(matRippleClassName, config.className || '');\n    }\n    // Store whether the ripple should be centered.\n    if (config.centered) {\n      host.setAttribute(matRippleCentered, '');\n    }\n    if (config.disabled) {\n      host.setAttribute(matRippleDisabled, '');\n    }\n  }\n  /** Returns the ripple instance for the given host element. */\n  getRipple(host) {\n    const ripple = this._hosts.get(host);\n    return ripple || this._createRipple(host);\n  }\n  /** Sets the disabled state on the ripple instance corresponding to the given host element. */\n  setDisabled(host, disabled) {\n    const ripple = this._hosts.get(host);\n    // If the ripple has already been instantiated, just disable it.\n    if (ripple) {\n      ripple.disabled = disabled;\n      return;\n    }\n    // Otherwise, set an attribute so we know what the\n    // disabled state should be when the ripple is initialized.\n    if (disabled) {\n      host.setAttribute(matRippleDisabled, '');\n    } else {\n      host.removeAttribute(matRippleDisabled);\n    }\n  }\n  /** Creates a MatRipple and appends it to the given element. */\n  _createRipple(host) {\n    if (!this._document) {\n      return;\n    }\n    const existingRipple = this._hosts.get(host);\n    if (existingRipple) {\n      return existingRipple;\n    }\n    // Create the ripple element.\n    host.querySelector('.mat-ripple')?.remove();\n    const rippleEl = this._document.createElement('span');\n    rippleEl.classList.add('mat-ripple', host.getAttribute(matRippleClassName));\n    host.append(rippleEl);\n    // Create the MatRipple.\n    const ripple = new MatRipple(new ElementRef(rippleEl), this._ngZone, this._platform, this._globalRippleOptions ? this._globalRippleOptions : undefined, this._animationMode ? this._animationMode : undefined);\n    ripple._isInitialized = true;\n    ripple.trigger = host;\n    ripple.centered = host.hasAttribute(matRippleCentered);\n    ripple.disabled = host.hasAttribute(matRippleDisabled);\n    this.attachRipple(host, ripple);\n    return ripple;\n  }\n  attachRipple(host, ripple) {\n    host.removeAttribute(matRippleUninitialized);\n    this._hosts.set(host, ripple);\n  }\n  destroyRipple(host) {\n    const ripple = this._hosts.get(host);\n    if (ripple) {\n      // Since this directive is created manually, it needs to be destroyed manually too.\n      // tslint:disable-next-line:no-lifecycle-invocation\n      ripple.ngOnDestroy();\n      this._hosts.delete(host);\n    }\n  }\n  static {\n    this.ɵfac = function MatRippleLoader_Factory(t) {\n      return new (t || MatRippleLoader)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: MatRippleLoader,\n      factory: MatRippleLoader.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRippleLoader, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [];\n  }, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AnimationCurves, AnimationDurations, DateAdapter, ErrorStateMatcher, MATERIAL_SANITY_CHECKS, MAT_DATE_FORMATS, MAT_DATE_LOCALE, MAT_DATE_LOCALE_FACTORY, MAT_NATIVE_DATE_FORMATS, MAT_OPTGROUP, MAT_OPTION_PARENT_COMPONENT, MAT_RIPPLE_GLOBAL_OPTIONS, MatCommonModule, MatLine, MatLineModule, MatNativeDateModule, MatOptgroup, MatOption, MatOptionModule, MatOptionSelectionChange, MatPseudoCheckbox, MatPseudoCheckboxModule, MatRipple, MatRippleLoader, MatRippleModule, NativeDateAdapter, NativeDateModule, RippleRef, RippleRenderer, ShowOnDirtyErrorStateMatcher, VERSION, _MatOptgroupBase, _MatOptionBase, _countGroupLabelsBeforeOption, _getOptionScrollPosition, defaultRippleAnimationConfig, mixinColor, mixinDisableRipple, mixinDisabled, mixinErrorState, mixinInitialized, mixinTabIndex, setLines };", "map": {"version": 3, "names": ["i0", "Version", "InjectionToken", "inject", "NgModule", "Optional", "Inject", "LOCALE_ID", "Injectable", "Directive", "Input", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "EventEmitter", "Output", "ViewChild", "ANIMATION_MODULE_TYPE", "ANIMATION_MODULE_TYPE$1", "NgZone", "ElementRef", "i1", "isFakeMousedownFromScreenReader", "isFakeTouchstartFromScreenReader", "BidiModule", "VERSION", "VERSION$1", "i3", "DOCUMENT", "CommonModule", "i1$1", "Platform", "_isTestEnvironment", "normalizePassiveListenerOptions", "_getEventTarget", "coerceBooleanProperty", "coerceNumberProperty", "coerceElement", "Observable", "Subject", "startWith", "ENTER", "SPACE", "hasModifierKey", "_c0", "_c1", "_c2", "MatOption_mat_pseudo_checkbox_0_Template", "rf", "ctx", "ɵɵelement", "ctx_r0", "ɵɵnextContext", "ɵɵproperty", "disabled", "selected", "MatOption_mat_pseudo_checkbox_5_Template", "ctx_r2", "MatOption_span_6_Template", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ctx_r3", "ɵɵadvance", "ɵɵtextInterpolate1", "group", "label", "_c3", "_c4", "AnimationCurves", "STANDARD_CURVE", "DECELERATION_CURVE", "ACCELERATION_CURVE", "SHARP_CURVE", "AnimationDurations", "COMPLEX", "ENTERING", "EXITING", "MATERIAL_SANITY_CHECKS_FACTORY", "MATERIAL_SANITY_CHECKS", "providedIn", "factory", "MatCommonModule", "constructor", "highContrastModeDetector", "_<PERSON><PERSON><PERSON><PERSON>", "_document", "_hasDoneGlobalChecks", "_applyBodyHighContrastModeCssClasses", "ngDevMode", "platform", "optional", "_checkIsEnabled", "_checkDoctypeIsDefined", "_checkThemeIsPresent", "<PERSON><PERSON><PERSON><PERSON>", "_checkCdkVersionMatch", "name", "ɵfac", "MatCommonModule_Factory", "t", "ɵɵinject", "HighContrastModeDetector", "ɵmod", "ɵɵdefineNgModule", "type", "ɵinj", "ɵɵdefineInjector", "imports", "ɵsetClassMetadata", "args", "exports", "undefined", "decorators", "Document", "doc", "doctype", "console", "warn", "body", "testElement", "createElement", "classList", "add", "append<PERSON><PERSON><PERSON>", "computedStyle", "getComputedStyle", "display", "remove", "full", "mixinDisabled", "base", "_disabled", "value", "mixinColor", "defaultColor", "color", "_color", "colorPalette", "_elementRef", "nativeElement", "mixinDisableRipple", "disable<PERSON><PERSON><PERSON>", "_disableRipple", "mixinTabIndex", "defaultTabIndex", "tabIndex", "_tabIndex", "mixinErrorState", "updateErrorState", "oldState", "errorState", "parent", "_parentFormGroup", "_parentForm", "matcher", "errorStateMatcher", "_defaultErrorStateMatcher", "control", "ngControl", "newState", "isErrorState", "stateChanges", "next", "mixinInitialized", "_isInitialized", "_pendingSubscribers", "initialized", "subscriber", "_notifySubscriber", "push", "_markInitialized", "Error", "for<PERSON>ach", "complete", "MAT_DATE_LOCALE", "MAT_DATE_LOCALE_FACTORY", "DateAdapter", "_localeChanges", "localeChanges", "getValidDateOrNull", "obj", "isDateInstance", "<PERSON><PERSON><PERSON><PERSON>", "deserialize", "invalid", "setLocale", "locale", "compareDate", "first", "second", "getYear", "getMonth", "getDate", "sameDate", "firstValid", "second<PERSON><PERSON><PERSON>", "clampDate", "date", "min", "max", "MAT_DATE_FORMATS", "ISO_8601_REGEX", "range", "length", "valueFunction", "valuesArray", "Array", "i", "NativeDateAdapter", "matDateLocale", "_platform", "useUtcForDisplay", "getFullYear", "getDayOfWeek", "getDay", "getMonthNames", "style", "dtf", "Intl", "DateTimeFormat", "month", "timeZone", "_format", "Date", "getDateNames", "day", "getDayOfWeekNames", "weekday", "getYearName", "year", "getFirstDayOfWeek", "getNumDaysInMonth", "_createDateWithOverflow", "clone", "getTime", "createDate", "result", "today", "parse", "parseFormat", "format", "displayFormat", "addCalendarYears", "years", "addCalendarMonths", "months", "newDate", "addCalendarDays", "days", "toIso8601", "getUTCFullYear", "_2digit", "getUTCMonth", "getUTCDate", "join", "test", "isNaN", "NaN", "d", "setFullYear", "setHours", "n", "slice", "setUTCFullYear", "setUTCHours", "getHours", "getMinutes", "getSeconds", "getMilliseconds", "NativeDateAdapter_Factory", "ɵprov", "ɵɵdefineInjectable", "token", "MAT_NATIVE_DATE_FORMATS", "dateInput", "month<PERSON><PERSON><PERSON><PERSON><PERSON>", "dateA11yLabel", "monthYearA11yLabel", "NativeDateModule", "NativeDateModule_Factory", "providers", "provide", "useClass", "MatNativeDateModule", "MatNativeDateModule_Factory", "useValue", "ShowOnDirtyErrorStateMatcher", "form", "dirty", "submitted", "ShowOnDirtyErrorStateMatcher_Factory", "ErrorStateMatcher", "touched", "ErrorStateMatcher_Factory", "MatLine", "MatLine_Factory", "ɵdir", "ɵɵdefineDirective", "selectors", "hostAttrs", "selector", "host", "setLines", "lines", "element", "prefix", "changes", "pipe", "subscribe", "setClass", "className", "isAdd", "toggle", "MatLineModule", "MatLineModule_Factory", "declarations", "RippleRef", "_renderer", "config", "_animationForciblyDisabledThroughCss", "state", "fadeOut", "fadeOutRipple", "passiveCapturingEventOptions$1", "passive", "capture", "RippleEventManager", "_events", "Map", "_delegate<PERSON><PERSON><PERSON><PERSON><PERSON>", "event", "target", "get", "handlers", "contains", "handler", "handleEvent", "add<PERSON><PERSON><PERSON>", "ngZone", "handlersForEvent", "handlersForElement", "set", "Set", "runOutsideAngular", "document", "addEventListener", "<PERSON><PERSON><PERSON><PERSON>", "delete", "size", "removeEventListener", "defaultRippleAnimationConfig", "enterDuration", "exitDuration", "ignoreMouseEventsTimeout", "passiveCapturingEventOptions", "pointerDownEvents", "pointerUpEvents", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_eventManager", "_target", "_ngZone", "elementOrElementRef", "_isPointerDown", "_activeRipples", "_pointerUpEventsRegistered", "_containerElement", "fadeInRipple", "x", "y", "containerRect", "_containerRect", "getBoundingClientRect", "animationConfig", "animation", "centered", "left", "width", "top", "height", "radius", "distanceToFurthestCorner", "offsetX", "offsetY", "ripple", "backgroundColor", "transitionDuration", "computedStyles", "window", "userTransitionProperty", "transitionProperty", "userTransitionDuration", "animationForciblyDisabledThroughCss", "rippleRef", "transform", "persistent", "_mostRecentTransientRipple", "eventListeners", "onTransitionEnd", "_finishRippleTransition", "onTransitionCancel", "_destroyRipple", "rippleEl", "opacity", "fadeOutAll", "_getActiveRipples", "fadeOutAllNonPersistent", "setupTriggerEvents", "_triggerElement", "_removeTriggerEvents", "_onMousedown", "_onTouchStart", "_onPointerUp", "_startFadeOutTransition", "isMostRecentTransientRipple", "isFakeMousedown", "isSyntheticEvent", "_lastTouchStartEvent", "now", "rippleDisabled", "clientX", "clientY", "rippleConfig", "touches", "changedTouches", "isVisible", "terminateOnPointerUp", "from", "keys", "trigger", "rect", "distX", "Math", "abs", "right", "distY", "bottom", "sqrt", "MAT_RIPPLE_GLOBAL_OPTIONS", "<PERSON><PERSON><PERSON><PERSON>", "_setupTriggerEventsIfEnabled", "_trigger", "globalOptions", "_animationMode", "_globalOptions", "_ripple<PERSON><PERSON>er", "ngOnInit", "ngOnDestroy", "launch", "configOrX", "MatRipple_Factory", "ɵɵdirectiveInject", "hostVars", "hostBindings", "MatRipple_HostBindings", "ɵɵclassProp", "unbounded", "inputs", "exportAs", "MatRippleModule", "MatRippleModule_Factory", "MatPseudoCheckbox", "appearance", "MatPseudoCheckbox_Factory", "ɵcmp", "ɵɵdefineComponent", "MatPseudoCheckbox_HostBindings", "decls", "vars", "template", "MatPseudoCheckbox_Template", "styles", "encapsulation", "changeDetection", "None", "OnPush", "MatPseudoCheckboxModule", "MatPseudoCheckboxModule_Factory", "MAT_OPTION_PARENT_COMPONENT", "_MatOptgroupMixinBase", "_uniqueOptgroupIdCounter", "_MatOptgroupBase", "_labelId", "_inert", "inertGroups", "_MatOptgroupBase_Factory", "features", "ɵɵInheritDefinitionFeature", "MAT_OPTGROUP", "MatOptgroup", "ɵMatOptgroup_BaseFactory", "MatOptgroup_Factory", "ɵɵgetInheritedFactory", "MatOptgroup_HostBindings", "ɵɵattribute", "toString", "ɵɵProvidersFeature", "useExisting", "ngContentSelectors", "consts", "MatOptgroup_Template", "ɵɵprojectionDef", "ɵɵprojection", "_uniqueIdCounter", "MatOptionSelectionChange", "source", "isUserInput", "_MatOptionBase", "multiple", "_parent", "_selected", "hideSingleSelectionIndicator", "_element", "_changeDetectorRef", "_active", "_mostRecentViewValue", "id", "onSelectionChange", "_stateChanges", "active", "viewValue", "_text", "textContent", "trim", "select", "emitEvent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_emitSelectionChangeEvent", "deselect", "focus", "_origin", "options", "_getHostElement", "setActiveStyles", "setInactiveStyles", "get<PERSON><PERSON><PERSON>", "_handleKeydown", "keyCode", "_selectViaInteraction", "preventDefault", "_getTabIndex", "ngAfterViewChecked", "emit", "_MatOptionBase_Factory", "ɵɵinvalidFactory", "viewQuery", "_MatOptionBase_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "outputs", "ChangeDetectorRef", "static", "MatOption", "changeDetectorRef", "MatOption_Factory", "MatOption_HostBindings", "ɵɵlistener", "MatOption_click_HostBindingHandler", "MatOption_keydown_HostBindingHandler", "$event", "ɵɵhostProperty", "MatOption_Template", "ɵɵtemplate", "dependencies", "NgIf", "_countGroupLabelsBeforeOption", "optionIndex", "optionGroups", "optionsArray", "toArray", "groups", "groupCounter", "_getOptionScrollPosition", "optionOffset", "optionHeight", "currentScrollPosition", "panelHeight", "MatOptionModule", "MatOptionModule_Factory", "eventListenerOptions", "rippleInteractionEvents", "matRippleUninitialized", "matRippleClassName", "mat<PERSON><PERSON>pleCentered", "matRippleDisabled", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_globalRippleOptions", "_hosts", "_onInteraction", "HTMLElement", "eventTarget", "closest", "_createRipple", "hosts", "destroyRipple", "configureRipple", "setAttribute", "hasAttribute", "getRipple", "setDisabled", "removeAttribute", "existingRipple", "querySelector", "getAttribute", "append", "attachRipple", "MatRippleLoader_Factory"], "sources": ["C:/cal_wf/Augment/4-img/apqr400bl/node_modules/@angular/material/fesm2022/core.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Version, InjectionToken, inject, NgModule, Optional, Inject, LOCALE_ID, Injectable, Directive, Input, Component, ViewEncapsulation, ChangeDetectionStrategy, EventEmitter, Output, ViewChild, ANIMATION_MODULE_TYPE as ANIMATION_MODULE_TYPE$1, NgZone, ElementRef } from '@angular/core';\nimport * as i1 from '@angular/cdk/a11y';\nimport { isFakeMousedownFromScreenReader, isFakeTouchstartFromScreenReader } from '@angular/cdk/a11y';\nimport { BidiModule } from '@angular/cdk/bidi';\nimport { VERSION as VERSION$1 } from '@angular/cdk';\nimport * as i3 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i1$1 from '@angular/cdk/platform';\nimport { Platform, _isTestEnvironment, normalizePassiveListenerOptions, _getEventTarget } from '@angular/cdk/platform';\nimport { coerceBooleanProperty, coerceNumberProperty, coerceElement } from '@angular/cdk/coercion';\nimport { Observable, Subject } from 'rxjs';\nimport { startWith } from 'rxjs/operators';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport { ENTER, SPACE, hasModifierKey } from '@angular/cdk/keycodes';\n\n/** Current version of Angular Material. */\nconst VERSION = new Version('16.2.14');\n\n/** @docs-private */\nclass AnimationCurves {\n    static { this.STANDARD_CURVE = 'cubic-bezier(0.4,0.0,0.2,1)'; }\n    static { this.DECELERATION_CURVE = 'cubic-bezier(0.0,0.0,0.2,1)'; }\n    static { this.ACCELERATION_CURVE = 'cubic-bezier(0.4,0.0,1,1)'; }\n    static { this.SHARP_CURVE = 'cubic-bezier(0.4,0.0,0.6,1)'; }\n}\n/** @docs-private */\nclass AnimationDurations {\n    static { this.COMPLEX = '375ms'; }\n    static { this.ENTERING = '225ms'; }\n    static { this.EXITING = '195ms'; }\n}\n\n/** @docs-private */\nfunction MATERIAL_SANITY_CHECKS_FACTORY() {\n    return true;\n}\n/** Injection token that configures whether the Material sanity checks are enabled. */\nconst MATERIAL_SANITY_CHECKS = new InjectionToken('mat-sanity-checks', {\n    providedIn: 'root',\n    factory: MATERIAL_SANITY_CHECKS_FACTORY,\n});\n/**\n * Module that captures anything that should be loaded and/or run for *all* Angular Material\n * components. This includes Bidi, etc.\n *\n * This module should be imported to each top-level component module (e.g., MatTabsModule).\n */\nclass MatCommonModule {\n    constructor(highContrastModeDetector, _sanityChecks, _document) {\n        this._sanityChecks = _sanityChecks;\n        this._document = _document;\n        /** Whether we've done the global sanity checks (e.g. a theme is loaded, there is a doctype). */\n        this._hasDoneGlobalChecks = false;\n        // While A11yModule also does this, we repeat it here to avoid importing A11yModule\n        // in MatCommonModule.\n        highContrastModeDetector._applyBodyHighContrastModeCssClasses();\n        if (!this._hasDoneGlobalChecks) {\n            this._hasDoneGlobalChecks = true;\n            if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                // Inject in here so the reference to `Platform` can be removed in production mode.\n                const platform = inject(Platform, { optional: true });\n                if (this._checkIsEnabled('doctype')) {\n                    _checkDoctypeIsDefined(this._document);\n                }\n                if (this._checkIsEnabled('theme')) {\n                    _checkThemeIsPresent(this._document, !!platform?.isBrowser);\n                }\n                if (this._checkIsEnabled('version')) {\n                    _checkCdkVersionMatch();\n                }\n            }\n        }\n    }\n    /** Gets whether a specific sanity check is enabled. */\n    _checkIsEnabled(name) {\n        if (_isTestEnvironment()) {\n            return false;\n        }\n        if (typeof this._sanityChecks === 'boolean') {\n            return this._sanityChecks;\n        }\n        return !!this._sanityChecks[name];\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatCommonModule, deps: [{ token: i1.HighContrastModeDetector }, { token: MATERIAL_SANITY_CHECKS, optional: true }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.1.1\", ngImport: i0, type: MatCommonModule, imports: [BidiModule], exports: [BidiModule] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatCommonModule, imports: [BidiModule, BidiModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatCommonModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [BidiModule],\n                    exports: [BidiModule],\n                }]\n        }], ctorParameters: function () { return [{ type: i1.HighContrastModeDetector }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MATERIAL_SANITY_CHECKS]\n                }] }, { type: Document, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n/** Checks that the page has a doctype. */\nfunction _checkDoctypeIsDefined(doc) {\n    if (!doc.doctype) {\n        console.warn('Current document does not have a doctype. This may cause ' +\n            'some Angular Material components not to behave as expected.');\n    }\n}\n/** Checks that a theme has been included. */\nfunction _checkThemeIsPresent(doc, isBrowser) {\n    // We need to assert that the `body` is defined, because these checks run very early\n    // and the `body` won't be defined if the consumer put their scripts in the `head`.\n    if (!doc.body || !isBrowser) {\n        return;\n    }\n    const testElement = doc.createElement('div');\n    testElement.classList.add('mat-theme-loaded-marker');\n    doc.body.appendChild(testElement);\n    const computedStyle = getComputedStyle(testElement);\n    // In some situations the computed style of the test element can be null. For example in\n    // Firefox, the computed style is null if an application is running inside of a hidden iframe.\n    // See: https://bugzilla.mozilla.org/show_bug.cgi?id=548397\n    if (computedStyle && computedStyle.display !== 'none') {\n        console.warn('Could not find Angular Material core theme. Most Material ' +\n            'components may not work as expected. For more info refer ' +\n            'to the theming guide: https://material.angular.io/guide/theming');\n    }\n    testElement.remove();\n}\n/** Checks whether the Material version matches the CDK version. */\nfunction _checkCdkVersionMatch() {\n    if (VERSION.full !== VERSION$1.full) {\n        console.warn('The Angular Material version (' +\n            VERSION.full +\n            ') does not match ' +\n            'the Angular CDK version (' +\n            VERSION$1.full +\n            ').\\n' +\n            'Please ensure the versions of these two packages exactly match.');\n    }\n}\n\nfunction mixinDisabled(base) {\n    return class extends base {\n        get disabled() {\n            return this._disabled;\n        }\n        set disabled(value) {\n            this._disabled = coerceBooleanProperty(value);\n        }\n        constructor(...args) {\n            super(...args);\n            this._disabled = false;\n        }\n    };\n}\n\nfunction mixinColor(base, defaultColor) {\n    return class extends base {\n        get color() {\n            return this._color;\n        }\n        set color(value) {\n            const colorPalette = value || this.defaultColor;\n            if (colorPalette !== this._color) {\n                if (this._color) {\n                    this._elementRef.nativeElement.classList.remove(`mat-${this._color}`);\n                }\n                if (colorPalette) {\n                    this._elementRef.nativeElement.classList.add(`mat-${colorPalette}`);\n                }\n                this._color = colorPalette;\n            }\n        }\n        constructor(...args) {\n            super(...args);\n            this.defaultColor = defaultColor;\n            // Set the default color that can be specified from the mixin.\n            this.color = defaultColor;\n        }\n    };\n}\n\nfunction mixinDisableRipple(base) {\n    return class extends base {\n        /** Whether the ripple effect is disabled or not. */\n        get disableRipple() {\n            return this._disableRipple;\n        }\n        set disableRipple(value) {\n            this._disableRipple = coerceBooleanProperty(value);\n        }\n        constructor(...args) {\n            super(...args);\n            this._disableRipple = false;\n        }\n    };\n}\n\nfunction mixinTabIndex(base, defaultTabIndex = 0) {\n    return class extends base {\n        get tabIndex() {\n            return this.disabled ? -1 : this._tabIndex;\n        }\n        set tabIndex(value) {\n            // If the specified tabIndex value is null or undefined, fall back to the default value.\n            this._tabIndex = value != null ? coerceNumberProperty(value) : this.defaultTabIndex;\n        }\n        constructor(...args) {\n            super(...args);\n            this._tabIndex = defaultTabIndex;\n            this.defaultTabIndex = defaultTabIndex;\n        }\n    };\n}\n\nfunction mixinErrorState(base) {\n    return class extends base {\n        /** Updates the error state based on the provided error state matcher. */\n        updateErrorState() {\n            const oldState = this.errorState;\n            const parent = this._parentFormGroup || this._parentForm;\n            const matcher = this.errorStateMatcher || this._defaultErrorStateMatcher;\n            const control = this.ngControl ? this.ngControl.control : null;\n            const newState = matcher.isErrorState(control, parent);\n            if (newState !== oldState) {\n                this.errorState = newState;\n                this.stateChanges.next();\n            }\n        }\n        constructor(...args) {\n            super(...args);\n            /** Whether the component is in an error state. */\n            this.errorState = false;\n        }\n    };\n}\n\n/** Mixin to augment a directive with an initialized property that will emits when ngOnInit ends. */\nfunction mixinInitialized(base) {\n    return class extends base {\n        constructor(...args) {\n            super(...args);\n            /** Whether this directive has been marked as initialized. */\n            this._isInitialized = false;\n            /**\n             * List of subscribers that subscribed before the directive was initialized. Should be notified\n             * during _markInitialized. Set to null after pending subscribers are notified, and should\n             * not expect to be populated after.\n             */\n            this._pendingSubscribers = [];\n            /**\n             * Observable stream that emits when the directive initializes. If already initialized, the\n             * subscriber is stored to be notified once _markInitialized is called.\n             */\n            this.initialized = new Observable(subscriber => {\n                // If initialized, immediately notify the subscriber. Otherwise store the subscriber to notify\n                // when _markInitialized is called.\n                if (this._isInitialized) {\n                    this._notifySubscriber(subscriber);\n                }\n                else {\n                    this._pendingSubscribers.push(subscriber);\n                }\n            });\n        }\n        /**\n         * Marks the state as initialized and notifies pending subscribers. Should be called at the end\n         * of ngOnInit.\n         * @docs-private\n         */\n        _markInitialized() {\n            if (this._isInitialized && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw Error('This directive has already been marked as initialized and ' +\n                    'should not be called twice.');\n            }\n            this._isInitialized = true;\n            this._pendingSubscribers.forEach(this._notifySubscriber);\n            this._pendingSubscribers = null;\n        }\n        /** Emits and completes the subscriber stream (should only emit once). */\n        _notifySubscriber(subscriber) {\n            subscriber.next();\n            subscriber.complete();\n        }\n    };\n}\n\n/** InjectionToken for datepicker that can be used to override default locale code. */\nconst MAT_DATE_LOCALE = new InjectionToken('MAT_DATE_LOCALE', {\n    providedIn: 'root',\n    factory: MAT_DATE_LOCALE_FACTORY,\n});\n/** @docs-private */\nfunction MAT_DATE_LOCALE_FACTORY() {\n    return inject(LOCALE_ID);\n}\n/** Adapts type `D` to be usable as a date by cdk-based components that work with dates. */\nclass DateAdapter {\n    constructor() {\n        this._localeChanges = new Subject();\n        /** A stream that emits when the locale changes. */\n        this.localeChanges = this._localeChanges;\n    }\n    /**\n     * Given a potential date object, returns that same date object if it is\n     * a valid date, or `null` if it's not a valid date.\n     * @param obj The object to check.\n     * @returns A date or `null`.\n     */\n    getValidDateOrNull(obj) {\n        return this.isDateInstance(obj) && this.isValid(obj) ? obj : null;\n    }\n    /**\n     * Attempts to deserialize a value to a valid date object. This is different from parsing in that\n     * deserialize should only accept non-ambiguous, locale-independent formats (e.g. a ISO 8601\n     * string). The default implementation does not allow any deserialization, it simply checks that\n     * the given value is already a valid date object or null. The `<mat-datepicker>` will call this\n     * method on all of its `@Input()` properties that accept dates. It is therefore possible to\n     * support passing values from your backend directly to these properties by overriding this method\n     * to also deserialize the format used by your backend.\n     * @param value The value to be deserialized into a date object.\n     * @returns The deserialized date object, either a valid date, null if the value can be\n     *     deserialized into a null date (e.g. the empty string), or an invalid date.\n     */\n    deserialize(value) {\n        if (value == null || (this.isDateInstance(value) && this.isValid(value))) {\n            return value;\n        }\n        return this.invalid();\n    }\n    /**\n     * Sets the locale used for all dates.\n     * @param locale The new locale.\n     */\n    setLocale(locale) {\n        this.locale = locale;\n        this._localeChanges.next();\n    }\n    /**\n     * Compares two dates.\n     * @param first The first date to compare.\n     * @param second The second date to compare.\n     * @returns 0 if the dates are equal, a number less than 0 if the first date is earlier,\n     *     a number greater than 0 if the first date is later.\n     */\n    compareDate(first, second) {\n        return (this.getYear(first) - this.getYear(second) ||\n            this.getMonth(first) - this.getMonth(second) ||\n            this.getDate(first) - this.getDate(second));\n    }\n    /**\n     * Checks if two dates are equal.\n     * @param first The first date to check.\n     * @param second The second date to check.\n     * @returns Whether the two dates are equal.\n     *     Null dates are considered equal to other null dates.\n     */\n    sameDate(first, second) {\n        if (first && second) {\n            let firstValid = this.isValid(first);\n            let secondValid = this.isValid(second);\n            if (firstValid && secondValid) {\n                return !this.compareDate(first, second);\n            }\n            return firstValid == secondValid;\n        }\n        return first == second;\n    }\n    /**\n     * Clamp the given date between min and max dates.\n     * @param date The date to clamp.\n     * @param min The minimum value to allow. If null or omitted no min is enforced.\n     * @param max The maximum value to allow. If null or omitted no max is enforced.\n     * @returns `min` if `date` is less than `min`, `max` if date is greater than `max`,\n     *     otherwise `date`.\n     */\n    clampDate(date, min, max) {\n        if (min && this.compareDate(date, min) < 0) {\n            return min;\n        }\n        if (max && this.compareDate(date, max) > 0) {\n            return max;\n        }\n        return date;\n    }\n}\n\nconst MAT_DATE_FORMATS = new InjectionToken('mat-date-formats');\n\n/**\n * Matches strings that have the form of a valid RFC 3339 string\n * (https://tools.ietf.org/html/rfc3339). Note that the string may not actually be a valid date\n * because the regex will match strings an with out of bounds month, date, etc.\n */\nconst ISO_8601_REGEX = /^\\d{4}-\\d{2}-\\d{2}(?:T\\d{2}:\\d{2}:\\d{2}(?:\\.\\d+)?(?:Z|(?:(?:\\+|-)\\d{2}:\\d{2}))?)?$/;\n/** Creates an array and fills it with values. */\nfunction range(length, valueFunction) {\n    const valuesArray = Array(length);\n    for (let i = 0; i < length; i++) {\n        valuesArray[i] = valueFunction(i);\n    }\n    return valuesArray;\n}\n/** Adapts the native JS Date for use with cdk-based components that work with dates. */\nclass NativeDateAdapter extends DateAdapter {\n    constructor(matDateLocale, \n    /**\n     * @deprecated No longer being used. To be removed.\n     * @breaking-change 14.0.0\n     */\n    _platform) {\n        super();\n        /**\n         * @deprecated No longer being used. To be removed.\n         * @breaking-change 14.0.0\n         */\n        this.useUtcForDisplay = false;\n        super.setLocale(matDateLocale);\n    }\n    getYear(date) {\n        return date.getFullYear();\n    }\n    getMonth(date) {\n        return date.getMonth();\n    }\n    getDate(date) {\n        return date.getDate();\n    }\n    getDayOfWeek(date) {\n        return date.getDay();\n    }\n    getMonthNames(style) {\n        const dtf = new Intl.DateTimeFormat(this.locale, { month: style, timeZone: 'utc' });\n        return range(12, i => this._format(dtf, new Date(2017, i, 1)));\n    }\n    getDateNames() {\n        const dtf = new Intl.DateTimeFormat(this.locale, { day: 'numeric', timeZone: 'utc' });\n        return range(31, i => this._format(dtf, new Date(2017, 0, i + 1)));\n    }\n    getDayOfWeekNames(style) {\n        const dtf = new Intl.DateTimeFormat(this.locale, { weekday: style, timeZone: 'utc' });\n        return range(7, i => this._format(dtf, new Date(2017, 0, i + 1)));\n    }\n    getYearName(date) {\n        const dtf = new Intl.DateTimeFormat(this.locale, { year: 'numeric', timeZone: 'utc' });\n        return this._format(dtf, date);\n    }\n    getFirstDayOfWeek() {\n        // We can't tell using native JS Date what the first day of the week is, we default to Sunday.\n        return 0;\n    }\n    getNumDaysInMonth(date) {\n        return this.getDate(this._createDateWithOverflow(this.getYear(date), this.getMonth(date) + 1, 0));\n    }\n    clone(date) {\n        return new Date(date.getTime());\n    }\n    createDate(year, month, date) {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            // Check for invalid month and date (except upper bound on date which we have to check after\n            // creating the Date).\n            if (month < 0 || month > 11) {\n                throw Error(`Invalid month index \"${month}\". Month index has to be between 0 and 11.`);\n            }\n            if (date < 1) {\n                throw Error(`Invalid date \"${date}\". Date has to be greater than 0.`);\n            }\n        }\n        let result = this._createDateWithOverflow(year, month, date);\n        // Check that the date wasn't above the upper bound for the month, causing the month to overflow\n        if (result.getMonth() != month && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error(`Invalid date \"${date}\" for month with index \"${month}\".`);\n        }\n        return result;\n    }\n    today() {\n        return new Date();\n    }\n    parse(value, parseFormat) {\n        // We have no way using the native JS Date to set the parse format or locale, so we ignore these\n        // parameters.\n        if (typeof value == 'number') {\n            return new Date(value);\n        }\n        return value ? new Date(Date.parse(value)) : null;\n    }\n    format(date, displayFormat) {\n        if (!this.isValid(date)) {\n            throw Error('NativeDateAdapter: Cannot format invalid date.');\n        }\n        const dtf = new Intl.DateTimeFormat(this.locale, { ...displayFormat, timeZone: 'utc' });\n        return this._format(dtf, date);\n    }\n    addCalendarYears(date, years) {\n        return this.addCalendarMonths(date, years * 12);\n    }\n    addCalendarMonths(date, months) {\n        let newDate = this._createDateWithOverflow(this.getYear(date), this.getMonth(date) + months, this.getDate(date));\n        // It's possible to wind up in the wrong month if the original month has more days than the new\n        // month. In this case we want to go to the last day of the desired month.\n        // Note: the additional + 12 % 12 ensures we end up with a positive number, since JS % doesn't\n        // guarantee this.\n        if (this.getMonth(newDate) != (((this.getMonth(date) + months) % 12) + 12) % 12) {\n            newDate = this._createDateWithOverflow(this.getYear(newDate), this.getMonth(newDate), 0);\n        }\n        return newDate;\n    }\n    addCalendarDays(date, days) {\n        return this._createDateWithOverflow(this.getYear(date), this.getMonth(date), this.getDate(date) + days);\n    }\n    toIso8601(date) {\n        return [\n            date.getUTCFullYear(),\n            this._2digit(date.getUTCMonth() + 1),\n            this._2digit(date.getUTCDate()),\n        ].join('-');\n    }\n    /**\n     * Returns the given value if given a valid Date or null. Deserializes valid ISO 8601 strings\n     * (https://www.ietf.org/rfc/rfc3339.txt) into valid Dates and empty string into null. Returns an\n     * invalid date for all other values.\n     */\n    deserialize(value) {\n        if (typeof value === 'string') {\n            if (!value) {\n                return null;\n            }\n            // The `Date` constructor accepts formats other than ISO 8601, so we need to make sure the\n            // string is the right format first.\n            if (ISO_8601_REGEX.test(value)) {\n                let date = new Date(value);\n                if (this.isValid(date)) {\n                    return date;\n                }\n            }\n        }\n        return super.deserialize(value);\n    }\n    isDateInstance(obj) {\n        return obj instanceof Date;\n    }\n    isValid(date) {\n        return !isNaN(date.getTime());\n    }\n    invalid() {\n        return new Date(NaN);\n    }\n    /** Creates a date but allows the month and date to overflow. */\n    _createDateWithOverflow(year, month, date) {\n        // Passing the year to the constructor causes year numbers <100 to be converted to 19xx.\n        // To work around this we use `setFullYear` and `setHours` instead.\n        const d = new Date();\n        d.setFullYear(year, month, date);\n        d.setHours(0, 0, 0, 0);\n        return d;\n    }\n    /**\n     * Pads a number to make it two digits.\n     * @param n The number to pad.\n     * @returns The padded number.\n     */\n    _2digit(n) {\n        return ('00' + n).slice(-2);\n    }\n    /**\n     * When converting Date object to string, javascript built-in functions may return wrong\n     * results because it applies its internal DST rules. The DST rules around the world change\n     * very frequently, and the current valid rule is not always valid in previous years though.\n     * We work around this problem building a new Date object which has its internal UTC\n     * representation with the local date and time.\n     * @param dtf Intl.DateTimeFormat object, containing the desired string format. It must have\n     *    timeZone set to 'utc' to work fine.\n     * @param date Date from which we want to get the string representation according to dtf\n     * @returns A Date object with its UTC representation based on the passed in date info\n     */\n    _format(dtf, date) {\n        // Passing the year to the constructor causes year numbers <100 to be converted to 19xx.\n        // To work around this we use `setUTCFullYear` and `setUTCHours` instead.\n        const d = new Date();\n        d.setUTCFullYear(date.getFullYear(), date.getMonth(), date.getDate());\n        d.setUTCHours(date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds());\n        return dtf.format(d);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: NativeDateAdapter, deps: [{ token: MAT_DATE_LOCALE, optional: true }, { token: i1$1.Platform }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: NativeDateAdapter }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: NativeDateAdapter, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_DATE_LOCALE]\n                }] }, { type: i1$1.Platform }]; } });\n\nconst MAT_NATIVE_DATE_FORMATS = {\n    parse: {\n        dateInput: null,\n    },\n    display: {\n        dateInput: { year: 'numeric', month: 'numeric', day: 'numeric' },\n        monthYearLabel: { year: 'numeric', month: 'short' },\n        dateA11yLabel: { year: 'numeric', month: 'long', day: 'numeric' },\n        monthYearA11yLabel: { year: 'numeric', month: 'long' },\n    },\n};\n\nclass NativeDateModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: NativeDateModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.1.1\", ngImport: i0, type: NativeDateModule }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: NativeDateModule, providers: [{ provide: DateAdapter, useClass: NativeDateAdapter }] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: NativeDateModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [{ provide: DateAdapter, useClass: NativeDateAdapter }],\n                }]\n        }] });\nclass MatNativeDateModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatNativeDateModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.1.1\", ngImport: i0, type: MatNativeDateModule, imports: [NativeDateModule] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatNativeDateModule, providers: [{ provide: MAT_DATE_FORMATS, useValue: MAT_NATIVE_DATE_FORMATS }], imports: [NativeDateModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatNativeDateModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [NativeDateModule],\n                    providers: [{ provide: MAT_DATE_FORMATS, useValue: MAT_NATIVE_DATE_FORMATS }],\n                }]\n        }] });\n\n/** Error state matcher that matches when a control is invalid and dirty. */\nclass ShowOnDirtyErrorStateMatcher {\n    isErrorState(control, form) {\n        return !!(control && control.invalid && (control.dirty || (form && form.submitted)));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: ShowOnDirtyErrorStateMatcher, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: ShowOnDirtyErrorStateMatcher }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: ShowOnDirtyErrorStateMatcher, decorators: [{\n            type: Injectable\n        }] });\n/** Provider that defines how form controls behave with regards to displaying error messages. */\nclass ErrorStateMatcher {\n    isErrorState(control, form) {\n        return !!(control && control.invalid && (control.touched || (form && form.submitted)));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: ErrorStateMatcher, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: ErrorStateMatcher, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: ErrorStateMatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n\n/**\n * Shared directive to count lines inside a text area, such as a list item.\n * Line elements can be extracted with a @ContentChildren(MatLine) query, then\n * counted by checking the query list's length.\n */\nclass MatLine {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatLine, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatLine, selector: \"[mat-line], [matLine]\", host: { classAttribute: \"mat-line\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatLine, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-line], [matLine]',\n                    host: { 'class': 'mat-line' },\n                }]\n        }] });\n/**\n * Helper that takes a query list of lines and sets the correct class on the host.\n * @docs-private\n */\nfunction setLines(lines, element, prefix = 'mat') {\n    // Note: doesn't need to unsubscribe, because `changes`\n    // gets completed by Angular when the view is destroyed.\n    lines.changes.pipe(startWith(lines)).subscribe(({ length }) => {\n        setClass(element, `${prefix}-2-line`, false);\n        setClass(element, `${prefix}-3-line`, false);\n        setClass(element, `${prefix}-multi-line`, false);\n        if (length === 2 || length === 3) {\n            setClass(element, `${prefix}-${length}-line`, true);\n        }\n        else if (length > 3) {\n            setClass(element, `${prefix}-multi-line`, true);\n        }\n    });\n}\n/** Adds or removes a class from an element. */\nfunction setClass(element, className, isAdd) {\n    element.nativeElement.classList.toggle(className, isAdd);\n}\nclass MatLineModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatLineModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.1.1\", ngImport: i0, type: MatLineModule, declarations: [MatLine], imports: [MatCommonModule], exports: [MatLine, MatCommonModule] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatLineModule, imports: [MatCommonModule, MatCommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatLineModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule],\n                    exports: [MatLine, MatCommonModule],\n                    declarations: [MatLine],\n                }]\n        }] });\n\n/**\n * Reference to a previously launched ripple element.\n */\nclass RippleRef {\n    constructor(_renderer, \n    /** Reference to the ripple HTML element. */\n    element, \n    /** Ripple configuration used for the ripple. */\n    config, \n    /* Whether animations are forcibly disabled for ripples through CSS. */\n    _animationForciblyDisabledThroughCss = false) {\n        this._renderer = _renderer;\n        this.element = element;\n        this.config = config;\n        this._animationForciblyDisabledThroughCss = _animationForciblyDisabledThroughCss;\n        /** Current state of the ripple. */\n        this.state = 3 /* RippleState.HIDDEN */;\n    }\n    /** Fades out the ripple element. */\n    fadeOut() {\n        this._renderer.fadeOutRipple(this);\n    }\n}\n\n/** Options used to bind a passive capturing event. */\nconst passiveCapturingEventOptions$1 = normalizePassiveListenerOptions({\n    passive: true,\n    capture: true,\n});\n/** Manages events through delegation so that as few event handlers as possible are bound. */\nclass RippleEventManager {\n    constructor() {\n        this._events = new Map();\n        /** Event handler that is bound and which dispatches the events to the different targets. */\n        this._delegateEventHandler = (event) => {\n            const target = _getEventTarget(event);\n            if (target) {\n                this._events.get(event.type)?.forEach((handlers, element) => {\n                    if (element === target || element.contains(target)) {\n                        handlers.forEach(handler => handler.handleEvent(event));\n                    }\n                });\n            }\n        };\n    }\n    /** Adds an event handler. */\n    addHandler(ngZone, name, element, handler) {\n        const handlersForEvent = this._events.get(name);\n        if (handlersForEvent) {\n            const handlersForElement = handlersForEvent.get(element);\n            if (handlersForElement) {\n                handlersForElement.add(handler);\n            }\n            else {\n                handlersForEvent.set(element, new Set([handler]));\n            }\n        }\n        else {\n            this._events.set(name, new Map([[element, new Set([handler])]]));\n            ngZone.runOutsideAngular(() => {\n                document.addEventListener(name, this._delegateEventHandler, passiveCapturingEventOptions$1);\n            });\n        }\n    }\n    /** Removes an event handler. */\n    removeHandler(name, element, handler) {\n        const handlersForEvent = this._events.get(name);\n        if (!handlersForEvent) {\n            return;\n        }\n        const handlersForElement = handlersForEvent.get(element);\n        if (!handlersForElement) {\n            return;\n        }\n        handlersForElement.delete(handler);\n        if (handlersForElement.size === 0) {\n            handlersForEvent.delete(element);\n        }\n        if (handlersForEvent.size === 0) {\n            this._events.delete(name);\n            document.removeEventListener(name, this._delegateEventHandler, passiveCapturingEventOptions$1);\n        }\n    }\n}\n\n/**\n * Default ripple animation configuration for ripples without an explicit\n * animation config specified.\n */\nconst defaultRippleAnimationConfig = {\n    enterDuration: 225,\n    exitDuration: 150,\n};\n/**\n * Timeout for ignoring mouse events. Mouse events will be temporary ignored after touch\n * events to avoid synthetic mouse events.\n */\nconst ignoreMouseEventsTimeout = 800;\n/** Options used to bind a passive capturing event. */\nconst passiveCapturingEventOptions = normalizePassiveListenerOptions({\n    passive: true,\n    capture: true,\n});\n/** Events that signal that the pointer is down. */\nconst pointerDownEvents = ['mousedown', 'touchstart'];\n/** Events that signal that the pointer is up. */\nconst pointerUpEvents = ['mouseup', 'mouseleave', 'touchend', 'touchcancel'];\n/**\n * Helper service that performs DOM manipulations. Not intended to be used outside this module.\n * The constructor takes a reference to the ripple directive's host element and a map of DOM\n * event handlers to be installed on the element that triggers ripple animations.\n * This will eventually become a custom renderer once Angular support exists.\n * @docs-private\n */\nclass RippleRenderer {\n    static { this._eventManager = new RippleEventManager(); }\n    constructor(_target, _ngZone, elementOrElementRef, _platform) {\n        this._target = _target;\n        this._ngZone = _ngZone;\n        this._platform = _platform;\n        /** Whether the pointer is currently down or not. */\n        this._isPointerDown = false;\n        /**\n         * Map of currently active ripple references.\n         * The ripple reference is mapped to its element event listeners.\n         * The reason why `| null` is used is that event listeners are added only\n         * when the condition is truthy (see the `_startFadeOutTransition` method).\n         */\n        this._activeRipples = new Map();\n        /** Whether pointer-up event listeners have been registered. */\n        this._pointerUpEventsRegistered = false;\n        // Only do anything if we're on the browser.\n        if (_platform.isBrowser) {\n            this._containerElement = coerceElement(elementOrElementRef);\n        }\n    }\n    /**\n     * Fades in a ripple at the given coordinates.\n     * @param x Coordinate within the element, along the X axis at which to start the ripple.\n     * @param y Coordinate within the element, along the Y axis at which to start the ripple.\n     * @param config Extra ripple options.\n     */\n    fadeInRipple(x, y, config = {}) {\n        const containerRect = (this._containerRect =\n            this._containerRect || this._containerElement.getBoundingClientRect());\n        const animationConfig = { ...defaultRippleAnimationConfig, ...config.animation };\n        if (config.centered) {\n            x = containerRect.left + containerRect.width / 2;\n            y = containerRect.top + containerRect.height / 2;\n        }\n        const radius = config.radius || distanceToFurthestCorner(x, y, containerRect);\n        const offsetX = x - containerRect.left;\n        const offsetY = y - containerRect.top;\n        const enterDuration = animationConfig.enterDuration;\n        const ripple = document.createElement('div');\n        ripple.classList.add('mat-ripple-element');\n        ripple.style.left = `${offsetX - radius}px`;\n        ripple.style.top = `${offsetY - radius}px`;\n        ripple.style.height = `${radius * 2}px`;\n        ripple.style.width = `${radius * 2}px`;\n        // If a custom color has been specified, set it as inline style. If no color is\n        // set, the default color will be applied through the ripple theme styles.\n        if (config.color != null) {\n            ripple.style.backgroundColor = config.color;\n        }\n        ripple.style.transitionDuration = `${enterDuration}ms`;\n        this._containerElement.appendChild(ripple);\n        // By default the browser does not recalculate the styles of dynamically created\n        // ripple elements. This is critical to ensure that the `scale` animates properly.\n        // We enforce a style recalculation by calling `getComputedStyle` and *accessing* a property.\n        // See: https://gist.github.com/paulirish/5d52fb081b3570c81e3a\n        const computedStyles = window.getComputedStyle(ripple);\n        const userTransitionProperty = computedStyles.transitionProperty;\n        const userTransitionDuration = computedStyles.transitionDuration;\n        // Note: We detect whether animation is forcibly disabled through CSS (e.g. through\n        // `transition: none` or `display: none`). This is technically unexpected since animations are\n        // controlled through the animation config, but this exists for backwards compatibility. This\n        // logic does not need to be super accurate since it covers some edge cases which can be easily\n        // avoided by users.\n        const animationForciblyDisabledThroughCss = userTransitionProperty === 'none' ||\n            // Note: The canonical unit for serialized CSS `<time>` properties is seconds. Additionally\n            // some browsers expand the duration for every property (in our case `opacity` and `transform`).\n            userTransitionDuration === '0s' ||\n            userTransitionDuration === '0s, 0s' ||\n            // If the container is 0x0, it's likely `display: none`.\n            (containerRect.width === 0 && containerRect.height === 0);\n        // Exposed reference to the ripple that will be returned.\n        const rippleRef = new RippleRef(this, ripple, config, animationForciblyDisabledThroughCss);\n        // Start the enter animation by setting the transform/scale to 100%. The animation will\n        // execute as part of this statement because we forced a style recalculation before.\n        // Note: We use a 3d transform here in order to avoid an issue in Safari where\n        // the ripples aren't clipped when inside the shadow DOM (see #24028).\n        ripple.style.transform = 'scale3d(1, 1, 1)';\n        rippleRef.state = 0 /* RippleState.FADING_IN */;\n        if (!config.persistent) {\n            this._mostRecentTransientRipple = rippleRef;\n        }\n        let eventListeners = null;\n        // Do not register the `transition` event listener if fade-in and fade-out duration\n        // are set to zero. The events won't fire anyway and we can save resources here.\n        if (!animationForciblyDisabledThroughCss && (enterDuration || animationConfig.exitDuration)) {\n            this._ngZone.runOutsideAngular(() => {\n                const onTransitionEnd = () => this._finishRippleTransition(rippleRef);\n                const onTransitionCancel = () => this._destroyRipple(rippleRef);\n                ripple.addEventListener('transitionend', onTransitionEnd);\n                // If the transition is cancelled (e.g. due to DOM removal), we destroy the ripple\n                // directly as otherwise we would keep it part of the ripple container forever.\n                // https://www.w3.org/TR/css-transitions-1/#:~:text=no%20longer%20in%20the%20document.\n                ripple.addEventListener('transitioncancel', onTransitionCancel);\n                eventListeners = { onTransitionEnd, onTransitionCancel };\n            });\n        }\n        // Add the ripple reference to the list of all active ripples.\n        this._activeRipples.set(rippleRef, eventListeners);\n        // In case there is no fade-in transition duration, we need to manually call the transition\n        // end listener because `transitionend` doesn't fire if there is no transition.\n        if (animationForciblyDisabledThroughCss || !enterDuration) {\n            this._finishRippleTransition(rippleRef);\n        }\n        return rippleRef;\n    }\n    /** Fades out a ripple reference. */\n    fadeOutRipple(rippleRef) {\n        // For ripples already fading out or hidden, this should be a noop.\n        if (rippleRef.state === 2 /* RippleState.FADING_OUT */ || rippleRef.state === 3 /* RippleState.HIDDEN */) {\n            return;\n        }\n        const rippleEl = rippleRef.element;\n        const animationConfig = { ...defaultRippleAnimationConfig, ...rippleRef.config.animation };\n        // This starts the fade-out transition and will fire the transition end listener that\n        // removes the ripple element from the DOM.\n        rippleEl.style.transitionDuration = `${animationConfig.exitDuration}ms`;\n        rippleEl.style.opacity = '0';\n        rippleRef.state = 2 /* RippleState.FADING_OUT */;\n        // In case there is no fade-out transition duration, we need to manually call the\n        // transition end listener because `transitionend` doesn't fire if there is no transition.\n        if (rippleRef._animationForciblyDisabledThroughCss || !animationConfig.exitDuration) {\n            this._finishRippleTransition(rippleRef);\n        }\n    }\n    /** Fades out all currently active ripples. */\n    fadeOutAll() {\n        this._getActiveRipples().forEach(ripple => ripple.fadeOut());\n    }\n    /** Fades out all currently active non-persistent ripples. */\n    fadeOutAllNonPersistent() {\n        this._getActiveRipples().forEach(ripple => {\n            if (!ripple.config.persistent) {\n                ripple.fadeOut();\n            }\n        });\n    }\n    /** Sets up the trigger event listeners */\n    setupTriggerEvents(elementOrElementRef) {\n        const element = coerceElement(elementOrElementRef);\n        if (!this._platform.isBrowser || !element || element === this._triggerElement) {\n            return;\n        }\n        // Remove all previously registered event listeners from the trigger element.\n        this._removeTriggerEvents();\n        this._triggerElement = element;\n        // Use event delegation for the trigger events since they're\n        // set up during creation and are performance-sensitive.\n        pointerDownEvents.forEach(type => {\n            RippleRenderer._eventManager.addHandler(this._ngZone, type, element, this);\n        });\n    }\n    /**\n     * Handles all registered events.\n     * @docs-private\n     */\n    handleEvent(event) {\n        if (event.type === 'mousedown') {\n            this._onMousedown(event);\n        }\n        else if (event.type === 'touchstart') {\n            this._onTouchStart(event);\n        }\n        else {\n            this._onPointerUp();\n        }\n        // If pointer-up events haven't been registered yet, do so now.\n        // We do this on-demand in order to reduce the total number of event listeners\n        // registered by the ripples, which speeds up the rendering time for large UIs.\n        if (!this._pointerUpEventsRegistered) {\n            // The events for hiding the ripple are bound directly on the trigger, because:\n            // 1. Some of them occur frequently (e.g. `mouseleave`) and any advantage we get from\n            // delegation will be diminished by having to look through all the data structures often.\n            // 2. They aren't as performance-sensitive, because they're bound only after the user\n            // has interacted with an element.\n            this._ngZone.runOutsideAngular(() => {\n                pointerUpEvents.forEach(type => {\n                    this._triggerElement.addEventListener(type, this, passiveCapturingEventOptions);\n                });\n            });\n            this._pointerUpEventsRegistered = true;\n        }\n    }\n    /** Method that will be called if the fade-in or fade-in transition completed. */\n    _finishRippleTransition(rippleRef) {\n        if (rippleRef.state === 0 /* RippleState.FADING_IN */) {\n            this._startFadeOutTransition(rippleRef);\n        }\n        else if (rippleRef.state === 2 /* RippleState.FADING_OUT */) {\n            this._destroyRipple(rippleRef);\n        }\n    }\n    /**\n     * Starts the fade-out transition of the given ripple if it's not persistent and the pointer\n     * is not held down anymore.\n     */\n    _startFadeOutTransition(rippleRef) {\n        const isMostRecentTransientRipple = rippleRef === this._mostRecentTransientRipple;\n        const { persistent } = rippleRef.config;\n        rippleRef.state = 1 /* RippleState.VISIBLE */;\n        // When the timer runs out while the user has kept their pointer down, we want to\n        // keep only the persistent ripples and the latest transient ripple. We do this,\n        // because we don't want stacked transient ripples to appear after their enter\n        // animation has finished.\n        if (!persistent && (!isMostRecentTransientRipple || !this._isPointerDown)) {\n            rippleRef.fadeOut();\n        }\n    }\n    /** Destroys the given ripple by removing it from the DOM and updating its state. */\n    _destroyRipple(rippleRef) {\n        const eventListeners = this._activeRipples.get(rippleRef) ?? null;\n        this._activeRipples.delete(rippleRef);\n        // Clear out the cached bounding rect if we have no more ripples.\n        if (!this._activeRipples.size) {\n            this._containerRect = null;\n        }\n        // If the current ref is the most recent transient ripple, unset it\n        // avoid memory leaks.\n        if (rippleRef === this._mostRecentTransientRipple) {\n            this._mostRecentTransientRipple = null;\n        }\n        rippleRef.state = 3 /* RippleState.HIDDEN */;\n        if (eventListeners !== null) {\n            rippleRef.element.removeEventListener('transitionend', eventListeners.onTransitionEnd);\n            rippleRef.element.removeEventListener('transitioncancel', eventListeners.onTransitionCancel);\n        }\n        rippleRef.element.remove();\n    }\n    /** Function being called whenever the trigger is being pressed using mouse. */\n    _onMousedown(event) {\n        // Screen readers will fire fake mouse events for space/enter. Skip launching a\n        // ripple in this case for consistency with the non-screen-reader experience.\n        const isFakeMousedown = isFakeMousedownFromScreenReader(event);\n        const isSyntheticEvent = this._lastTouchStartEvent &&\n            Date.now() < this._lastTouchStartEvent + ignoreMouseEventsTimeout;\n        if (!this._target.rippleDisabled && !isFakeMousedown && !isSyntheticEvent) {\n            this._isPointerDown = true;\n            this.fadeInRipple(event.clientX, event.clientY, this._target.rippleConfig);\n        }\n    }\n    /** Function being called whenever the trigger is being pressed using touch. */\n    _onTouchStart(event) {\n        if (!this._target.rippleDisabled && !isFakeTouchstartFromScreenReader(event)) {\n            // Some browsers fire mouse events after a `touchstart` event. Those synthetic mouse\n            // events will launch a second ripple if we don't ignore mouse events for a specific\n            // time after a touchstart event.\n            this._lastTouchStartEvent = Date.now();\n            this._isPointerDown = true;\n            // Use `changedTouches` so we skip any touches where the user put\n            // their finger down, but used another finger to tap the element again.\n            const touches = event.changedTouches;\n            // According to the typings the touches should always be defined, but in some cases\n            // the browser appears to not assign them in tests which leads to flakes.\n            if (touches) {\n                for (let i = 0; i < touches.length; i++) {\n                    this.fadeInRipple(touches[i].clientX, touches[i].clientY, this._target.rippleConfig);\n                }\n            }\n        }\n    }\n    /** Function being called whenever the trigger is being released. */\n    _onPointerUp() {\n        if (!this._isPointerDown) {\n            return;\n        }\n        this._isPointerDown = false;\n        // Fade-out all ripples that are visible and not persistent.\n        this._getActiveRipples().forEach(ripple => {\n            // By default, only ripples that are completely visible will fade out on pointer release.\n            // If the `terminateOnPointerUp` option is set, ripples that still fade in will also fade out.\n            const isVisible = ripple.state === 1 /* RippleState.VISIBLE */ ||\n                (ripple.config.terminateOnPointerUp && ripple.state === 0 /* RippleState.FADING_IN */);\n            if (!ripple.config.persistent && isVisible) {\n                ripple.fadeOut();\n            }\n        });\n    }\n    _getActiveRipples() {\n        return Array.from(this._activeRipples.keys());\n    }\n    /** Removes previously registered event listeners from the trigger element. */\n    _removeTriggerEvents() {\n        const trigger = this._triggerElement;\n        if (trigger) {\n            pointerDownEvents.forEach(type => RippleRenderer._eventManager.removeHandler(type, trigger, this));\n            if (this._pointerUpEventsRegistered) {\n                pointerUpEvents.forEach(type => trigger.removeEventListener(type, this, passiveCapturingEventOptions));\n            }\n        }\n    }\n}\n/**\n * Returns the distance from the point (x, y) to the furthest corner of a rectangle.\n */\nfunction distanceToFurthestCorner(x, y, rect) {\n    const distX = Math.max(Math.abs(x - rect.left), Math.abs(x - rect.right));\n    const distY = Math.max(Math.abs(y - rect.top), Math.abs(y - rect.bottom));\n    return Math.sqrt(distX * distX + distY * distY);\n}\n\n/** Injection token that can be used to specify the global ripple options. */\nconst MAT_RIPPLE_GLOBAL_OPTIONS = new InjectionToken('mat-ripple-global-options');\nclass MatRipple {\n    /**\n     * Whether click events will not trigger the ripple. Ripples can be still launched manually\n     * by using the `launch()` method.\n     */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(value) {\n        if (value) {\n            this.fadeOutAllNonPersistent();\n        }\n        this._disabled = value;\n        this._setupTriggerEventsIfEnabled();\n    }\n    /**\n     * The element that triggers the ripple when click events are received.\n     * Defaults to the directive's host element.\n     */\n    get trigger() {\n        return this._trigger || this._elementRef.nativeElement;\n    }\n    set trigger(trigger) {\n        this._trigger = trigger;\n        this._setupTriggerEventsIfEnabled();\n    }\n    constructor(_elementRef, ngZone, platform, globalOptions, _animationMode) {\n        this._elementRef = _elementRef;\n        this._animationMode = _animationMode;\n        /**\n         * If set, the radius in pixels of foreground ripples when fully expanded. If unset, the radius\n         * will be the distance from the center of the ripple to the furthest corner of the host element's\n         * bounding rectangle.\n         */\n        this.radius = 0;\n        this._disabled = false;\n        /** @docs-private Whether ripple directive is initialized and the input bindings are set. */\n        this._isInitialized = false;\n        this._globalOptions = globalOptions || {};\n        this._rippleRenderer = new RippleRenderer(this, ngZone, _elementRef, platform);\n    }\n    ngOnInit() {\n        this._isInitialized = true;\n        this._setupTriggerEventsIfEnabled();\n    }\n    ngOnDestroy() {\n        this._rippleRenderer._removeTriggerEvents();\n    }\n    /** Fades out all currently showing ripple elements. */\n    fadeOutAll() {\n        this._rippleRenderer.fadeOutAll();\n    }\n    /** Fades out all currently showing non-persistent ripple elements. */\n    fadeOutAllNonPersistent() {\n        this._rippleRenderer.fadeOutAllNonPersistent();\n    }\n    /**\n     * Ripple configuration from the directive's input values.\n     * @docs-private Implemented as part of RippleTarget\n     */\n    get rippleConfig() {\n        return {\n            centered: this.centered,\n            radius: this.radius,\n            color: this.color,\n            animation: {\n                ...this._globalOptions.animation,\n                ...(this._animationMode === 'NoopAnimations' ? { enterDuration: 0, exitDuration: 0 } : {}),\n                ...this.animation,\n            },\n            terminateOnPointerUp: this._globalOptions.terminateOnPointerUp,\n        };\n    }\n    /**\n     * Whether ripples on pointer-down are disabled or not.\n     * @docs-private Implemented as part of RippleTarget\n     */\n    get rippleDisabled() {\n        return this.disabled || !!this._globalOptions.disabled;\n    }\n    /** Sets up the trigger event listeners if ripples are enabled. */\n    _setupTriggerEventsIfEnabled() {\n        if (!this.disabled && this._isInitialized) {\n            this._rippleRenderer.setupTriggerEvents(this.trigger);\n        }\n    }\n    /** Launches a manual ripple at the specified coordinated or just by the ripple config. */\n    launch(configOrX, y = 0, config) {\n        if (typeof configOrX === 'number') {\n            return this._rippleRenderer.fadeInRipple(configOrX, y, { ...this.rippleConfig, ...config });\n        }\n        else {\n            return this._rippleRenderer.fadeInRipple(0, 0, { ...this.rippleConfig, ...configOrX });\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatRipple, deps: [{ token: i0.ElementRef }, { token: i0.NgZone }, { token: i1$1.Platform }, { token: MAT_RIPPLE_GLOBAL_OPTIONS, optional: true }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: { color: [\"matRippleColor\", \"color\"], unbounded: [\"matRippleUnbounded\", \"unbounded\"], centered: [\"matRippleCentered\", \"centered\"], radius: [\"matRippleRadius\", \"radius\"], animation: [\"matRippleAnimation\", \"animation\"], disabled: [\"matRippleDisabled\", \"disabled\"], trigger: [\"matRippleTrigger\", \"trigger\"] }, host: { properties: { \"class.mat-ripple-unbounded\": \"unbounded\" }, classAttribute: \"mat-ripple\" }, exportAs: [\"matRipple\"], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatRipple, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-ripple], [matRipple]',\n                    exportAs: 'matRipple',\n                    host: {\n                        'class': 'mat-ripple',\n                        '[class.mat-ripple-unbounded]': 'unbounded',\n                    },\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.NgZone }, { type: i1$1.Platform }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_RIPPLE_GLOBAL_OPTIONS]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }]; }, propDecorators: { color: [{\n                type: Input,\n                args: ['matRippleColor']\n            }], unbounded: [{\n                type: Input,\n                args: ['matRippleUnbounded']\n            }], centered: [{\n                type: Input,\n                args: ['matRippleCentered']\n            }], radius: [{\n                type: Input,\n                args: ['matRippleRadius']\n            }], animation: [{\n                type: Input,\n                args: ['matRippleAnimation']\n            }], disabled: [{\n                type: Input,\n                args: ['matRippleDisabled']\n            }], trigger: [{\n                type: Input,\n                args: ['matRippleTrigger']\n            }] } });\n\nclass MatRippleModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatRippleModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.1.1\", ngImport: i0, type: MatRippleModule, declarations: [MatRipple], imports: [MatCommonModule], exports: [MatRipple, MatCommonModule] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatRippleModule, imports: [MatCommonModule, MatCommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatRippleModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule],\n                    exports: [MatRipple, MatCommonModule],\n                    declarations: [MatRipple],\n                }]\n        }] });\n\n/**\n * Component that shows a simplified checkbox without including any kind of \"real\" checkbox.\n * Meant to be used when the checkbox is purely decorative and a large number of them will be\n * included, such as for the options in a multi-select. Uses no SVGs or complex animations.\n * Note that theming is meant to be handled by the parent element, e.g.\n * `mat-primary .mat-pseudo-checkbox`.\n *\n * Note that this component will be completely invisible to screen-reader users. This is *not*\n * interchangeable with `<mat-checkbox>` and should *not* be used if the user would directly\n * interact with the checkbox. The pseudo-checkbox should only be used as an implementation detail\n * of more complex components that appropriately handle selected / checked state.\n * @docs-private\n */\nclass MatPseudoCheckbox {\n    constructor(_animationMode) {\n        this._animationMode = _animationMode;\n        /** Display state of the checkbox. */\n        this.state = 'unchecked';\n        /** Whether the checkbox is disabled. */\n        this.disabled = false;\n        /**\n         * Appearance of the pseudo checkbox. Default appearance of 'full' renders a checkmark/mixedmark\n         * indicator inside a square box. 'minimal' appearance only renders the checkmark/mixedmark.\n         */\n        this.appearance = 'full';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatPseudoCheckbox, deps: [{ token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatPseudoCheckbox, selector: \"mat-pseudo-checkbox\", inputs: { state: \"state\", disabled: \"disabled\", appearance: \"appearance\" }, host: { properties: { \"class.mat-pseudo-checkbox-indeterminate\": \"state === \\\"indeterminate\\\"\", \"class.mat-pseudo-checkbox-checked\": \"state === \\\"checked\\\"\", \"class.mat-pseudo-checkbox-disabled\": \"disabled\", \"class.mat-pseudo-checkbox-minimal\": \"appearance === \\\"minimal\\\"\", \"class.mat-pseudo-checkbox-full\": \"appearance === \\\"full\\\"\", \"class._mat-animation-noopable\": \"_animationMode === \\\"NoopAnimations\\\"\" }, classAttribute: \"mat-pseudo-checkbox\" }, ngImport: i0, template: '', isInline: true, styles: [\".mat-pseudo-checkbox{border-radius:2px;cursor:pointer;display:inline-block;vertical-align:middle;box-sizing:border-box;position:relative;flex-shrink:0;transition:border-color 90ms cubic-bezier(0, 0, 0.2, 0.1),background-color 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox::after{position:absolute;opacity:0;content:\\\"\\\";border-bottom:2px solid currentColor;transition:opacity 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox._mat-animation-noopable{transition:none !important;animation:none !important}.mat-pseudo-checkbox._mat-animation-noopable::after{transition:none}.mat-pseudo-checkbox-disabled{cursor:default}.mat-pseudo-checkbox-indeterminate::after{left:1px;opacity:1;border-radius:2px}.mat-pseudo-checkbox-checked::after{left:1px;border-left:2px solid currentColor;transform:rotate(-45deg);opacity:1;box-sizing:content-box}.mat-pseudo-checkbox-full{border:2px solid}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate{border-color:rgba(0,0,0,0)}.mat-pseudo-checkbox{width:18px;height:18px}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after{width:14px;height:6px;transform-origin:center;top:-4.2426406871px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{top:8px;width:16px}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked::after{width:10px;height:4px;transform-origin:center;top:-2.8284271247px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate::after{top:6px;width:12px}\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatPseudoCheckbox, decorators: [{\n            type: Component,\n            args: [{ encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, selector: 'mat-pseudo-checkbox', template: '', host: {\n                        'class': 'mat-pseudo-checkbox',\n                        '[class.mat-pseudo-checkbox-indeterminate]': 'state === \"indeterminate\"',\n                        '[class.mat-pseudo-checkbox-checked]': 'state === \"checked\"',\n                        '[class.mat-pseudo-checkbox-disabled]': 'disabled',\n                        '[class.mat-pseudo-checkbox-minimal]': 'appearance === \"minimal\"',\n                        '[class.mat-pseudo-checkbox-full]': 'appearance === \"full\"',\n                        '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n                    }, styles: [\".mat-pseudo-checkbox{border-radius:2px;cursor:pointer;display:inline-block;vertical-align:middle;box-sizing:border-box;position:relative;flex-shrink:0;transition:border-color 90ms cubic-bezier(0, 0, 0.2, 0.1),background-color 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox::after{position:absolute;opacity:0;content:\\\"\\\";border-bottom:2px solid currentColor;transition:opacity 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox._mat-animation-noopable{transition:none !important;animation:none !important}.mat-pseudo-checkbox._mat-animation-noopable::after{transition:none}.mat-pseudo-checkbox-disabled{cursor:default}.mat-pseudo-checkbox-indeterminate::after{left:1px;opacity:1;border-radius:2px}.mat-pseudo-checkbox-checked::after{left:1px;border-left:2px solid currentColor;transform:rotate(-45deg);opacity:1;box-sizing:content-box}.mat-pseudo-checkbox-full{border:2px solid}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate{border-color:rgba(0,0,0,0)}.mat-pseudo-checkbox{width:18px;height:18px}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after{width:14px;height:6px;transform-origin:center;top:-4.2426406871px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{top:8px;width:16px}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked::after{width:10px;height:4px;transform-origin:center;top:-2.8284271247px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate::after{top:6px;width:12px}\"] }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }]; }, propDecorators: { state: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], appearance: [{\n                type: Input\n            }] } });\n\nclass MatPseudoCheckboxModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatPseudoCheckboxModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.1.1\", ngImport: i0, type: MatPseudoCheckboxModule, declarations: [MatPseudoCheckbox], imports: [MatCommonModule], exports: [MatPseudoCheckbox] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatPseudoCheckboxModule, imports: [MatCommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatPseudoCheckboxModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule],\n                    exports: [MatPseudoCheckbox],\n                    declarations: [MatPseudoCheckbox],\n                }]\n        }] });\n\n/**\n * Injection token used to provide the parent component to options.\n */\nconst MAT_OPTION_PARENT_COMPONENT = new InjectionToken('MAT_OPTION_PARENT_COMPONENT');\n\n// Notes on the accessibility pattern used for `mat-optgroup`.\n// The option group has two different \"modes\": regular and inert. The regular mode uses the\n// recommended a11y pattern which has `role=\"group\"` on the group element with `aria-labelledby`\n// pointing to the label. This works for `mat-select`, but it seems to hit a bug for autocomplete\n// under VoiceOver where the group doesn't get read out at all. The bug appears to be that if\n// there's __any__ a11y-related attribute on the group (e.g. `role` or `aria-labelledby`),\n// VoiceOver on Safari won't read it out.\n// We've introduced the `inert` mode as a workaround. Under this mode, all a11y attributes are\n// removed from the group, and we get the screen reader to read out the group label by mirroring it\n// inside an invisible element in the option. This is sub-optimal, because the screen reader will\n// repeat the group label on each navigation, whereas the default pattern only reads the group when\n// the user enters a new group. The following alternate approaches were considered:\n// 1. Reading out the group label using the `LiveAnnouncer` solves the problem, but we can't control\n//    when the text will be read out so sometimes it comes in too late or never if the user\n//    navigates quickly.\n// 2. `<mat-option aria-describedby=\"groupLabel\"` - This works on Safari, but VoiceOver in Chrome\n//    won't read out the description at all.\n// 3. `<mat-option aria-labelledby=\"optionLabel groupLabel\"` - This works on Chrome, but Safari\n//     doesn't read out the text at all. Furthermore, on\n// Boilerplate for applying mixins to MatOptgroup.\n/** @docs-private */\nconst _MatOptgroupMixinBase = mixinDisabled(class {\n});\n// Counter for unique group ids.\nlet _uniqueOptgroupIdCounter = 0;\nclass _MatOptgroupBase extends _MatOptgroupMixinBase {\n    constructor(parent) {\n        super();\n        /** Unique id for the underlying label. */\n        this._labelId = `mat-optgroup-label-${_uniqueOptgroupIdCounter++}`;\n        this._inert = parent?.inertGroups ?? false;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatOptgroupBase, deps: [{ token: MAT_OPTION_PARENT_COMPONENT, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: _MatOptgroupBase, inputs: { label: \"label\" }, usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatOptgroupBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_OPTION_PARENT_COMPONENT]\n                }, {\n                    type: Optional\n                }] }]; }, propDecorators: { label: [{\n                type: Input\n            }] } });\n/**\n * Injection token that can be used to reference instances of `MatOptgroup`. It serves as\n * alternative token to the actual `MatOptgroup` class which could cause unnecessary\n * retention of the class and its component metadata.\n */\nconst MAT_OPTGROUP = new InjectionToken('MatOptgroup');\n/**\n * Component that is used to group instances of `mat-option`.\n */\nclass MatOptgroup extends _MatOptgroupBase {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatOptgroup, deps: null, target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatOptgroup, selector: \"mat-optgroup\", inputs: { disabled: \"disabled\" }, host: { properties: { \"attr.role\": \"_inert ? null : \\\"group\\\"\", \"attr.aria-disabled\": \"_inert ? null : disabled.toString()\", \"attr.aria-labelledby\": \"_inert ? null : _labelId\" }, classAttribute: \"mat-mdc-optgroup\" }, providers: [{ provide: MAT_OPTGROUP, useExisting: MatOptgroup }], exportAs: [\"matOptgroup\"], usesInheritance: true, ngImport: i0, template: \"<span\\n  class=\\\"mat-mdc-optgroup-label\\\"\\n  role=\\\"presentation\\\"\\n  [class.mdc-list-item--disabled]=\\\"disabled\\\"\\n  [id]=\\\"_labelId\\\">\\n  <span class=\\\"mdc-list-item__primary-text\\\">{{ label }} <ng-content></ng-content></span>\\n</span>\\n\\n<ng-content select=\\\"mat-option, ng-container\\\"></ng-content>\\n\", styles: [\".mat-mdc-optgroup{color:var(--mat-optgroup-label-text-color);font-family:var(--mat-optgroup-label-text-font);line-height:var(--mat-optgroup-label-text-line-height);font-size:var(--mat-optgroup-label-text-size);letter-spacing:var(--mat-optgroup-label-text-tracking);font-weight:var(--mat-optgroup-label-text-weight)}.mat-mdc-optgroup-label{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:16px;padding-right:16px;min-height:48px}.mat-mdc-optgroup-label:focus{outline:none}[dir=rtl] .mat-mdc-optgroup-label,.mat-mdc-optgroup-label[dir=rtl]{padding-left:16px;padding-right:16px}.mat-mdc-optgroup-label.mdc-list-item--disabled{opacity:.38}.mat-mdc-optgroup-label .mdc-list-item__primary-text{font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;white-space:normal}\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatOptgroup, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-optgroup', exportAs: 'matOptgroup', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, inputs: ['disabled'], host: {\n                        'class': 'mat-mdc-optgroup',\n                        '[attr.role]': '_inert ? null : \"group\"',\n                        '[attr.aria-disabled]': '_inert ? null : disabled.toString()',\n                        '[attr.aria-labelledby]': '_inert ? null : _labelId',\n                    }, providers: [{ provide: MAT_OPTGROUP, useExisting: MatOptgroup }], template: \"<span\\n  class=\\\"mat-mdc-optgroup-label\\\"\\n  role=\\\"presentation\\\"\\n  [class.mdc-list-item--disabled]=\\\"disabled\\\"\\n  [id]=\\\"_labelId\\\">\\n  <span class=\\\"mdc-list-item__primary-text\\\">{{ label }} <ng-content></ng-content></span>\\n</span>\\n\\n<ng-content select=\\\"mat-option, ng-container\\\"></ng-content>\\n\", styles: [\".mat-mdc-optgroup{color:var(--mat-optgroup-label-text-color);font-family:var(--mat-optgroup-label-text-font);line-height:var(--mat-optgroup-label-text-line-height);font-size:var(--mat-optgroup-label-text-size);letter-spacing:var(--mat-optgroup-label-text-tracking);font-weight:var(--mat-optgroup-label-text-weight)}.mat-mdc-optgroup-label{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:16px;padding-right:16px;min-height:48px}.mat-mdc-optgroup-label:focus{outline:none}[dir=rtl] .mat-mdc-optgroup-label,.mat-mdc-optgroup-label[dir=rtl]{padding-left:16px;padding-right:16px}.mat-mdc-optgroup-label.mdc-list-item--disabled{opacity:.38}.mat-mdc-optgroup-label .mdc-list-item__primary-text{font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;white-space:normal}\"] }]\n        }] });\n\n/**\n * Option IDs need to be unique across components, so this counter exists outside of\n * the component definition.\n */\nlet _uniqueIdCounter = 0;\n/** Event object emitted by MatOption when selected or deselected. */\nclass MatOptionSelectionChange {\n    constructor(\n    /** Reference to the option that emitted the event. */\n    source, \n    /** Whether the change in the option's value was a result of a user action. */\n    isUserInput = false) {\n        this.source = source;\n        this.isUserInput = isUserInput;\n    }\n}\nclass _MatOptionBase {\n    /** Whether the wrapping component is in multiple selection mode. */\n    get multiple() {\n        return this._parent && this._parent.multiple;\n    }\n    /** Whether or not the option is currently selected. */\n    get selected() {\n        return this._selected;\n    }\n    /** Whether the option is disabled. */\n    get disabled() {\n        return (this.group && this.group.disabled) || this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = coerceBooleanProperty(value);\n    }\n    /** Whether ripples for the option are disabled. */\n    get disableRipple() {\n        return !!(this._parent && this._parent.disableRipple);\n    }\n    /** Whether to display checkmark for single-selection. */\n    get hideSingleSelectionIndicator() {\n        return !!(this._parent && this._parent.hideSingleSelectionIndicator);\n    }\n    constructor(_element, _changeDetectorRef, _parent, group) {\n        this._element = _element;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._parent = _parent;\n        this.group = group;\n        this._selected = false;\n        this._active = false;\n        this._disabled = false;\n        this._mostRecentViewValue = '';\n        /** The unique ID of the option. */\n        this.id = `mat-option-${_uniqueIdCounter++}`;\n        /** Event emitted when the option is selected or deselected. */\n        // tslint:disable-next-line:no-output-on-prefix\n        this.onSelectionChange = new EventEmitter();\n        /** Emits when the state of the option changes and any parents have to be notified. */\n        this._stateChanges = new Subject();\n    }\n    /**\n     * Whether or not the option is currently active and ready to be selected.\n     * An active option displays styles as if it is focused, but the\n     * focus is actually retained somewhere else. This comes in handy\n     * for components like autocomplete where focus must remain on the input.\n     */\n    get active() {\n        return this._active;\n    }\n    /**\n     * The displayed value of the option. It is necessary to show the selected option in the\n     * select's trigger.\n     */\n    get viewValue() {\n        // TODO(kara): Add input property alternative for node envs.\n        return (this._text?.nativeElement.textContent || '').trim();\n    }\n    /** Selects the option. */\n    select(emitEvent = true) {\n        if (!this._selected) {\n            this._selected = true;\n            this._changeDetectorRef.markForCheck();\n            if (emitEvent) {\n                this._emitSelectionChangeEvent();\n            }\n        }\n    }\n    /** Deselects the option. */\n    deselect(emitEvent = true) {\n        if (this._selected) {\n            this._selected = false;\n            this._changeDetectorRef.markForCheck();\n            if (emitEvent) {\n                this._emitSelectionChangeEvent();\n            }\n        }\n    }\n    /** Sets focus onto this option. */\n    focus(_origin, options) {\n        // Note that we aren't using `_origin`, but we need to keep it because some internal consumers\n        // use `MatOption` in a `FocusKeyManager` and we need it to match `FocusableOption`.\n        const element = this._getHostElement();\n        if (typeof element.focus === 'function') {\n            element.focus(options);\n        }\n    }\n    /**\n     * This method sets display styles on the option to make it appear\n     * active. This is used by the ActiveDescendantKeyManager so key\n     * events will display the proper options as active on arrow key events.\n     */\n    setActiveStyles() {\n        if (!this._active) {\n            this._active = true;\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    /**\n     * This method removes display styles on the option that made it appear\n     * active. This is used by the ActiveDescendantKeyManager so key\n     * events will display the proper options as active on arrow key events.\n     */\n    setInactiveStyles() {\n        if (this._active) {\n            this._active = false;\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    /** Gets the label to be used when determining whether the option should be focused. */\n    getLabel() {\n        return this.viewValue;\n    }\n    /** Ensures the option is selected when activated from the keyboard. */\n    _handleKeydown(event) {\n        if ((event.keyCode === ENTER || event.keyCode === SPACE) && !hasModifierKey(event)) {\n            this._selectViaInteraction();\n            // Prevent the page from scrolling down and form submits.\n            event.preventDefault();\n        }\n    }\n    /**\n     * `Selects the option while indicating the selection came from the user. Used to\n     * determine if the select's view -> model callback should be invoked.`\n     */\n    _selectViaInteraction() {\n        if (!this.disabled) {\n            this._selected = this.multiple ? !this._selected : true;\n            this._changeDetectorRef.markForCheck();\n            this._emitSelectionChangeEvent(true);\n        }\n    }\n    /** Returns the correct tabindex for the option depending on disabled state. */\n    // This method is only used by `MatLegacyOption`. Keeping it here to avoid breaking the types.\n    // That's because `MatLegacyOption` use `MatOption` type in a few places such as\n    // `MatOptionSelectionChange`. It is safe to delete this when `MatLegacyOption` is deleted.\n    _getTabIndex() {\n        return this.disabled ? '-1' : '0';\n    }\n    /** Gets the host DOM element. */\n    _getHostElement() {\n        return this._element.nativeElement;\n    }\n    ngAfterViewChecked() {\n        // Since parent components could be using the option's label to display the selected values\n        // (e.g. `mat-select`) and they don't have a way of knowing if the option's label has changed\n        // we have to check for changes in the DOM ourselves and dispatch an event. These checks are\n        // relatively cheap, however we still limit them only to selected options in order to avoid\n        // hitting the DOM too often.\n        if (this._selected) {\n            const viewValue = this.viewValue;\n            if (viewValue !== this._mostRecentViewValue) {\n                if (this._mostRecentViewValue) {\n                    this._stateChanges.next();\n                }\n                this._mostRecentViewValue = viewValue;\n            }\n        }\n    }\n    ngOnDestroy() {\n        this._stateChanges.complete();\n    }\n    /** Emits the selection change event. */\n    _emitSelectionChangeEvent(isUserInput = false) {\n        this.onSelectionChange.emit(new MatOptionSelectionChange(this, isUserInput));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatOptionBase, deps: \"invalid\", target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: _MatOptionBase, inputs: { value: \"value\", id: \"id\", disabled: \"disabled\" }, outputs: { onSelectionChange: \"onSelectionChange\" }, viewQueries: [{ propertyName: \"_text\", first: true, predicate: [\"text\"], descendants: true, static: true }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatOptionBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: undefined }, { type: _MatOptgroupBase }]; }, propDecorators: { value: [{\n                type: Input\n            }], id: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], onSelectionChange: [{\n                type: Output\n            }], _text: [{\n                type: ViewChild,\n                args: ['text', { static: true }]\n            }] } });\n/**\n * Single option inside of a `<mat-select>` element.\n */\nclass MatOption extends _MatOptionBase {\n    constructor(element, changeDetectorRef, parent, group) {\n        super(element, changeDetectorRef, parent, group);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatOption, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: MAT_OPTION_PARENT_COMPONENT, optional: true }, { token: MAT_OPTGROUP, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatOption, selector: \"mat-option\", host: { attributes: { \"role\": \"option\" }, listeners: { \"click\": \"_selectViaInteraction()\", \"keydown\": \"_handleKeydown($event)\" }, properties: { \"class.mdc-list-item--selected\": \"selected\", \"class.mat-mdc-option-multiple\": \"multiple\", \"class.mat-mdc-option-active\": \"active\", \"class.mdc-list-item--disabled\": \"disabled\", \"id\": \"id\", \"attr.aria-selected\": \"selected\", \"attr.aria-disabled\": \"disabled.toString()\" }, classAttribute: \"mat-mdc-option mdc-list-item\" }, exportAs: [\"matOption\"], usesInheritance: true, ngImport: i0, template: \"<!-- Set aria-hidden=\\\"true\\\" to this DOM node and other decorative nodes in this file. This might\\n be contributing to issue where sometimes VoiceOver focuses on a TextNode in the a11y tree instead\\n of the Option node (#23202). Most assistive technology will generally ignore non-role,\\n non-text-content elements. Adding aria-hidden seems to make VoiceOver behave more consistently. -->\\n<mat-pseudo-checkbox *ngIf=\\\"multiple\\\" class=\\\"mat-mdc-option-pseudo-checkbox\\\" [disabled]=\\\"disabled\\\"\\n    [state]=\\\"selected ? 'checked' : 'unchecked'\\\" aria-hidden=\\\"true\\\"></mat-pseudo-checkbox>\\n\\n<ng-content select=\\\"mat-icon\\\"></ng-content>\\n\\n<span class=\\\"mdc-list-item__primary-text\\\" #text><ng-content></ng-content></span>\\n\\n<!-- Render checkmark at the end for single-selection. -->\\n<mat-pseudo-checkbox *ngIf=\\\"!multiple && selected && !hideSingleSelectionIndicator\\\"\\n    class=\\\"mat-mdc-option-pseudo-checkbox\\\" [disabled]=\\\"disabled\\\" state=\\\"checked\\\"\\n    aria-hidden=\\\"true\\\" appearance=\\\"minimal\\\"></mat-pseudo-checkbox>\\n\\n<!-- See a11y notes inside optgroup.ts for context behind this element. -->\\n<span class=\\\"cdk-visually-hidden\\\" *ngIf=\\\"group && group._inert\\\">({{ group.label }})</span>\\n\\n<div class=\\\"mat-mdc-option-ripple mat-mdc-focus-indicator\\\" aria-hidden=\\\"true\\\" mat-ripple\\n     [matRippleTrigger]=\\\"_getHostElement()\\\" [matRippleDisabled]=\\\"disabled || disableRipple\\\">\\n</div>\\n\", styles: [\".mat-mdc-option{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:16px;padding-right:16px;-webkit-user-select:none;user-select:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;cursor:pointer;-webkit-tap-highlight-color:rgba(0,0,0,0);color:var(--mat-option-label-text-color);font-family:var(--mat-option-label-text-font);line-height:var(--mat-option-label-text-line-height);font-size:var(--mat-option-label-text-size);letter-spacing:var(--mat-option-label-text-tracking);font-weight:var(--mat-option-label-text-weight);min-height:48px}.mat-mdc-option:focus{outline:none}[dir=rtl] .mat-mdc-option,.mat-mdc-option[dir=rtl]{padding-left:16px;padding-right:16px}.mat-mdc-option:hover:not(.mdc-list-item--disabled){background-color:var(--mat-option-hover-state-layer-color)}.mat-mdc-option:focus.mdc-list-item,.mat-mdc-option.mat-mdc-option-active.mdc-list-item{background-color:var(--mat-option-focus-state-layer-color)}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled) .mdc-list-item__primary-text{color:var(--mat-option-selected-state-label-text-color)}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-mdc-option-multiple){background-color:var(--mat-option-selected-state-layer-color)}.mat-mdc-option.mdc-list-item{align-items:center}.mat-mdc-option.mdc-list-item--disabled{cursor:default;pointer-events:none}.mat-mdc-option.mdc-list-item--disabled .mat-mdc-option-pseudo-checkbox,.mat-mdc-option.mdc-list-item--disabled .mdc-list-item__primary-text,.mat-mdc-option.mdc-list-item--disabled>mat-icon{opacity:.38}.mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:32px}[dir=rtl] .mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:16px;padding-right:32px}.mat-mdc-option .mat-icon,.mat-mdc-option .mat-pseudo-checkbox-full{margin-right:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-icon,[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-full{margin-right:0;margin-left:16px}.mat-mdc-option .mat-pseudo-checkbox-minimal{margin-left:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-minimal{margin-right:16px;margin-left:0}.mat-mdc-option .mat-mdc-option-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-option .mdc-list-item__primary-text{white-space:normal;font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;margin-right:auto}[dir=rtl] .mat-mdc-option .mdc-list-item__primary-text{margin-right:0;margin-left:auto}.cdk-high-contrast-active .mat-mdc-option.mdc-list-item--selected:not(.mat-mdc-option-multiple)::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}[dir=rtl] .cdk-high-contrast-active .mat-mdc-option.mdc-list-item--selected:not(.mat-mdc-option-multiple)::after{right:auto;left:16px}.mat-mdc-option-active .mat-mdc-focus-indicator::before{content:\\\"\\\"}\"], dependencies: [{ kind: \"directive\", type: MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }, { kind: \"directive\", type: i3.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"component\", type: MatPseudoCheckbox, selector: \"mat-pseudo-checkbox\", inputs: [\"state\", \"disabled\", \"appearance\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatOption, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-option', exportAs: 'matOption', host: {\n                        'role': 'option',\n                        '[class.mdc-list-item--selected]': 'selected',\n                        '[class.mat-mdc-option-multiple]': 'multiple',\n                        '[class.mat-mdc-option-active]': 'active',\n                        '[class.mdc-list-item--disabled]': 'disabled',\n                        '[id]': 'id',\n                        // Set aria-selected to false for non-selected items and true for selected items. Conform to\n                        // [WAI ARIA Listbox authoring practices guide](\n                        //  https://www.w3.org/WAI/ARIA/apg/patterns/listbox/), \"If any options are selected, each\n                        // selected option has either aria-selected or aria-checked  set to true. All options that are\n                        // selectable but not selected have either aria-selected or aria-checked set to false.\" Align\n                        // aria-selected implementation of Chips and List components.\n                        //\n                        // Set `aria-selected=\"false\"` on not-selected listbox options to fix VoiceOver announcing\n                        // every option as \"selected\" (#21491).\n                        '[attr.aria-selected]': 'selected',\n                        '[attr.aria-disabled]': 'disabled.toString()',\n                        '(click)': '_selectViaInteraction()',\n                        '(keydown)': '_handleKeydown($event)',\n                        'class': 'mat-mdc-option mdc-list-item',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, template: \"<!-- Set aria-hidden=\\\"true\\\" to this DOM node and other decorative nodes in this file. This might\\n be contributing to issue where sometimes VoiceOver focuses on a TextNode in the a11y tree instead\\n of the Option node (#23202). Most assistive technology will generally ignore non-role,\\n non-text-content elements. Adding aria-hidden seems to make VoiceOver behave more consistently. -->\\n<mat-pseudo-checkbox *ngIf=\\\"multiple\\\" class=\\\"mat-mdc-option-pseudo-checkbox\\\" [disabled]=\\\"disabled\\\"\\n    [state]=\\\"selected ? 'checked' : 'unchecked'\\\" aria-hidden=\\\"true\\\"></mat-pseudo-checkbox>\\n\\n<ng-content select=\\\"mat-icon\\\"></ng-content>\\n\\n<span class=\\\"mdc-list-item__primary-text\\\" #text><ng-content></ng-content></span>\\n\\n<!-- Render checkmark at the end for single-selection. -->\\n<mat-pseudo-checkbox *ngIf=\\\"!multiple && selected && !hideSingleSelectionIndicator\\\"\\n    class=\\\"mat-mdc-option-pseudo-checkbox\\\" [disabled]=\\\"disabled\\\" state=\\\"checked\\\"\\n    aria-hidden=\\\"true\\\" appearance=\\\"minimal\\\"></mat-pseudo-checkbox>\\n\\n<!-- See a11y notes inside optgroup.ts for context behind this element. -->\\n<span class=\\\"cdk-visually-hidden\\\" *ngIf=\\\"group && group._inert\\\">({{ group.label }})</span>\\n\\n<div class=\\\"mat-mdc-option-ripple mat-mdc-focus-indicator\\\" aria-hidden=\\\"true\\\" mat-ripple\\n     [matRippleTrigger]=\\\"_getHostElement()\\\" [matRippleDisabled]=\\\"disabled || disableRipple\\\">\\n</div>\\n\", styles: [\".mat-mdc-option{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:16px;padding-right:16px;-webkit-user-select:none;user-select:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;cursor:pointer;-webkit-tap-highlight-color:rgba(0,0,0,0);color:var(--mat-option-label-text-color);font-family:var(--mat-option-label-text-font);line-height:var(--mat-option-label-text-line-height);font-size:var(--mat-option-label-text-size);letter-spacing:var(--mat-option-label-text-tracking);font-weight:var(--mat-option-label-text-weight);min-height:48px}.mat-mdc-option:focus{outline:none}[dir=rtl] .mat-mdc-option,.mat-mdc-option[dir=rtl]{padding-left:16px;padding-right:16px}.mat-mdc-option:hover:not(.mdc-list-item--disabled){background-color:var(--mat-option-hover-state-layer-color)}.mat-mdc-option:focus.mdc-list-item,.mat-mdc-option.mat-mdc-option-active.mdc-list-item{background-color:var(--mat-option-focus-state-layer-color)}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled) .mdc-list-item__primary-text{color:var(--mat-option-selected-state-label-text-color)}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-mdc-option-multiple){background-color:var(--mat-option-selected-state-layer-color)}.mat-mdc-option.mdc-list-item{align-items:center}.mat-mdc-option.mdc-list-item--disabled{cursor:default;pointer-events:none}.mat-mdc-option.mdc-list-item--disabled .mat-mdc-option-pseudo-checkbox,.mat-mdc-option.mdc-list-item--disabled .mdc-list-item__primary-text,.mat-mdc-option.mdc-list-item--disabled>mat-icon{opacity:.38}.mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:32px}[dir=rtl] .mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:16px;padding-right:32px}.mat-mdc-option .mat-icon,.mat-mdc-option .mat-pseudo-checkbox-full{margin-right:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-icon,[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-full{margin-right:0;margin-left:16px}.mat-mdc-option .mat-pseudo-checkbox-minimal{margin-left:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-minimal{margin-right:16px;margin-left:0}.mat-mdc-option .mat-mdc-option-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-option .mdc-list-item__primary-text{white-space:normal;font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;margin-right:auto}[dir=rtl] .mat-mdc-option .mdc-list-item__primary-text{margin-right:0;margin-left:auto}.cdk-high-contrast-active .mat-mdc-option.mdc-list-item--selected:not(.mat-mdc-option-multiple)::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}[dir=rtl] .cdk-high-contrast-active .mat-mdc-option.mdc-list-item--selected:not(.mat-mdc-option-multiple)::after{right:auto;left:16px}.mat-mdc-option-active .mat-mdc-focus-indicator::before{content:\\\"\\\"}\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_OPTION_PARENT_COMPONENT]\n                }] }, { type: MatOptgroup, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_OPTGROUP]\n                }] }]; } });\n/**\n * Counts the amount of option group labels that precede the specified option.\n * @param optionIndex Index of the option at which to start counting.\n * @param options Flat list of all of the options.\n * @param optionGroups Flat list of all of the option groups.\n * @docs-private\n */\nfunction _countGroupLabelsBeforeOption(optionIndex, options, optionGroups) {\n    if (optionGroups.length) {\n        let optionsArray = options.toArray();\n        let groups = optionGroups.toArray();\n        let groupCounter = 0;\n        for (let i = 0; i < optionIndex + 1; i++) {\n            if (optionsArray[i].group && optionsArray[i].group === groups[groupCounter]) {\n                groupCounter++;\n            }\n        }\n        return groupCounter;\n    }\n    return 0;\n}\n/**\n * Determines the position to which to scroll a panel in order for an option to be into view.\n * @param optionOffset Offset of the option from the top of the panel.\n * @param optionHeight Height of the options.\n * @param currentScrollPosition Current scroll position of the panel.\n * @param panelHeight Height of the panel.\n * @docs-private\n */\nfunction _getOptionScrollPosition(optionOffset, optionHeight, currentScrollPosition, panelHeight) {\n    if (optionOffset < currentScrollPosition) {\n        return optionOffset;\n    }\n    if (optionOffset + optionHeight > currentScrollPosition + panelHeight) {\n        return Math.max(0, optionOffset - panelHeight + optionHeight);\n    }\n    return currentScrollPosition;\n}\n\nclass MatOptionModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatOptionModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.1.1\", ngImport: i0, type: MatOptionModule, declarations: [MatOption, MatOptgroup], imports: [MatRippleModule, CommonModule, MatCommonModule, MatPseudoCheckboxModule], exports: [MatOption, MatOptgroup] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatOptionModule, imports: [MatRippleModule, CommonModule, MatCommonModule, MatPseudoCheckboxModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatOptionModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatRippleModule, CommonModule, MatCommonModule, MatPseudoCheckboxModule],\n                    exports: [MatOption, MatOptgroup],\n                    declarations: [MatOption, MatOptgroup],\n                }]\n        }] });\n\n/** The options for the MatRippleLoader's event listeners. */\nconst eventListenerOptions = { capture: true };\n/** The events that should trigger the initialization of the ripple. */\nconst rippleInteractionEvents = ['focus', 'click', 'mouseenter', 'touchstart'];\n/** The attribute attached to a component whose ripple has not yet been initialized. */\nconst matRippleUninitialized = 'mat-ripple-loader-uninitialized';\n/** Additional classes that should be added to the ripple when it is rendered. */\nconst matRippleClassName = 'mat-ripple-loader-class-name';\n/** Whether the ripple should be centered. */\nconst matRippleCentered = 'mat-ripple-loader-centered';\n/** Whether the ripple should be disabled. */\nconst matRippleDisabled = 'mat-ripple-loader-disabled';\n/**\n * Handles attaching ripples on demand.\n *\n * This service allows us to avoid eagerly creating & attaching MatRipples.\n * It works by creating & attaching a ripple only when a component is first interacted with.\n *\n * @docs-private\n */\nclass MatRippleLoader {\n    constructor() {\n        this._document = inject(DOCUMENT, { optional: true });\n        this._animationMode = inject(ANIMATION_MODULE_TYPE$1, { optional: true });\n        this._globalRippleOptions = inject(MAT_RIPPLE_GLOBAL_OPTIONS, { optional: true });\n        this._platform = inject(Platform);\n        this._ngZone = inject(NgZone);\n        this._hosts = new Map();\n        /** Handles creating and attaching component internals when a component it is initially interacted with. */\n        this._onInteraction = (event) => {\n            if (!(event.target instanceof HTMLElement)) {\n                return;\n            }\n            const eventTarget = event.target;\n            // TODO(wagnermaciel): Consider batching these events to improve runtime performance.\n            const element = eventTarget.closest(`[${matRippleUninitialized}]`);\n            if (element) {\n                this._createRipple(element);\n            }\n        };\n        this._ngZone.runOutsideAngular(() => {\n            for (const event of rippleInteractionEvents) {\n                this._document?.addEventListener(event, this._onInteraction, eventListenerOptions);\n            }\n        });\n    }\n    ngOnDestroy() {\n        const hosts = this._hosts.keys();\n        for (const host of hosts) {\n            this.destroyRipple(host);\n        }\n        for (const event of rippleInteractionEvents) {\n            this._document?.removeEventListener(event, this._onInteraction, eventListenerOptions);\n        }\n    }\n    /**\n     * Configures the ripple that will be rendered by the ripple loader.\n     *\n     * Stores the given information about how the ripple should be configured on the host\n     * element so that it can later be retrived & used when the ripple is actually created.\n     */\n    configureRipple(host, config) {\n        // Indicates that the ripple has not yet been rendered for this component.\n        host.setAttribute(matRippleUninitialized, '');\n        // Store the additional class name(s) that should be added to the ripple element.\n        if (config.className || !host.hasAttribute(matRippleClassName)) {\n            host.setAttribute(matRippleClassName, config.className || '');\n        }\n        // Store whether the ripple should be centered.\n        if (config.centered) {\n            host.setAttribute(matRippleCentered, '');\n        }\n        if (config.disabled) {\n            host.setAttribute(matRippleDisabled, '');\n        }\n    }\n    /** Returns the ripple instance for the given host element. */\n    getRipple(host) {\n        const ripple = this._hosts.get(host);\n        return ripple || this._createRipple(host);\n    }\n    /** Sets the disabled state on the ripple instance corresponding to the given host element. */\n    setDisabled(host, disabled) {\n        const ripple = this._hosts.get(host);\n        // If the ripple has already been instantiated, just disable it.\n        if (ripple) {\n            ripple.disabled = disabled;\n            return;\n        }\n        // Otherwise, set an attribute so we know what the\n        // disabled state should be when the ripple is initialized.\n        if (disabled) {\n            host.setAttribute(matRippleDisabled, '');\n        }\n        else {\n            host.removeAttribute(matRippleDisabled);\n        }\n    }\n    /** Creates a MatRipple and appends it to the given element. */\n    _createRipple(host) {\n        if (!this._document) {\n            return;\n        }\n        const existingRipple = this._hosts.get(host);\n        if (existingRipple) {\n            return existingRipple;\n        }\n        // Create the ripple element.\n        host.querySelector('.mat-ripple')?.remove();\n        const rippleEl = this._document.createElement('span');\n        rippleEl.classList.add('mat-ripple', host.getAttribute(matRippleClassName));\n        host.append(rippleEl);\n        // Create the MatRipple.\n        const ripple = new MatRipple(new ElementRef(rippleEl), this._ngZone, this._platform, this._globalRippleOptions ? this._globalRippleOptions : undefined, this._animationMode ? this._animationMode : undefined);\n        ripple._isInitialized = true;\n        ripple.trigger = host;\n        ripple.centered = host.hasAttribute(matRippleCentered);\n        ripple.disabled = host.hasAttribute(matRippleDisabled);\n        this.attachRipple(host, ripple);\n        return ripple;\n    }\n    attachRipple(host, ripple) {\n        host.removeAttribute(matRippleUninitialized);\n        this._hosts.set(host, ripple);\n    }\n    destroyRipple(host) {\n        const ripple = this._hosts.get(host);\n        if (ripple) {\n            // Since this directive is created manually, it needs to be destroyed manually too.\n            // tslint:disable-next-line:no-lifecycle-invocation\n            ripple.ngOnDestroy();\n            this._hosts.delete(host);\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatRippleLoader, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatRippleLoader, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatRippleLoader, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return []; } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AnimationCurves, AnimationDurations, DateAdapter, ErrorStateMatcher, MATERIAL_SANITY_CHECKS, MAT_DATE_FORMATS, MAT_DATE_LOCALE, MAT_DATE_LOCALE_FACTORY, MAT_NATIVE_DATE_FORMATS, MAT_OPTGROUP, MAT_OPTION_PARENT_COMPONENT, MAT_RIPPLE_GLOBAL_OPTIONS, MatCommonModule, MatLine, MatLineModule, MatNativeDateModule, MatOptgroup, MatOption, MatOptionModule, MatOptionSelectionChange, MatPseudoCheckbox, MatPseudoCheckboxModule, MatRipple, MatRippleLoader, MatRippleModule, NativeDateAdapter, NativeDateModule, RippleRef, RippleRenderer, ShowOnDirtyErrorStateMatcher, VERSION, _MatOptgroupBase, _MatOptionBase, _countGroupLabelsBeforeOption, _getOptionScrollPosition, defaultRippleAnimationConfig, mixinColor, mixinDisableRipple, mixinDisabled, mixinErrorState, mixinInitialized, mixinTabIndex, setLines };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,OAAO,EAAEC,cAAc,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,UAAU,EAAEC,SAAS,EAAEC,KAAK,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,YAAY,EAAEC,MAAM,EAAEC,SAAS,EAAEC,qBAAqB,IAAIC,uBAAuB,EAAEC,MAAM,EAAEC,UAAU,QAAQ,eAAe;AAClS,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,SAASC,+BAA+B,EAAEC,gCAAgC,QAAQ,mBAAmB;AACrG,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,SAASC,OAAO,IAAIC,SAAS,QAAQ,cAAc;AACnD,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,QAAQ,EAAEC,YAAY,QAAQ,iBAAiB;AACxD,OAAO,KAAKC,IAAI,MAAM,uBAAuB;AAC7C,SAASC,QAAQ,EAAEC,kBAAkB,EAAEC,+BAA+B,EAAEC,eAAe,QAAQ,uBAAuB;AACtH,SAASC,qBAAqB,EAAEC,oBAAoB,EAAEC,aAAa,QAAQ,uBAAuB;AAClG,SAASC,UAAU,EAAEC,OAAO,QAAQ,MAAM;AAC1C,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASvB,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASwB,KAAK,EAAEC,KAAK,EAAEC,cAAc,QAAQ,uBAAuB;;AAEpE;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,yCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAoEoGhD,EAAE,CAAAkD,SAAA,4BA0gDqoC,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,MAAA,GA1gDxoCnD,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAAqD,UAAA,aAAAF,MAAA,CAAAG,QA0gDqiC,CAAC,UAAAH,MAAA,CAAAI,QAAA,0BAAD,CAAC;EAAA;AAAA;AAAA,SAAAC,yCAAAR,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA1gDxiChD,EAAE,CAAAkD,SAAA,4BA0gDikD,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAS,MAAA,GA1gDpkDzD,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAAqD,UAAA,aAAAI,MAAA,CAAAH,QA0gDu+C,CAAC;EAAA;AAAA;AAAA,SAAAI,0BAAAV,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA1gD1+ChD,EAAE,CAAA2D,cAAA,aA0gDstD,CAAC;IA1gDztD3D,EAAE,CAAA4D,MAAA,EA0gDyuD,CAAC;IA1gD5uD5D,EAAE,CAAA6D,YAAA,CA0gDgvD,CAAC;EAAA;EAAA,IAAAb,EAAA;IAAA,MAAAc,MAAA,GA1gDnvD9D,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAA+D,SAAA,EA0gDyuD,CAAC;IA1gD5uD/D,EAAE,CAAAgE,kBAAA,MAAAF,MAAA,CAAAG,KAAA,CAAAC,KAAA,KA0gDyuD,CAAC;EAAA;AAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AA7kDh1D,MAAM3C,OAAO,GAAG,IAAIxB,OAAO,CAAC,SAAS,CAAC;;AAEtC;AACA,MAAMoE,eAAe,CAAC;EAClB;IAAS,IAAI,CAACC,cAAc,GAAG,6BAA6B;EAAE;EAC9D;IAAS,IAAI,CAACC,kBAAkB,GAAG,6BAA6B;EAAE;EAClE;IAAS,IAAI,CAACC,kBAAkB,GAAG,2BAA2B;EAAE;EAChE;IAAS,IAAI,CAACC,WAAW,GAAG,6BAA6B;EAAE;AAC/D;AACA;AACA,MAAMC,kBAAkB,CAAC;EACrB;IAAS,IAAI,CAACC,OAAO,GAAG,OAAO;EAAE;EACjC;IAAS,IAAI,CAACC,QAAQ,GAAG,OAAO;EAAE;EAClC;IAAS,IAAI,CAACC,OAAO,GAAG,OAAO;EAAE;AACrC;;AAEA;AACA,SAASC,8BAA8BA,CAAA,EAAG;EACtC,OAAO,IAAI;AACf;AACA;AACA,MAAMC,sBAAsB,GAAG,IAAI7E,cAAc,CAAC,mBAAmB,EAAE;EACnE8E,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEH;AACb,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,eAAe,CAAC;EAClBC,WAAWA,CAACC,wBAAwB,EAAEC,aAAa,EAAEC,SAAS,EAAE;IAC5D,IAAI,CAACD,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B;IACA,IAAI,CAACC,oBAAoB,GAAG,KAAK;IACjC;IACA;IACAH,wBAAwB,CAACI,oCAAoC,CAAC,CAAC;IAC/D,IAAI,CAAC,IAAI,CAACD,oBAAoB,EAAE;MAC5B,IAAI,CAACA,oBAAoB,GAAG,IAAI;MAChC,IAAI,OAAOE,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;QAC/C;QACA,MAAMC,QAAQ,GAAGvF,MAAM,CAAC4B,QAAQ,EAAE;UAAE4D,QAAQ,EAAE;QAAK,CAAC,CAAC;QACrD,IAAI,IAAI,CAACC,eAAe,CAAC,SAAS,CAAC,EAAE;UACjCC,sBAAsB,CAAC,IAAI,CAACP,SAAS,CAAC;QAC1C;QACA,IAAI,IAAI,CAACM,eAAe,CAAC,OAAO,CAAC,EAAE;UAC/BE,oBAAoB,CAAC,IAAI,CAACR,SAAS,EAAE,CAAC,CAACI,QAAQ,EAAEK,SAAS,CAAC;QAC/D;QACA,IAAI,IAAI,CAACH,eAAe,CAAC,SAAS,CAAC,EAAE;UACjCI,qBAAqB,CAAC,CAAC;QAC3B;MACJ;IACJ;EACJ;EACA;EACAJ,eAAeA,CAACK,IAAI,EAAE;IAClB,IAAIjE,kBAAkB,CAAC,CAAC,EAAE;MACtB,OAAO,KAAK;IAChB;IACA,IAAI,OAAO,IAAI,CAACqD,aAAa,KAAK,SAAS,EAAE;MACzC,OAAO,IAAI,CAACA,aAAa;IAC7B;IACA,OAAO,CAAC,CAAC,IAAI,CAACA,aAAa,CAACY,IAAI,CAAC;EACrC;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,wBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFlB,eAAe,EAAzBlF,EAAE,CAAAqG,QAAA,CAAyChF,EAAE,CAACiF,wBAAwB,GAAtEtG,EAAE,CAAAqG,QAAA,CAAiFtB,sBAAsB,MAAzG/E,EAAE,CAAAqG,QAAA,CAAoIzE,QAAQ;IAAA,CAA2C;EAAE;EAC3R;IAAS,IAAI,CAAC2E,IAAI,kBAD8EvG,EAAE,CAAAwG,gBAAA;MAAAC,IAAA,EACSvB;IAAe,EAAiD;EAAE;EAC7K;IAAS,IAAI,CAACwB,IAAI,kBAF8E1G,EAAE,CAAA2G,gBAAA;MAAAC,OAAA,GAEoCpF,UAAU,EAAEA,UAAU;IAAA,EAAI;EAAE;AACtK;AACA;EAAA,QAAAiE,SAAA,oBAAAA,SAAA,KAJoGzF,EAAE,CAAA6G,iBAAA,CAIX3B,eAAe,EAAc,CAAC;IAC7GuB,IAAI,EAAErG,QAAQ;IACd0G,IAAI,EAAE,CAAC;MACCF,OAAO,EAAE,CAACpF,UAAU,CAAC;MACrBuF,OAAO,EAAE,CAACvF,UAAU;IACxB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEiF,IAAI,EAAEpF,EAAE,CAACiF;IAAyB,CAAC,EAAE;MAAEG,IAAI,EAAEO,SAAS;MAAEC,UAAU,EAAE,CAAC;QACrGR,IAAI,EAAEpG;MACV,CAAC,EAAE;QACCoG,IAAI,EAAEnG,MAAM;QACZwG,IAAI,EAAE,CAAC/B,sBAAsB;MACjC,CAAC;IAAE,CAAC,EAAE;MAAE0B,IAAI,EAAES,QAAQ;MAAED,UAAU,EAAE,CAAC;QACjCR,IAAI,EAAEnG,MAAM;QACZwG,IAAI,EAAE,CAAClF,QAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AACxB;AACA,SAASiE,sBAAsBA,CAACsB,GAAG,EAAE;EACjC,IAAI,CAACA,GAAG,CAACC,OAAO,EAAE;IACdC,OAAO,CAACC,IAAI,CAAC,2DAA2D,GACpE,6DAA6D,CAAC;EACtE;AACJ;AACA;AACA,SAASxB,oBAAoBA,CAACqB,GAAG,EAAEpB,SAAS,EAAE;EAC1C;EACA;EACA,IAAI,CAACoB,GAAG,CAACI,IAAI,IAAI,CAACxB,SAAS,EAAE;IACzB;EACJ;EACA,MAAMyB,WAAW,GAAGL,GAAG,CAACM,aAAa,CAAC,KAAK,CAAC;EAC5CD,WAAW,CAACE,SAAS,CAACC,GAAG,CAAC,yBAAyB,CAAC;EACpDR,GAAG,CAACI,IAAI,CAACK,WAAW,CAACJ,WAAW,CAAC;EACjC,MAAMK,aAAa,GAAGC,gBAAgB,CAACN,WAAW,CAAC;EACnD;EACA;EACA;EACA,IAAIK,aAAa,IAAIA,aAAa,CAACE,OAAO,KAAK,MAAM,EAAE;IACnDV,OAAO,CAACC,IAAI,CAAC,4DAA4D,GACrE,2DAA2D,GAC3D,iEAAiE,CAAC;EAC1E;EACAE,WAAW,CAACQ,MAAM,CAAC,CAAC;AACxB;AACA;AACA,SAAShC,qBAAqBA,CAAA,EAAG;EAC7B,IAAIvE,OAAO,CAACwG,IAAI,KAAKvG,SAAS,CAACuG,IAAI,EAAE;IACjCZ,OAAO,CAACC,IAAI,CAAC,gCAAgC,GACzC7F,OAAO,CAACwG,IAAI,GACZ,mBAAmB,GACnB,2BAA2B,GAC3BvG,SAAS,CAACuG,IAAI,GACd,MAAM,GACN,iEAAiE,CAAC;EAC1E;AACJ;AAEA,SAASC,aAAaA,CAACC,IAAI,EAAE;EACzB,OAAO,cAAcA,IAAI,CAAC;IACtB,IAAI7E,QAAQA,CAAA,EAAG;MACX,OAAO,IAAI,CAAC8E,SAAS;IACzB;IACA,IAAI9E,QAAQA,CAAC+E,KAAK,EAAE;MAChB,IAAI,CAACD,SAAS,GAAGjG,qBAAqB,CAACkG,KAAK,CAAC;IACjD;IACAlD,WAAWA,CAAC,GAAG2B,IAAI,EAAE;MACjB,KAAK,CAAC,GAAGA,IAAI,CAAC;MACd,IAAI,CAACsB,SAAS,GAAG,KAAK;IAC1B;EACJ,CAAC;AACL;AAEA,SAASE,UAAUA,CAACH,IAAI,EAAEI,YAAY,EAAE;EACpC,OAAO,cAAcJ,IAAI,CAAC;IACtB,IAAIK,KAAKA,CAAA,EAAG;MACR,OAAO,IAAI,CAACC,MAAM;IACtB;IACA,IAAID,KAAKA,CAACH,KAAK,EAAE;MACb,MAAMK,YAAY,GAAGL,KAAK,IAAI,IAAI,CAACE,YAAY;MAC/C,IAAIG,YAAY,KAAK,IAAI,CAACD,MAAM,EAAE;QAC9B,IAAI,IAAI,CAACA,MAAM,EAAE;UACb,IAAI,CAACE,WAAW,CAACC,aAAa,CAAClB,SAAS,CAACM,MAAM,CAAE,OAAM,IAAI,CAACS,MAAO,EAAC,CAAC;QACzE;QACA,IAAIC,YAAY,EAAE;UACd,IAAI,CAACC,WAAW,CAACC,aAAa,CAAClB,SAAS,CAACC,GAAG,CAAE,OAAMe,YAAa,EAAC,CAAC;QACvE;QACA,IAAI,CAACD,MAAM,GAAGC,YAAY;MAC9B;IACJ;IACAvD,WAAWA,CAAC,GAAG2B,IAAI,EAAE;MACjB,KAAK,CAAC,GAAGA,IAAI,CAAC;MACd,IAAI,CAACyB,YAAY,GAAGA,YAAY;MAChC;MACA,IAAI,CAACC,KAAK,GAAGD,YAAY;IAC7B;EACJ,CAAC;AACL;AAEA,SAASM,kBAAkBA,CAACV,IAAI,EAAE;EAC9B,OAAO,cAAcA,IAAI,CAAC;IACtB;IACA,IAAIW,aAAaA,CAAA,EAAG;MAChB,OAAO,IAAI,CAACC,cAAc;IAC9B;IACA,IAAID,aAAaA,CAACT,KAAK,EAAE;MACrB,IAAI,CAACU,cAAc,GAAG5G,qBAAqB,CAACkG,KAAK,CAAC;IACtD;IACAlD,WAAWA,CAAC,GAAG2B,IAAI,EAAE;MACjB,KAAK,CAAC,GAAGA,IAAI,CAAC;MACd,IAAI,CAACiC,cAAc,GAAG,KAAK;IAC/B;EACJ,CAAC;AACL;AAEA,SAASC,aAAaA,CAACb,IAAI,EAAEc,eAAe,GAAG,CAAC,EAAE;EAC9C,OAAO,cAAcd,IAAI,CAAC;IACtB,IAAIe,QAAQA,CAAA,EAAG;MACX,OAAO,IAAI,CAAC5F,QAAQ,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC6F,SAAS;IAC9C;IACA,IAAID,QAAQA,CAACb,KAAK,EAAE;MAChB;MACA,IAAI,CAACc,SAAS,GAAGd,KAAK,IAAI,IAAI,GAAGjG,oBAAoB,CAACiG,KAAK,CAAC,GAAG,IAAI,CAACY,eAAe;IACvF;IACA9D,WAAWA,CAAC,GAAG2B,IAAI,EAAE;MACjB,KAAK,CAAC,GAAGA,IAAI,CAAC;MACd,IAAI,CAACqC,SAAS,GAAGF,eAAe;MAChC,IAAI,CAACA,eAAe,GAAGA,eAAe;IAC1C;EACJ,CAAC;AACL;AAEA,SAASG,eAAeA,CAACjB,IAAI,EAAE;EAC3B,OAAO,cAAcA,IAAI,CAAC;IACtB;IACAkB,gBAAgBA,CAAA,EAAG;MACf,MAAMC,QAAQ,GAAG,IAAI,CAACC,UAAU;MAChC,MAAMC,MAAM,GAAG,IAAI,CAACC,gBAAgB,IAAI,IAAI,CAACC,WAAW;MACxD,MAAMC,OAAO,GAAG,IAAI,CAACC,iBAAiB,IAAI,IAAI,CAACC,yBAAyB;MACxE,MAAMC,OAAO,GAAG,IAAI,CAACC,SAAS,GAAG,IAAI,CAACA,SAAS,CAACD,OAAO,GAAG,IAAI;MAC9D,MAAME,QAAQ,GAAGL,OAAO,CAACM,YAAY,CAACH,OAAO,EAAEN,MAAM,CAAC;MACtD,IAAIQ,QAAQ,KAAKV,QAAQ,EAAE;QACvB,IAAI,CAACC,UAAU,GAAGS,QAAQ;QAC1B,IAAI,CAACE,YAAY,CAACC,IAAI,CAAC,CAAC;MAC5B;IACJ;IACAhF,WAAWA,CAAC,GAAG2B,IAAI,EAAE;MACjB,KAAK,CAAC,GAAGA,IAAI,CAAC;MACd;MACA,IAAI,CAACyC,UAAU,GAAG,KAAK;IAC3B;EACJ,CAAC;AACL;;AAEA;AACA,SAASa,gBAAgBA,CAACjC,IAAI,EAAE;EAC5B,OAAO,cAAcA,IAAI,CAAC;IACtBhD,WAAWA,CAAC,GAAG2B,IAAI,EAAE;MACjB,KAAK,CAAC,GAAGA,IAAI,CAAC;MACd;MACA,IAAI,CAACuD,cAAc,GAAG,KAAK;MAC3B;AACZ;AACA;AACA;AACA;MACY,IAAI,CAACC,mBAAmB,GAAG,EAAE;MAC7B;AACZ;AACA;AACA;MACY,IAAI,CAACC,WAAW,GAAG,IAAIjI,UAAU,CAACkI,UAAU,IAAI;QAC5C;QACA;QACA,IAAI,IAAI,CAACH,cAAc,EAAE;UACrB,IAAI,CAACI,iBAAiB,CAACD,UAAU,CAAC;QACtC,CAAC,MACI;UACD,IAAI,CAACF,mBAAmB,CAACI,IAAI,CAACF,UAAU,CAAC;QAC7C;MACJ,CAAC,CAAC;IACN;IACA;AACR;AACA;AACA;AACA;IACQG,gBAAgBA,CAAA,EAAG;MACf,IAAI,IAAI,CAACN,cAAc,KAAK,OAAO5E,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QACxE,MAAMmF,KAAK,CAAC,4DAA4D,GACpE,6BAA6B,CAAC;MACtC;MACA,IAAI,CAACP,cAAc,GAAG,IAAI;MAC1B,IAAI,CAACC,mBAAmB,CAACO,OAAO,CAAC,IAAI,CAACJ,iBAAiB,CAAC;MACxD,IAAI,CAACH,mBAAmB,GAAG,IAAI;IACnC;IACA;IACAG,iBAAiBA,CAACD,UAAU,EAAE;MAC1BA,UAAU,CAACL,IAAI,CAAC,CAAC;MACjBK,UAAU,CAACM,QAAQ,CAAC,CAAC;IACzB;EACJ,CAAC;AACL;;AAEA;AACA,MAAMC,eAAe,GAAG,IAAI7K,cAAc,CAAC,iBAAiB,EAAE;EAC1D8E,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAE+F;AACb,CAAC,CAAC;AACF;AACA,SAASA,uBAAuBA,CAAA,EAAG;EAC/B,OAAO7K,MAAM,CAACI,SAAS,CAAC;AAC5B;AACA;AACA,MAAM0K,WAAW,CAAC;EACd9F,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC+F,cAAc,GAAG,IAAI3I,OAAO,CAAC,CAAC;IACnC;IACA,IAAI,CAAC4I,aAAa,GAAG,IAAI,CAACD,cAAc;EAC5C;EACA;AACJ;AACA;AACA;AACA;AACA;EACIE,kBAAkBA,CAACC,GAAG,EAAE;IACpB,OAAO,IAAI,CAACC,cAAc,CAACD,GAAG,CAAC,IAAI,IAAI,CAACE,OAAO,CAACF,GAAG,CAAC,GAAGA,GAAG,GAAG,IAAI;EACrE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIG,WAAWA,CAACnD,KAAK,EAAE;IACf,IAAIA,KAAK,IAAI,IAAI,IAAK,IAAI,CAACiD,cAAc,CAACjD,KAAK,CAAC,IAAI,IAAI,CAACkD,OAAO,CAAClD,KAAK,CAAE,EAAE;MACtE,OAAOA,KAAK;IAChB;IACA,OAAO,IAAI,CAACoD,OAAO,CAAC,CAAC;EACzB;EACA;AACJ;AACA;AACA;EACIC,SAASA,CAACC,MAAM,EAAE;IACd,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACT,cAAc,CAACf,IAAI,CAAC,CAAC;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIyB,WAAWA,CAACC,KAAK,EAAEC,MAAM,EAAE;IACvB,OAAQ,IAAI,CAACC,OAAO,CAACF,KAAK,CAAC,GAAG,IAAI,CAACE,OAAO,CAACD,MAAM,CAAC,IAC9C,IAAI,CAACE,QAAQ,CAACH,KAAK,CAAC,GAAG,IAAI,CAACG,QAAQ,CAACF,MAAM,CAAC,IAC5C,IAAI,CAACG,OAAO,CAACJ,KAAK,CAAC,GAAG,IAAI,CAACI,OAAO,CAACH,MAAM,CAAC;EAClD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACII,QAAQA,CAACL,KAAK,EAAEC,MAAM,EAAE;IACpB,IAAID,KAAK,IAAIC,MAAM,EAAE;MACjB,IAAIK,UAAU,GAAG,IAAI,CAACZ,OAAO,CAACM,KAAK,CAAC;MACpC,IAAIO,WAAW,GAAG,IAAI,CAACb,OAAO,CAACO,MAAM,CAAC;MACtC,IAAIK,UAAU,IAAIC,WAAW,EAAE;QAC3B,OAAO,CAAC,IAAI,CAACR,WAAW,CAACC,KAAK,EAAEC,MAAM,CAAC;MAC3C;MACA,OAAOK,UAAU,IAAIC,WAAW;IACpC;IACA,OAAOP,KAAK,IAAIC,MAAM;EAC1B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIO,SAASA,CAACC,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAE;IACtB,IAAID,GAAG,IAAI,IAAI,CAACX,WAAW,CAACU,IAAI,EAAEC,GAAG,CAAC,GAAG,CAAC,EAAE;MACxC,OAAOA,GAAG;IACd;IACA,IAAIC,GAAG,IAAI,IAAI,CAACZ,WAAW,CAACU,IAAI,EAAEE,GAAG,CAAC,GAAG,CAAC,EAAE;MACxC,OAAOA,GAAG;IACd;IACA,OAAOF,IAAI;EACf;AACJ;AAEA,MAAMG,gBAAgB,GAAG,IAAIvM,cAAc,CAAC,kBAAkB,CAAC;;AAE/D;AACA;AACA;AACA;AACA;AACA,MAAMwM,cAAc,GAAG,oFAAoF;AAC3G;AACA,SAASC,KAAKA,CAACC,MAAM,EAAEC,aAAa,EAAE;EAClC,MAAMC,WAAW,GAAGC,KAAK,CAACH,MAAM,CAAC;EACjC,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,MAAM,EAAEI,CAAC,EAAE,EAAE;IAC7BF,WAAW,CAACE,CAAC,CAAC,GAAGH,aAAa,CAACG,CAAC,CAAC;EACrC;EACA,OAAOF,WAAW;AACtB;AACA;AACA,MAAMG,iBAAiB,SAAShC,WAAW,CAAC;EACxC9F,WAAWA,CAAC+H,aAAa;EACzB;AACJ;AACA;AACA;EACIC,SAAS,EAAE;IACP,KAAK,CAAC,CAAC;IACP;AACR;AACA;AACA;IACQ,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,KAAK,CAAC1B,SAAS,CAACwB,aAAa,CAAC;EAClC;EACAnB,OAAOA,CAACO,IAAI,EAAE;IACV,OAAOA,IAAI,CAACe,WAAW,CAAC,CAAC;EAC7B;EACArB,QAAQA,CAACM,IAAI,EAAE;IACX,OAAOA,IAAI,CAACN,QAAQ,CAAC,CAAC;EAC1B;EACAC,OAAOA,CAACK,IAAI,EAAE;IACV,OAAOA,IAAI,CAACL,OAAO,CAAC,CAAC;EACzB;EACAqB,YAAYA,CAAChB,IAAI,EAAE;IACf,OAAOA,IAAI,CAACiB,MAAM,CAAC,CAAC;EACxB;EACAC,aAAaA,CAACC,KAAK,EAAE;IACjB,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAACC,cAAc,CAAC,IAAI,CAACjC,MAAM,EAAE;MAAEkC,KAAK,EAAEJ,KAAK;MAAEK,QAAQ,EAAE;IAAM,CAAC,CAAC;IACnF,OAAOnB,KAAK,CAAC,EAAE,EAAEK,CAAC,IAAI,IAAI,CAACe,OAAO,CAACL,GAAG,EAAE,IAAIM,IAAI,CAAC,IAAI,EAAEhB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClE;EACAiB,YAAYA,CAAA,EAAG;IACX,MAAMP,GAAG,GAAG,IAAIC,IAAI,CAACC,cAAc,CAAC,IAAI,CAACjC,MAAM,EAAE;MAAEuC,GAAG,EAAE,SAAS;MAAEJ,QAAQ,EAAE;IAAM,CAAC,CAAC;IACrF,OAAOnB,KAAK,CAAC,EAAE,EAAEK,CAAC,IAAI,IAAI,CAACe,OAAO,CAACL,GAAG,EAAE,IAAIM,IAAI,CAAC,IAAI,EAAE,CAAC,EAAEhB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACtE;EACAmB,iBAAiBA,CAACV,KAAK,EAAE;IACrB,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAACC,cAAc,CAAC,IAAI,CAACjC,MAAM,EAAE;MAAEyC,OAAO,EAAEX,KAAK;MAAEK,QAAQ,EAAE;IAAM,CAAC,CAAC;IACrF,OAAOnB,KAAK,CAAC,CAAC,EAAEK,CAAC,IAAI,IAAI,CAACe,OAAO,CAACL,GAAG,EAAE,IAAIM,IAAI,CAAC,IAAI,EAAE,CAAC,EAAEhB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACrE;EACAqB,WAAWA,CAAC/B,IAAI,EAAE;IACd,MAAMoB,GAAG,GAAG,IAAIC,IAAI,CAACC,cAAc,CAAC,IAAI,CAACjC,MAAM,EAAE;MAAE2C,IAAI,EAAE,SAAS;MAAER,QAAQ,EAAE;IAAM,CAAC,CAAC;IACtF,OAAO,IAAI,CAACC,OAAO,CAACL,GAAG,EAAEpB,IAAI,CAAC;EAClC;EACAiC,iBAAiBA,CAAA,EAAG;IAChB;IACA,OAAO,CAAC;EACZ;EACAC,iBAAiBA,CAAClC,IAAI,EAAE;IACpB,OAAO,IAAI,CAACL,OAAO,CAAC,IAAI,CAACwC,uBAAuB,CAAC,IAAI,CAAC1C,OAAO,CAACO,IAAI,CAAC,EAAE,IAAI,CAACN,QAAQ,CAACM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;EACrG;EACAoC,KAAKA,CAACpC,IAAI,EAAE;IACR,OAAO,IAAI0B,IAAI,CAAC1B,IAAI,CAACqC,OAAO,CAAC,CAAC,CAAC;EACnC;EACAC,UAAUA,CAACN,IAAI,EAAET,KAAK,EAAEvB,IAAI,EAAE;IAC1B,IAAI,OAAO7G,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C;MACA;MACA,IAAIoI,KAAK,GAAG,CAAC,IAAIA,KAAK,GAAG,EAAE,EAAE;QACzB,MAAMjD,KAAK,CAAE,wBAAuBiD,KAAM,4CAA2C,CAAC;MAC1F;MACA,IAAIvB,IAAI,GAAG,CAAC,EAAE;QACV,MAAM1B,KAAK,CAAE,iBAAgB0B,IAAK,mCAAkC,CAAC;MACzE;IACJ;IACA,IAAIuC,MAAM,GAAG,IAAI,CAACJ,uBAAuB,CAACH,IAAI,EAAET,KAAK,EAAEvB,IAAI,CAAC;IAC5D;IACA,IAAIuC,MAAM,CAAC7C,QAAQ,CAAC,CAAC,IAAI6B,KAAK,KAAK,OAAOpI,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC/E,MAAMmF,KAAK,CAAE,iBAAgB0B,IAAK,2BAA0BuB,KAAM,IAAG,CAAC;IAC1E;IACA,OAAOgB,MAAM;EACjB;EACAC,KAAKA,CAAA,EAAG;IACJ,OAAO,IAAId,IAAI,CAAC,CAAC;EACrB;EACAe,KAAKA,CAAC1G,KAAK,EAAE2G,WAAW,EAAE;IACtB;IACA;IACA,IAAI,OAAO3G,KAAK,IAAI,QAAQ,EAAE;MAC1B,OAAO,IAAI2F,IAAI,CAAC3F,KAAK,CAAC;IAC1B;IACA,OAAOA,KAAK,GAAG,IAAI2F,IAAI,CAACA,IAAI,CAACe,KAAK,CAAC1G,KAAK,CAAC,CAAC,GAAG,IAAI;EACrD;EACA4G,MAAMA,CAAC3C,IAAI,EAAE4C,aAAa,EAAE;IACxB,IAAI,CAAC,IAAI,CAAC3D,OAAO,CAACe,IAAI,CAAC,EAAE;MACrB,MAAM1B,KAAK,CAAC,gDAAgD,CAAC;IACjE;IACA,MAAM8C,GAAG,GAAG,IAAIC,IAAI,CAACC,cAAc,CAAC,IAAI,CAACjC,MAAM,EAAE;MAAE,GAAGuD,aAAa;MAAEpB,QAAQ,EAAE;IAAM,CAAC,CAAC;IACvF,OAAO,IAAI,CAACC,OAAO,CAACL,GAAG,EAAEpB,IAAI,CAAC;EAClC;EACA6C,gBAAgBA,CAAC7C,IAAI,EAAE8C,KAAK,EAAE;IAC1B,OAAO,IAAI,CAACC,iBAAiB,CAAC/C,IAAI,EAAE8C,KAAK,GAAG,EAAE,CAAC;EACnD;EACAC,iBAAiBA,CAAC/C,IAAI,EAAEgD,MAAM,EAAE;IAC5B,IAAIC,OAAO,GAAG,IAAI,CAACd,uBAAuB,CAAC,IAAI,CAAC1C,OAAO,CAACO,IAAI,CAAC,EAAE,IAAI,CAACN,QAAQ,CAACM,IAAI,CAAC,GAAGgD,MAAM,EAAE,IAAI,CAACrD,OAAO,CAACK,IAAI,CAAC,CAAC;IAChH;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACN,QAAQ,CAACuD,OAAO,CAAC,IAAI,CAAE,CAAC,IAAI,CAACvD,QAAQ,CAACM,IAAI,CAAC,GAAGgD,MAAM,IAAI,EAAE,GAAI,EAAE,IAAI,EAAE,EAAE;MAC7EC,OAAO,GAAG,IAAI,CAACd,uBAAuB,CAAC,IAAI,CAAC1C,OAAO,CAACwD,OAAO,CAAC,EAAE,IAAI,CAACvD,QAAQ,CAACuD,OAAO,CAAC,EAAE,CAAC,CAAC;IAC5F;IACA,OAAOA,OAAO;EAClB;EACAC,eAAeA,CAAClD,IAAI,EAAEmD,IAAI,EAAE;IACxB,OAAO,IAAI,CAAChB,uBAAuB,CAAC,IAAI,CAAC1C,OAAO,CAACO,IAAI,CAAC,EAAE,IAAI,CAACN,QAAQ,CAACM,IAAI,CAAC,EAAE,IAAI,CAACL,OAAO,CAACK,IAAI,CAAC,GAAGmD,IAAI,CAAC;EAC3G;EACAC,SAASA,CAACpD,IAAI,EAAE;IACZ,OAAO,CACHA,IAAI,CAACqD,cAAc,CAAC,CAAC,EACrB,IAAI,CAACC,OAAO,CAACtD,IAAI,CAACuD,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,EACpC,IAAI,CAACD,OAAO,CAACtD,IAAI,CAACwD,UAAU,CAAC,CAAC,CAAC,CAClC,CAACC,IAAI,CAAC,GAAG,CAAC;EACf;EACA;AACJ;AACA;AACA;AACA;EACIvE,WAAWA,CAACnD,KAAK,EAAE;IACf,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC3B,IAAI,CAACA,KAAK,EAAE;QACR,OAAO,IAAI;MACf;MACA;MACA;MACA,IAAIqE,cAAc,CAACsD,IAAI,CAAC3H,KAAK,CAAC,EAAE;QAC5B,IAAIiE,IAAI,GAAG,IAAI0B,IAAI,CAAC3F,KAAK,CAAC;QAC1B,IAAI,IAAI,CAACkD,OAAO,CAACe,IAAI,CAAC,EAAE;UACpB,OAAOA,IAAI;QACf;MACJ;IACJ;IACA,OAAO,KAAK,CAACd,WAAW,CAACnD,KAAK,CAAC;EACnC;EACAiD,cAAcA,CAACD,GAAG,EAAE;IAChB,OAAOA,GAAG,YAAY2C,IAAI;EAC9B;EACAzC,OAAOA,CAACe,IAAI,EAAE;IACV,OAAO,CAAC2D,KAAK,CAAC3D,IAAI,CAACqC,OAAO,CAAC,CAAC,CAAC;EACjC;EACAlD,OAAOA,CAAA,EAAG;IACN,OAAO,IAAIuC,IAAI,CAACkC,GAAG,CAAC;EACxB;EACA;EACAzB,uBAAuBA,CAACH,IAAI,EAAET,KAAK,EAAEvB,IAAI,EAAE;IACvC;IACA;IACA,MAAM6D,CAAC,GAAG,IAAInC,IAAI,CAAC,CAAC;IACpBmC,CAAC,CAACC,WAAW,CAAC9B,IAAI,EAAET,KAAK,EAAEvB,IAAI,CAAC;IAChC6D,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtB,OAAOF,CAAC;EACZ;EACA;AACJ;AACA;AACA;AACA;EACIP,OAAOA,CAACU,CAAC,EAAE;IACP,OAAO,CAAC,IAAI,GAAGA,CAAC,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC;EAC/B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIxC,OAAOA,CAACL,GAAG,EAAEpB,IAAI,EAAE;IACf;IACA;IACA,MAAM6D,CAAC,GAAG,IAAInC,IAAI,CAAC,CAAC;IACpBmC,CAAC,CAACK,cAAc,CAAClE,IAAI,CAACe,WAAW,CAAC,CAAC,EAAEf,IAAI,CAACN,QAAQ,CAAC,CAAC,EAAEM,IAAI,CAACL,OAAO,CAAC,CAAC,CAAC;IACrEkE,CAAC,CAACM,WAAW,CAACnE,IAAI,CAACoE,QAAQ,CAAC,CAAC,EAAEpE,IAAI,CAACqE,UAAU,CAAC,CAAC,EAAErE,IAAI,CAACsE,UAAU,CAAC,CAAC,EAAEtE,IAAI,CAACuE,eAAe,CAAC,CAAC,CAAC;IAC5F,OAAOnD,GAAG,CAACuB,MAAM,CAACkB,CAAC,CAAC;EACxB;EACA;IAAS,IAAI,CAACjK,IAAI,YAAA4K,0BAAA1K,CAAA;MAAA,YAAAA,CAAA,IAAwF6G,iBAAiB,EAtf3BjN,EAAE,CAAAqG,QAAA,CAsf2C0E,eAAe,MAtf5D/K,EAAE,CAAAqG,QAAA,CAsfuFvE,IAAI,CAACC,QAAQ;IAAA,CAA6C;EAAE;EACrP;IAAS,IAAI,CAACgP,KAAK,kBAvf6E/Q,EAAE,CAAAgR,kBAAA;MAAAC,KAAA,EAufYhE,iBAAiB;MAAAhI,OAAA,EAAjBgI,iBAAiB,CAAA/G;IAAA,EAAG;EAAE;AACxI;AACA;EAAA,QAAAT,SAAA,oBAAAA,SAAA,KAzfoGzF,EAAE,CAAA6G,iBAAA,CAyfXoG,iBAAiB,EAAc,CAAC;IAC/GxG,IAAI,EAAEjG;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEiG,IAAI,EAAEO,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DR,IAAI,EAAEpG;MACV,CAAC,EAAE;QACCoG,IAAI,EAAEnG,MAAM;QACZwG,IAAI,EAAE,CAACiE,eAAe;MAC1B,CAAC;IAAE,CAAC,EAAE;MAAEtE,IAAI,EAAE3E,IAAI,CAACC;IAAS,CAAC,CAAC;EAAE,CAAC;AAAA;AAEjD,MAAMmP,uBAAuB,GAAG;EAC5BnC,KAAK,EAAE;IACHoC,SAAS,EAAE;EACf,CAAC;EACDpJ,OAAO,EAAE;IACLoJ,SAAS,EAAE;MAAE7C,IAAI,EAAE,SAAS;MAAET,KAAK,EAAE,SAAS;MAAEK,GAAG,EAAE;IAAU,CAAC;IAChEkD,cAAc,EAAE;MAAE9C,IAAI,EAAE,SAAS;MAAET,KAAK,EAAE;IAAQ,CAAC;IACnDwD,aAAa,EAAE;MAAE/C,IAAI,EAAE,SAAS;MAAET,KAAK,EAAE,MAAM;MAAEK,GAAG,EAAE;IAAU,CAAC;IACjEoD,kBAAkB,EAAE;MAAEhD,IAAI,EAAE,SAAS;MAAET,KAAK,EAAE;IAAO;EACzD;AACJ,CAAC;AAED,MAAM0D,gBAAgB,CAAC;EACnB;IAAS,IAAI,CAACrL,IAAI,YAAAsL,yBAAApL,CAAA;MAAA,YAAAA,CAAA,IAAwFmL,gBAAgB;IAAA,CAAkD;EAAE;EAC9K;IAAS,IAAI,CAAChL,IAAI,kBAhhB8EvG,EAAE,CAAAwG,gBAAA;MAAAC,IAAA,EAghBS8K;IAAgB,EAAG;EAAE;EAChI;IAAS,IAAI,CAAC7K,IAAI,kBAjhB8E1G,EAAE,CAAA2G,gBAAA;MAAA8K,SAAA,EAihBsC,CAAC;QAAEC,OAAO,EAAEzG,WAAW;QAAE0G,QAAQ,EAAE1E;MAAkB,CAAC;IAAC,EAAG;EAAE;AACxM;AACA;EAAA,QAAAxH,SAAA,oBAAAA,SAAA,KAnhBoGzF,EAAE,CAAA6G,iBAAA,CAmhBX0K,gBAAgB,EAAc,CAAC;IAC9G9K,IAAI,EAAErG,QAAQ;IACd0G,IAAI,EAAE,CAAC;MACC2K,SAAS,EAAE,CAAC;QAAEC,OAAO,EAAEzG,WAAW;QAAE0G,QAAQ,EAAE1E;MAAkB,CAAC;IACrE,CAAC;EACT,CAAC,CAAC;AAAA;AACV,MAAM2E,mBAAmB,CAAC;EACtB;IAAS,IAAI,CAAC1L,IAAI,YAAA2L,4BAAAzL,CAAA;MAAA,YAAAA,CAAA,IAAwFwL,mBAAmB;IAAA,CAAkD;EAAE;EACjL;IAAS,IAAI,CAACrL,IAAI,kBA3hB8EvG,EAAE,CAAAwG,gBAAA;MAAAC,IAAA,EA2hBSmL;IAAmB,EAAgC;EAAE;EAChK;IAAS,IAAI,CAAClL,IAAI,kBA5hB8E1G,EAAE,CAAA2G,gBAAA;MAAA8K,SAAA,EA4hByC,CAAC;QAAEC,OAAO,EAAEjF,gBAAgB;QAAEqF,QAAQ,EAAEZ;MAAwB,CAAC,CAAC;MAAAtK,OAAA,GAAY2K,gBAAgB;IAAA,EAAI;EAAE;AACnP;AACA;EAAA,QAAA9L,SAAA,oBAAAA,SAAA,KA9hBoGzF,EAAE,CAAA6G,iBAAA,CA8hBX+K,mBAAmB,EAAc,CAAC;IACjHnL,IAAI,EAAErG,QAAQ;IACd0G,IAAI,EAAE,CAAC;MACCF,OAAO,EAAE,CAAC2K,gBAAgB,CAAC;MAC3BE,SAAS,EAAE,CAAC;QAAEC,OAAO,EAAEjF,gBAAgB;QAAEqF,QAAQ,EAAEZ;MAAwB,CAAC;IAChF,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA,MAAMa,4BAA4B,CAAC;EAC/B9H,YAAYA,CAACH,OAAO,EAAEkI,IAAI,EAAE;IACxB,OAAO,CAAC,EAAElI,OAAO,IAAIA,OAAO,CAAC2B,OAAO,KAAK3B,OAAO,CAACmI,KAAK,IAAKD,IAAI,IAAIA,IAAI,CAACE,SAAU,CAAC,CAAC;EACxF;EACA;IAAS,IAAI,CAAChM,IAAI,YAAAiM,qCAAA/L,CAAA;MAAA,YAAAA,CAAA,IAAwF2L,4BAA4B;IAAA,CAAoD;EAAE;EAC5L;IAAS,IAAI,CAAChB,KAAK,kBA5iB6E/Q,EAAE,CAAAgR,kBAAA;MAAAC,KAAA,EA4iBYc,4BAA4B;MAAA9M,OAAA,EAA5B8M,4BAA4B,CAAA7L;IAAA,EAAG;EAAE;AACnJ;AACA;EAAA,QAAAT,SAAA,oBAAAA,SAAA,KA9iBoGzF,EAAE,CAAA6G,iBAAA,CA8iBXkL,4BAA4B,EAAc,CAAC;IAC1HtL,IAAI,EAAEjG;EACV,CAAC,CAAC;AAAA;AACV;AACA,MAAM4R,iBAAiB,CAAC;EACpBnI,YAAYA,CAACH,OAAO,EAAEkI,IAAI,EAAE;IACxB,OAAO,CAAC,EAAElI,OAAO,IAAIA,OAAO,CAAC2B,OAAO,KAAK3B,OAAO,CAACuI,OAAO,IAAKL,IAAI,IAAIA,IAAI,CAACE,SAAU,CAAC,CAAC;EAC1F;EACA;IAAS,IAAI,CAAChM,IAAI,YAAAoM,0BAAAlM,CAAA;MAAA,YAAAA,CAAA,IAAwFgM,iBAAiB;IAAA,CAAoD;EAAE;EACjL;IAAS,IAAI,CAACrB,KAAK,kBAvjB6E/Q,EAAE,CAAAgR,kBAAA;MAAAC,KAAA,EAujBYmB,iBAAiB;MAAAnN,OAAA,EAAjBmN,iBAAiB,CAAAlM,IAAA;MAAAlB,UAAA,EAAc;IAAM,EAAG;EAAE;AAC5J;AACA;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KAzjBoGzF,EAAE,CAAA6G,iBAAA,CAyjBXuL,iBAAiB,EAAc,CAAC;IAC/G3L,IAAI,EAAEjG,UAAU;IAChBsG,IAAI,EAAE,CAAC;MAAE9B,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA,MAAMuN,OAAO,CAAC;EACV;IAAS,IAAI,CAACrM,IAAI,YAAAsM,gBAAApM,CAAA;MAAA,YAAAA,CAAA,IAAwFmM,OAAO;IAAA,CAAmD;EAAE;EACtK;IAAS,IAAI,CAACE,IAAI,kBArkB8EzS,EAAE,CAAA0S,iBAAA;MAAAjM,IAAA,EAqkBJ8L,OAAO;MAAAI,SAAA;MAAAC,SAAA;IAAA,EAA0F;EAAE;AACrM;AACA;EAAA,QAAAnN,SAAA,oBAAAA,SAAA,KAvkBoGzF,EAAE,CAAA6G,iBAAA,CAukBX0L,OAAO,EAAc,CAAC;IACrG9L,IAAI,EAAEhG,SAAS;IACfqG,IAAI,EAAE,CAAC;MACC+L,QAAQ,EAAE,uBAAuB;MACjCC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAW;IAChC,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACC,KAAK,EAAEC,OAAO,EAAEC,MAAM,GAAG,KAAK,EAAE;EAC9C;EACA;EACAF,KAAK,CAACG,OAAO,CAACC,IAAI,CAAC5Q,SAAS,CAACwQ,KAAK,CAAC,CAAC,CAACK,SAAS,CAAC,CAAC;IAAEzG;EAAO,CAAC,KAAK;IAC3D0G,QAAQ,CAACL,OAAO,EAAG,GAAEC,MAAO,SAAQ,EAAE,KAAK,CAAC;IAC5CI,QAAQ,CAACL,OAAO,EAAG,GAAEC,MAAO,SAAQ,EAAE,KAAK,CAAC;IAC5CI,QAAQ,CAACL,OAAO,EAAG,GAAEC,MAAO,aAAY,EAAE,KAAK,CAAC;IAChD,IAAItG,MAAM,KAAK,CAAC,IAAIA,MAAM,KAAK,CAAC,EAAE;MAC9B0G,QAAQ,CAACL,OAAO,EAAG,GAAEC,MAAO,IAAGtG,MAAO,OAAM,EAAE,IAAI,CAAC;IACvD,CAAC,MACI,IAAIA,MAAM,GAAG,CAAC,EAAE;MACjB0G,QAAQ,CAACL,OAAO,EAAG,GAAEC,MAAO,aAAY,EAAE,IAAI,CAAC;IACnD;EACJ,CAAC,CAAC;AACN;AACA;AACA,SAASI,QAAQA,CAACL,OAAO,EAAEM,SAAS,EAAEC,KAAK,EAAE;EACzCP,OAAO,CAACrK,aAAa,CAAClB,SAAS,CAAC+L,MAAM,CAACF,SAAS,EAAEC,KAAK,CAAC;AAC5D;AACA,MAAME,aAAa,CAAC;EAChB;IAAS,IAAI,CAACxN,IAAI,YAAAyN,sBAAAvN,CAAA;MAAA,YAAAA,CAAA,IAAwFsN,aAAa;IAAA,CAAkD;EAAE;EAC3K;IAAS,IAAI,CAACnN,IAAI,kBAvmB8EvG,EAAE,CAAAwG,gBAAA;MAAAC,IAAA,EAumBSiN;IAAa,EAA6F;EAAE;EACvN;IAAS,IAAI,CAAChN,IAAI,kBAxmB8E1G,EAAE,CAAA2G,gBAAA;MAAAC,OAAA,GAwmBkC1B,eAAe,EAAEA,eAAe;IAAA,EAAI;EAAE;AAC9K;AACA;EAAA,QAAAO,SAAA,oBAAAA,SAAA,KA1mBoGzF,EAAE,CAAA6G,iBAAA,CA0mBX6M,aAAa,EAAc,CAAC;IAC3GjN,IAAI,EAAErG,QAAQ;IACd0G,IAAI,EAAE,CAAC;MACCF,OAAO,EAAE,CAAC1B,eAAe,CAAC;MAC1B6B,OAAO,EAAE,CAACwL,OAAO,EAAErN,eAAe,CAAC;MACnC0O,YAAY,EAAE,CAACrB,OAAO;IAC1B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA,MAAMsB,SAAS,CAAC;EACZ1O,WAAWA,CAAC2O,SAAS,EACrB;EACAb,OAAO,EACP;EACAc,MAAM,EACN;EACAC,oCAAoC,GAAG,KAAK,EAAE;IAC1C,IAAI,CAACF,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACb,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACc,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,oCAAoC,GAAGA,oCAAoC;IAChF;IACA,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;EACnB;EACA;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,CAACJ,SAAS,CAACK,aAAa,CAAC,IAAI,CAAC;EACtC;AACJ;;AAEA;AACA,MAAMC,8BAA8B,GAAGnS,+BAA+B,CAAC;EACnEoS,OAAO,EAAE,IAAI;EACbC,OAAO,EAAE;AACb,CAAC,CAAC;AACF;AACA,MAAMC,kBAAkB,CAAC;EACrBpP,WAAWA,CAAA,EAAG;IACV,IAAI,CAACqP,OAAO,GAAG,IAAIC,GAAG,CAAC,CAAC;IACxB;IACA,IAAI,CAACC,qBAAqB,GAAIC,KAAK,IAAK;MACpC,MAAMC,MAAM,GAAG1S,eAAe,CAACyS,KAAK,CAAC;MACrC,IAAIC,MAAM,EAAE;QACR,IAAI,CAACJ,OAAO,CAACK,GAAG,CAACF,KAAK,CAAClO,IAAI,CAAC,EAAEoE,OAAO,CAAC,CAACiK,QAAQ,EAAE7B,OAAO,KAAK;UACzD,IAAIA,OAAO,KAAK2B,MAAM,IAAI3B,OAAO,CAAC8B,QAAQ,CAACH,MAAM,CAAC,EAAE;YAChDE,QAAQ,CAACjK,OAAO,CAACmK,OAAO,IAAIA,OAAO,CAACC,WAAW,CAACN,KAAK,CAAC,CAAC;UAC3D;QACJ,CAAC,CAAC;MACN;IACJ,CAAC;EACL;EACA;EACAO,UAAUA,CAACC,MAAM,EAAElP,IAAI,EAAEgN,OAAO,EAAE+B,OAAO,EAAE;IACvC,MAAMI,gBAAgB,GAAG,IAAI,CAACZ,OAAO,CAACK,GAAG,CAAC5O,IAAI,CAAC;IAC/C,IAAImP,gBAAgB,EAAE;MAClB,MAAMC,kBAAkB,GAAGD,gBAAgB,CAACP,GAAG,CAAC5B,OAAO,CAAC;MACxD,IAAIoC,kBAAkB,EAAE;QACpBA,kBAAkB,CAAC1N,GAAG,CAACqN,OAAO,CAAC;MACnC,CAAC,MACI;QACDI,gBAAgB,CAACE,GAAG,CAACrC,OAAO,EAAE,IAAIsC,GAAG,CAAC,CAACP,OAAO,CAAC,CAAC,CAAC;MACrD;IACJ,CAAC,MACI;MACD,IAAI,CAACR,OAAO,CAACc,GAAG,CAACrP,IAAI,EAAE,IAAIwO,GAAG,CAAC,CAAC,CAACxB,OAAO,EAAE,IAAIsC,GAAG,CAAC,CAACP,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChEG,MAAM,CAACK,iBAAiB,CAAC,MAAM;QAC3BC,QAAQ,CAACC,gBAAgB,CAACzP,IAAI,EAAE,IAAI,CAACyO,qBAAqB,EAAEN,8BAA8B,CAAC;MAC/F,CAAC,CAAC;IACN;EACJ;EACA;EACAuB,aAAaA,CAAC1P,IAAI,EAAEgN,OAAO,EAAE+B,OAAO,EAAE;IAClC,MAAMI,gBAAgB,GAAG,IAAI,CAACZ,OAAO,CAACK,GAAG,CAAC5O,IAAI,CAAC;IAC/C,IAAI,CAACmP,gBAAgB,EAAE;MACnB;IACJ;IACA,MAAMC,kBAAkB,GAAGD,gBAAgB,CAACP,GAAG,CAAC5B,OAAO,CAAC;IACxD,IAAI,CAACoC,kBAAkB,EAAE;MACrB;IACJ;IACAA,kBAAkB,CAACO,MAAM,CAACZ,OAAO,CAAC;IAClC,IAAIK,kBAAkB,CAACQ,IAAI,KAAK,CAAC,EAAE;MAC/BT,gBAAgB,CAACQ,MAAM,CAAC3C,OAAO,CAAC;IACpC;IACA,IAAImC,gBAAgB,CAACS,IAAI,KAAK,CAAC,EAAE;MAC7B,IAAI,CAACrB,OAAO,CAACoB,MAAM,CAAC3P,IAAI,CAAC;MACzBwP,QAAQ,CAACK,mBAAmB,CAAC7P,IAAI,EAAE,IAAI,CAACyO,qBAAqB,EAAEN,8BAA8B,CAAC;IAClG;EACJ;AACJ;;AAEA;AACA;AACA;AACA;AACA,MAAM2B,4BAA4B,GAAG;EACjCC,aAAa,EAAE,GAAG;EAClBC,YAAY,EAAE;AAClB,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMC,wBAAwB,GAAG,GAAG;AACpC;AACA,MAAMC,4BAA4B,GAAGlU,+BAA+B,CAAC;EACjEoS,OAAO,EAAE,IAAI;EACbC,OAAO,EAAE;AACb,CAAC,CAAC;AACF;AACA,MAAM8B,iBAAiB,GAAG,CAAC,WAAW,EAAE,YAAY,CAAC;AACrD;AACA,MAAMC,eAAe,GAAG,CAAC,SAAS,EAAE,YAAY,EAAE,UAAU,EAAE,aAAa,CAAC;AAC5E;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,cAAc,CAAC;EACjB;IAAS,IAAI,CAACC,aAAa,GAAG,IAAIhC,kBAAkB,CAAC,CAAC;EAAE;EACxDpP,WAAWA,CAACqR,OAAO,EAAEC,OAAO,EAAEC,mBAAmB,EAAEvJ,SAAS,EAAE;IAC1D,IAAI,CAACqJ,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACtJ,SAAS,GAAGA,SAAS;IAC1B;IACA,IAAI,CAACwJ,cAAc,GAAG,KAAK;IAC3B;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,cAAc,GAAG,IAAInC,GAAG,CAAC,CAAC;IAC/B;IACA,IAAI,CAACoC,0BAA0B,GAAG,KAAK;IACvC;IACA,IAAI1J,SAAS,CAACpH,SAAS,EAAE;MACrB,IAAI,CAAC+Q,iBAAiB,GAAGzU,aAAa,CAACqU,mBAAmB,CAAC;IAC/D;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIK,YAAYA,CAACC,CAAC,EAAEC,CAAC,EAAElD,MAAM,GAAG,CAAC,CAAC,EAAE;IAC5B,MAAMmD,aAAa,GAAI,IAAI,CAACC,cAAc,GACtC,IAAI,CAACA,cAAc,IAAI,IAAI,CAACL,iBAAiB,CAACM,qBAAqB,CAAC,CAAE;IAC1E,MAAMC,eAAe,GAAG;MAAE,GAAGtB,4BAA4B;MAAE,GAAGhC,MAAM,CAACuD;IAAU,CAAC;IAChF,IAAIvD,MAAM,CAACwD,QAAQ,EAAE;MACjBP,CAAC,GAAGE,aAAa,CAACM,IAAI,GAAGN,aAAa,CAACO,KAAK,GAAG,CAAC;MAChDR,CAAC,GAAGC,aAAa,CAACQ,GAAG,GAAGR,aAAa,CAACS,MAAM,GAAG,CAAC;IACpD;IACA,MAAMC,MAAM,GAAG7D,MAAM,CAAC6D,MAAM,IAAIC,wBAAwB,CAACb,CAAC,EAAEC,CAAC,EAAEC,aAAa,CAAC;IAC7E,MAAMY,OAAO,GAAGd,CAAC,GAAGE,aAAa,CAACM,IAAI;IACtC,MAAMO,OAAO,GAAGd,CAAC,GAAGC,aAAa,CAACQ,GAAG;IACrC,MAAM1B,aAAa,GAAGqB,eAAe,CAACrB,aAAa;IACnD,MAAMgC,MAAM,GAAGvC,QAAQ,CAAChO,aAAa,CAAC,KAAK,CAAC;IAC5CuQ,MAAM,CAACtQ,SAAS,CAACC,GAAG,CAAC,oBAAoB,CAAC;IAC1CqQ,MAAM,CAACvK,KAAK,CAAC+J,IAAI,GAAI,GAAEM,OAAO,GAAGF,MAAO,IAAG;IAC3CI,MAAM,CAACvK,KAAK,CAACiK,GAAG,GAAI,GAAEK,OAAO,GAAGH,MAAO,IAAG;IAC1CI,MAAM,CAACvK,KAAK,CAACkK,MAAM,GAAI,GAAEC,MAAM,GAAG,CAAE,IAAG;IACvCI,MAAM,CAACvK,KAAK,CAACgK,KAAK,GAAI,GAAEG,MAAM,GAAG,CAAE,IAAG;IACtC;IACA;IACA,IAAI7D,MAAM,CAACvL,KAAK,IAAI,IAAI,EAAE;MACtBwP,MAAM,CAACvK,KAAK,CAACwK,eAAe,GAAGlE,MAAM,CAACvL,KAAK;IAC/C;IACAwP,MAAM,CAACvK,KAAK,CAACyK,kBAAkB,GAAI,GAAElC,aAAc,IAAG;IACtD,IAAI,CAACc,iBAAiB,CAAClP,WAAW,CAACoQ,MAAM,CAAC;IAC1C;IACA;IACA;IACA;IACA,MAAMG,cAAc,GAAGC,MAAM,CAACtQ,gBAAgB,CAACkQ,MAAM,CAAC;IACtD,MAAMK,sBAAsB,GAAGF,cAAc,CAACG,kBAAkB;IAChE,MAAMC,sBAAsB,GAAGJ,cAAc,CAACD,kBAAkB;IAChE;IACA;IACA;IACA;IACA;IACA,MAAMM,mCAAmC,GAAGH,sBAAsB,KAAK,MAAM;IACzE;IACA;IACAE,sBAAsB,KAAK,IAAI,IAC/BA,sBAAsB,KAAK,QAAQ;IACnC;IACCrB,aAAa,CAACO,KAAK,KAAK,CAAC,IAAIP,aAAa,CAACS,MAAM,KAAK,CAAE;IAC7D;IACA,MAAMc,SAAS,GAAG,IAAI5E,SAAS,CAAC,IAAI,EAAEmE,MAAM,EAAEjE,MAAM,EAAEyE,mCAAmC,CAAC;IAC1F;IACA;IACA;IACA;IACAR,MAAM,CAACvK,KAAK,CAACiL,SAAS,GAAG,kBAAkB;IAC3CD,SAAS,CAACxE,KAAK,GAAG,CAAC,CAAC;IACpB,IAAI,CAACF,MAAM,CAAC4E,UAAU,EAAE;MACpB,IAAI,CAACC,0BAA0B,GAAGH,SAAS;IAC/C;IACA,IAAII,cAAc,GAAG,IAAI;IACzB;IACA;IACA,IAAI,CAACL,mCAAmC,KAAKxC,aAAa,IAAIqB,eAAe,CAACpB,YAAY,CAAC,EAAE;MACzF,IAAI,CAACQ,OAAO,CAACjB,iBAAiB,CAAC,MAAM;QACjC,MAAMsD,eAAe,GAAGA,CAAA,KAAM,IAAI,CAACC,uBAAuB,CAACN,SAAS,CAAC;QACrE,MAAMO,kBAAkB,GAAGA,CAAA,KAAM,IAAI,CAACC,cAAc,CAACR,SAAS,CAAC;QAC/DT,MAAM,CAACtC,gBAAgB,CAAC,eAAe,EAAEoD,eAAe,CAAC;QACzD;QACA;QACA;QACAd,MAAM,CAACtC,gBAAgB,CAAC,kBAAkB,EAAEsD,kBAAkB,CAAC;QAC/DH,cAAc,GAAG;UAAEC,eAAe;UAAEE;QAAmB,CAAC;MAC5D,CAAC,CAAC;IACN;IACA;IACA,IAAI,CAACpC,cAAc,CAACtB,GAAG,CAACmD,SAAS,EAAEI,cAAc,CAAC;IAClD;IACA;IACA,IAAIL,mCAAmC,IAAI,CAACxC,aAAa,EAAE;MACvD,IAAI,CAAC+C,uBAAuB,CAACN,SAAS,CAAC;IAC3C;IACA,OAAOA,SAAS;EACpB;EACA;EACAtE,aAAaA,CAACsE,SAAS,EAAE;IACrB;IACA,IAAIA,SAAS,CAACxE,KAAK,KAAK,CAAC,CAAC,gCAAgCwE,SAAS,CAACxE,KAAK,KAAK,CAAC,CAAC,0BAA0B;MACtG;IACJ;IACA,MAAMiF,QAAQ,GAAGT,SAAS,CAACxF,OAAO;IAClC,MAAMoE,eAAe,GAAG;MAAE,GAAGtB,4BAA4B;MAAE,GAAG0C,SAAS,CAAC1E,MAAM,CAACuD;IAAU,CAAC;IAC1F;IACA;IACA4B,QAAQ,CAACzL,KAAK,CAACyK,kBAAkB,GAAI,GAAEb,eAAe,CAACpB,YAAa,IAAG;IACvEiD,QAAQ,CAACzL,KAAK,CAAC0L,OAAO,GAAG,GAAG;IAC5BV,SAAS,CAACxE,KAAK,GAAG,CAAC,CAAC;IACpB;IACA;IACA,IAAIwE,SAAS,CAACzE,oCAAoC,IAAI,CAACqD,eAAe,CAACpB,YAAY,EAAE;MACjF,IAAI,CAAC8C,uBAAuB,CAACN,SAAS,CAAC;IAC3C;EACJ;EACA;EACAW,UAAUA,CAAA,EAAG;IACT,IAAI,CAACC,iBAAiB,CAAC,CAAC,CAACxO,OAAO,CAACmN,MAAM,IAAIA,MAAM,CAAC9D,OAAO,CAAC,CAAC,CAAC;EAChE;EACA;EACAoF,uBAAuBA,CAAA,EAAG;IACtB,IAAI,CAACD,iBAAiB,CAAC,CAAC,CAACxO,OAAO,CAACmN,MAAM,IAAI;MACvC,IAAI,CAACA,MAAM,CAACjE,MAAM,CAAC4E,UAAU,EAAE;QAC3BX,MAAM,CAAC9D,OAAO,CAAC,CAAC;MACpB;IACJ,CAAC,CAAC;EACN;EACA;EACAqF,kBAAkBA,CAAC7C,mBAAmB,EAAE;IACpC,MAAMzD,OAAO,GAAG5Q,aAAa,CAACqU,mBAAmB,CAAC;IAClD,IAAI,CAAC,IAAI,CAACvJ,SAAS,CAACpH,SAAS,IAAI,CAACkN,OAAO,IAAIA,OAAO,KAAK,IAAI,CAACuG,eAAe,EAAE;MAC3E;IACJ;IACA;IACA,IAAI,CAACC,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAACD,eAAe,GAAGvG,OAAO;IAC9B;IACA;IACAmD,iBAAiB,CAACvL,OAAO,CAACpE,IAAI,IAAI;MAC9B6P,cAAc,CAACC,aAAa,CAACrB,UAAU,CAAC,IAAI,CAACuB,OAAO,EAAEhQ,IAAI,EAAEwM,OAAO,EAAE,IAAI,CAAC;IAC9E,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACIgC,WAAWA,CAACN,KAAK,EAAE;IACf,IAAIA,KAAK,CAAClO,IAAI,KAAK,WAAW,EAAE;MAC5B,IAAI,CAACiT,YAAY,CAAC/E,KAAK,CAAC;IAC5B,CAAC,MACI,IAAIA,KAAK,CAAClO,IAAI,KAAK,YAAY,EAAE;MAClC,IAAI,CAACkT,aAAa,CAAChF,KAAK,CAAC;IAC7B,CAAC,MACI;MACD,IAAI,CAACiF,YAAY,CAAC,CAAC;IACvB;IACA;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAAC/C,0BAA0B,EAAE;MAClC;MACA;MACA;MACA;MACA;MACA,IAAI,CAACJ,OAAO,CAACjB,iBAAiB,CAAC,MAAM;QACjCa,eAAe,CAACxL,OAAO,CAACpE,IAAI,IAAI;UAC5B,IAAI,CAAC+S,eAAe,CAAC9D,gBAAgB,CAACjP,IAAI,EAAE,IAAI,EAAE0P,4BAA4B,CAAC;QACnF,CAAC,CAAC;MACN,CAAC,CAAC;MACF,IAAI,CAACU,0BAA0B,GAAG,IAAI;IAC1C;EACJ;EACA;EACAkC,uBAAuBA,CAACN,SAAS,EAAE;IAC/B,IAAIA,SAAS,CAACxE,KAAK,KAAK,CAAC,CAAC,6BAA6B;MACnD,IAAI,CAAC4F,uBAAuB,CAACpB,SAAS,CAAC;IAC3C,CAAC,MACI,IAAIA,SAAS,CAACxE,KAAK,KAAK,CAAC,CAAC,8BAA8B;MACzD,IAAI,CAACgF,cAAc,CAACR,SAAS,CAAC;IAClC;EACJ;EACA;AACJ;AACA;AACA;EACIoB,uBAAuBA,CAACpB,SAAS,EAAE;IAC/B,MAAMqB,2BAA2B,GAAGrB,SAAS,KAAK,IAAI,CAACG,0BAA0B;IACjF,MAAM;MAAED;IAAW,CAAC,GAAGF,SAAS,CAAC1E,MAAM;IACvC0E,SAAS,CAACxE,KAAK,GAAG,CAAC,CAAC;IACpB;IACA;IACA;IACA;IACA,IAAI,CAAC0E,UAAU,KAAK,CAACmB,2BAA2B,IAAI,CAAC,IAAI,CAACnD,cAAc,CAAC,EAAE;MACvE8B,SAAS,CAACvE,OAAO,CAAC,CAAC;IACvB;EACJ;EACA;EACA+E,cAAcA,CAACR,SAAS,EAAE;IACtB,MAAMI,cAAc,GAAG,IAAI,CAACjC,cAAc,CAAC/B,GAAG,CAAC4D,SAAS,CAAC,IAAI,IAAI;IACjE,IAAI,CAAC7B,cAAc,CAAChB,MAAM,CAAC6C,SAAS,CAAC;IACrC;IACA,IAAI,CAAC,IAAI,CAAC7B,cAAc,CAACf,IAAI,EAAE;MAC3B,IAAI,CAACsB,cAAc,GAAG,IAAI;IAC9B;IACA;IACA;IACA,IAAIsB,SAAS,KAAK,IAAI,CAACG,0BAA0B,EAAE;MAC/C,IAAI,CAACA,0BAA0B,GAAG,IAAI;IAC1C;IACAH,SAAS,CAACxE,KAAK,GAAG,CAAC,CAAC;IACpB,IAAI4E,cAAc,KAAK,IAAI,EAAE;MACzBJ,SAAS,CAACxF,OAAO,CAAC6C,mBAAmB,CAAC,eAAe,EAAE+C,cAAc,CAACC,eAAe,CAAC;MACtFL,SAAS,CAACxF,OAAO,CAAC6C,mBAAmB,CAAC,kBAAkB,EAAE+C,cAAc,CAACG,kBAAkB,CAAC;IAChG;IACAP,SAAS,CAACxF,OAAO,CAACjL,MAAM,CAAC,CAAC;EAC9B;EACA;EACA0R,YAAYA,CAAC/E,KAAK,EAAE;IAChB;IACA;IACA,MAAMoF,eAAe,GAAGzY,+BAA+B,CAACqT,KAAK,CAAC;IAC9D,MAAMqF,gBAAgB,GAAG,IAAI,CAACC,oBAAoB,IAC9CjM,IAAI,CAACkM,GAAG,CAAC,CAAC,GAAG,IAAI,CAACD,oBAAoB,GAAG/D,wBAAwB;IACrE,IAAI,CAAC,IAAI,CAACM,OAAO,CAAC2D,cAAc,IAAI,CAACJ,eAAe,IAAI,CAACC,gBAAgB,EAAE;MACvE,IAAI,CAACrD,cAAc,GAAG,IAAI;MAC1B,IAAI,CAACI,YAAY,CAACpC,KAAK,CAACyF,OAAO,EAAEzF,KAAK,CAAC0F,OAAO,EAAE,IAAI,CAAC7D,OAAO,CAAC8D,YAAY,CAAC;IAC9E;EACJ;EACA;EACAX,aAAaA,CAAChF,KAAK,EAAE;IACjB,IAAI,CAAC,IAAI,CAAC6B,OAAO,CAAC2D,cAAc,IAAI,CAAC5Y,gCAAgC,CAACoT,KAAK,CAAC,EAAE;MAC1E;MACA;MACA;MACA,IAAI,CAACsF,oBAAoB,GAAGjM,IAAI,CAACkM,GAAG,CAAC,CAAC;MACtC,IAAI,CAACvD,cAAc,GAAG,IAAI;MAC1B;MACA;MACA,MAAM4D,OAAO,GAAG5F,KAAK,CAAC6F,cAAc;MACpC;MACA;MACA,IAAID,OAAO,EAAE;QACT,KAAK,IAAIvN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuN,OAAO,CAAC3N,MAAM,EAAEI,CAAC,EAAE,EAAE;UACrC,IAAI,CAAC+J,YAAY,CAACwD,OAAO,CAACvN,CAAC,CAAC,CAACoN,OAAO,EAAEG,OAAO,CAACvN,CAAC,CAAC,CAACqN,OAAO,EAAE,IAAI,CAAC7D,OAAO,CAAC8D,YAAY,CAAC;QACxF;MACJ;IACJ;EACJ;EACA;EACAV,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAACjD,cAAc,EAAE;MACtB;IACJ;IACA,IAAI,CAACA,cAAc,GAAG,KAAK;IAC3B;IACA,IAAI,CAAC0C,iBAAiB,CAAC,CAAC,CAACxO,OAAO,CAACmN,MAAM,IAAI;MACvC;MACA;MACA,MAAMyC,SAAS,GAAGzC,MAAM,CAAC/D,KAAK,KAAK,CAAC,CAAC,6BAChC+D,MAAM,CAACjE,MAAM,CAAC2G,oBAAoB,IAAI1C,MAAM,CAAC/D,KAAK,KAAK,CAAC,CAAC,2BAA4B;MAC1F,IAAI,CAAC+D,MAAM,CAACjE,MAAM,CAAC4E,UAAU,IAAI8B,SAAS,EAAE;QACxCzC,MAAM,CAAC9D,OAAO,CAAC,CAAC;MACpB;IACJ,CAAC,CAAC;EACN;EACAmF,iBAAiBA,CAAA,EAAG;IAChB,OAAOtM,KAAK,CAAC4N,IAAI,CAAC,IAAI,CAAC/D,cAAc,CAACgE,IAAI,CAAC,CAAC,CAAC;EACjD;EACA;EACAnB,oBAAoBA,CAAA,EAAG;IACnB,MAAMoB,OAAO,GAAG,IAAI,CAACrB,eAAe;IACpC,IAAIqB,OAAO,EAAE;MACTzE,iBAAiB,CAACvL,OAAO,CAACpE,IAAI,IAAI6P,cAAc,CAACC,aAAa,CAACZ,aAAa,CAAClP,IAAI,EAAEoU,OAAO,EAAE,IAAI,CAAC,CAAC;MAClG,IAAI,IAAI,CAAChE,0BAA0B,EAAE;QACjCR,eAAe,CAACxL,OAAO,CAACpE,IAAI,IAAIoU,OAAO,CAAC/E,mBAAmB,CAACrP,IAAI,EAAE,IAAI,EAAE0P,4BAA4B,CAAC,CAAC;MAC1G;IACJ;EACJ;AACJ;AACA;AACA;AACA;AACA,SAAS0B,wBAAwBA,CAACb,CAAC,EAAEC,CAAC,EAAE6D,IAAI,EAAE;EAC1C,MAAMC,KAAK,GAAGC,IAAI,CAACxO,GAAG,CAACwO,IAAI,CAACC,GAAG,CAACjE,CAAC,GAAG8D,IAAI,CAACtD,IAAI,CAAC,EAAEwD,IAAI,CAACC,GAAG,CAACjE,CAAC,GAAG8D,IAAI,CAACI,KAAK,CAAC,CAAC;EACzE,MAAMC,KAAK,GAAGH,IAAI,CAACxO,GAAG,CAACwO,IAAI,CAACC,GAAG,CAAChE,CAAC,GAAG6D,IAAI,CAACpD,GAAG,CAAC,EAAEsD,IAAI,CAACC,GAAG,CAAChE,CAAC,GAAG6D,IAAI,CAACM,MAAM,CAAC,CAAC;EACzE,OAAOJ,IAAI,CAACK,IAAI,CAACN,KAAK,GAAGA,KAAK,GAAGI,KAAK,GAAGA,KAAK,CAAC;AACnD;;AAEA;AACA,MAAMG,yBAAyB,GAAG,IAAIpb,cAAc,CAAC,2BAA2B,CAAC;AACjF,MAAMqb,SAAS,CAAC;EACZ;AACJ;AACA;AACA;EACI,IAAIjY,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC8E,SAAS;EACzB;EACA,IAAI9E,QAAQA,CAAC+E,KAAK,EAAE;IAChB,IAAIA,KAAK,EAAE;MACP,IAAI,CAACiR,uBAAuB,CAAC,CAAC;IAClC;IACA,IAAI,CAAClR,SAAS,GAAGC,KAAK;IACtB,IAAI,CAACmT,4BAA4B,CAAC,CAAC;EACvC;EACA;AACJ;AACA;AACA;EACI,IAAIX,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACY,QAAQ,IAAI,IAAI,CAAC9S,WAAW,CAACC,aAAa;EAC1D;EACA,IAAIiS,OAAOA,CAACA,OAAO,EAAE;IACjB,IAAI,CAACY,QAAQ,GAAGZ,OAAO;IACvB,IAAI,CAACW,4BAA4B,CAAC,CAAC;EACvC;EACArW,WAAWA,CAACwD,WAAW,EAAEwM,MAAM,EAAEzP,QAAQ,EAAEgW,aAAa,EAAEC,cAAc,EAAE;IACtE,IAAI,CAAChT,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACgT,cAAc,GAAGA,cAAc;IACpC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAAC/D,MAAM,GAAG,CAAC;IACf,IAAI,CAACxP,SAAS,GAAG,KAAK;IACtB;IACA,IAAI,CAACiC,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACuR,cAAc,GAAGF,aAAa,IAAI,CAAC,CAAC;IACzC,IAAI,CAACG,eAAe,GAAG,IAAIvF,cAAc,CAAC,IAAI,EAAEnB,MAAM,EAAExM,WAAW,EAAEjD,QAAQ,CAAC;EAClF;EACAoW,QAAQA,CAAA,EAAG;IACP,IAAI,CAACzR,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACmR,4BAA4B,CAAC,CAAC;EACvC;EACAO,WAAWA,CAAA,EAAG;IACV,IAAI,CAACF,eAAe,CAACpC,oBAAoB,CAAC,CAAC;EAC/C;EACA;EACAL,UAAUA,CAAA,EAAG;IACT,IAAI,CAACyC,eAAe,CAACzC,UAAU,CAAC,CAAC;EACrC;EACA;EACAE,uBAAuBA,CAAA,EAAG;IACtB,IAAI,CAACuC,eAAe,CAACvC,uBAAuB,CAAC,CAAC;EAClD;EACA;AACJ;AACA;AACA;EACI,IAAIgB,YAAYA,CAAA,EAAG;IACf,OAAO;MACH/C,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBK,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBpP,KAAK,EAAE,IAAI,CAACA,KAAK;MACjB8O,SAAS,EAAE;QACP,GAAG,IAAI,CAACsE,cAAc,CAACtE,SAAS;QAChC,IAAI,IAAI,CAACqE,cAAc,KAAK,gBAAgB,GAAG;UAAE3F,aAAa,EAAE,CAAC;UAAEC,YAAY,EAAE;QAAE,CAAC,GAAG,CAAC,CAAC,CAAC;QAC1F,GAAG,IAAI,CAACqB;MACZ,CAAC;MACDoD,oBAAoB,EAAE,IAAI,CAACkB,cAAc,CAAClB;IAC9C,CAAC;EACL;EACA;AACJ;AACA;AACA;EACI,IAAIP,cAAcA,CAAA,EAAG;IACjB,OAAO,IAAI,CAAC7W,QAAQ,IAAI,CAAC,CAAC,IAAI,CAACsY,cAAc,CAACtY,QAAQ;EAC1D;EACA;EACAkY,4BAA4BA,CAAA,EAAG;IAC3B,IAAI,CAAC,IAAI,CAAClY,QAAQ,IAAI,IAAI,CAAC+G,cAAc,EAAE;MACvC,IAAI,CAACwR,eAAe,CAACtC,kBAAkB,CAAC,IAAI,CAACsB,OAAO,CAAC;IACzD;EACJ;EACA;EACAmB,MAAMA,CAACC,SAAS,EAAEhF,CAAC,GAAG,CAAC,EAAElD,MAAM,EAAE;IAC7B,IAAI,OAAOkI,SAAS,KAAK,QAAQ,EAAE;MAC/B,OAAO,IAAI,CAACJ,eAAe,CAAC9E,YAAY,CAACkF,SAAS,EAAEhF,CAAC,EAAE;QAAE,GAAG,IAAI,CAACqD,YAAY;QAAE,GAAGvG;MAAO,CAAC,CAAC;IAC/F,CAAC,MACI;MACD,OAAO,IAAI,CAAC8H,eAAe,CAAC9E,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE;QAAE,GAAG,IAAI,CAACuD,YAAY;QAAE,GAAG2B;MAAU,CAAC,CAAC;IAC1F;EACJ;EACA;IAAS,IAAI,CAAC/V,IAAI,YAAAgW,kBAAA9V,CAAA;MAAA,YAAAA,CAAA,IAAwFmV,SAAS,EAnnCnBvb,EAAE,CAAAmc,iBAAA,CAmnCmCnc,EAAE,CAACoB,UAAU,GAnnClDpB,EAAE,CAAAmc,iBAAA,CAmnC6Dnc,EAAE,CAACmB,MAAM,GAnnCxEnB,EAAE,CAAAmc,iBAAA,CAmnCmFra,IAAI,CAACC,QAAQ,GAnnClG/B,EAAE,CAAAmc,iBAAA,CAmnC6Gb,yBAAyB,MAnnCxItb,EAAE,CAAAmc,iBAAA,CAmnCmKlb,qBAAqB;IAAA,CAA4D;EAAE;EACxV;IAAS,IAAI,CAACwR,IAAI,kBApnC8EzS,EAAE,CAAA0S,iBAAA;MAAAjM,IAAA,EAonCJ8U,SAAS;MAAA5I,SAAA;MAAAC,SAAA;MAAAwJ,QAAA;MAAAC,YAAA,WAAAC,uBAAAtZ,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UApnCPhD,EAAE,CAAAuc,WAAA,yBAAAtZ,GAAA,CAAAuZ,SAAA;QAAA;MAAA;MAAAC,MAAA;QAAAjU,KAAA;QAAAgU,SAAA;QAAAjF,QAAA;QAAAK,MAAA;QAAAN,SAAA;QAAAhU,QAAA;QAAAuX,OAAA;MAAA;MAAA6B,QAAA;IAAA,EAonCof;EAAE;AAC5lB;AACA;EAAA,QAAAjX,SAAA,oBAAAA,SAAA,KAtnCoGzF,EAAE,CAAA6G,iBAAA,CAsnCX0U,SAAS,EAAc,CAAC;IACvG9U,IAAI,EAAEhG,SAAS;IACfqG,IAAI,EAAE,CAAC;MACC+L,QAAQ,EAAE,2BAA2B;MACrC6J,QAAQ,EAAE,WAAW;MACrB5J,IAAI,EAAE;QACF,OAAO,EAAE,YAAY;QACrB,8BAA8B,EAAE;MACpC;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAErM,IAAI,EAAEzG,EAAE,CAACoB;IAAW,CAAC,EAAE;MAAEqF,IAAI,EAAEzG,EAAE,CAACmB;IAAO,CAAC,EAAE;MAAEsF,IAAI,EAAE3E,IAAI,CAACC;IAAS,CAAC,EAAE;MAAE0E,IAAI,EAAEO,SAAS;MAAEC,UAAU,EAAE,CAAC;QACrIR,IAAI,EAAEpG;MACV,CAAC,EAAE;QACCoG,IAAI,EAAEnG,MAAM;QACZwG,IAAI,EAAE,CAACwU,yBAAyB;MACpC,CAAC;IAAE,CAAC,EAAE;MAAE7U,IAAI,EAAEO,SAAS;MAAEC,UAAU,EAAE,CAAC;QAClCR,IAAI,EAAEpG;MACV,CAAC,EAAE;QACCoG,IAAI,EAAEnG,MAAM;QACZwG,IAAI,EAAE,CAAC7F,qBAAqB;MAChC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEuH,KAAK,EAAE,CAAC;MACpC/B,IAAI,EAAE/F,KAAK;MACXoG,IAAI,EAAE,CAAC,gBAAgB;IAC3B,CAAC,CAAC;IAAE0V,SAAS,EAAE,CAAC;MACZ/V,IAAI,EAAE/F,KAAK;MACXoG,IAAI,EAAE,CAAC,oBAAoB;IAC/B,CAAC,CAAC;IAAEyQ,QAAQ,EAAE,CAAC;MACX9Q,IAAI,EAAE/F,KAAK;MACXoG,IAAI,EAAE,CAAC,mBAAmB;IAC9B,CAAC,CAAC;IAAE8Q,MAAM,EAAE,CAAC;MACTnR,IAAI,EAAE/F,KAAK;MACXoG,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEwQ,SAAS,EAAE,CAAC;MACZ7Q,IAAI,EAAE/F,KAAK;MACXoG,IAAI,EAAE,CAAC,oBAAoB;IAC/B,CAAC,CAAC;IAAExD,QAAQ,EAAE,CAAC;MACXmD,IAAI,EAAE/F,KAAK;MACXoG,IAAI,EAAE,CAAC,mBAAmB;IAC9B,CAAC,CAAC;IAAE+T,OAAO,EAAE,CAAC;MACVpU,IAAI,EAAE/F,KAAK;MACXoG,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM6V,eAAe,CAAC;EAClB;IAAS,IAAI,CAACzW,IAAI,YAAA0W,wBAAAxW,CAAA;MAAA,YAAAA,CAAA,IAAwFuW,eAAe;IAAA,CAAkD;EAAE;EAC7K;IAAS,IAAI,CAACpW,IAAI,kBAnqC8EvG,EAAE,CAAAwG,gBAAA;MAAAC,IAAA,EAmqCSkW;IAAe,EAAiG;EAAE;EAC7N;IAAS,IAAI,CAACjW,IAAI,kBApqC8E1G,EAAE,CAAA2G,gBAAA;MAAAC,OAAA,GAoqCoC1B,eAAe,EAAEA,eAAe;IAAA,EAAI;EAAE;AAChL;AACA;EAAA,QAAAO,SAAA,oBAAAA,SAAA,KAtqCoGzF,EAAE,CAAA6G,iBAAA,CAsqCX8V,eAAe,EAAc,CAAC;IAC7GlW,IAAI,EAAErG,QAAQ;IACd0G,IAAI,EAAE,CAAC;MACCF,OAAO,EAAE,CAAC1B,eAAe,CAAC;MAC1B6B,OAAO,EAAE,CAACwU,SAAS,EAAErW,eAAe,CAAC;MACrC0O,YAAY,EAAE,CAAC2H,SAAS;IAC5B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMsB,iBAAiB,CAAC;EACpB1X,WAAWA,CAACwW,cAAc,EAAE;IACxB,IAAI,CAACA,cAAc,GAAGA,cAAc;IACpC;IACA,IAAI,CAAC1H,KAAK,GAAG,WAAW;IACxB;IACA,IAAI,CAAC3Q,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;AACA;IACQ,IAAI,CAACwZ,UAAU,GAAG,MAAM;EAC5B;EACA;IAAS,IAAI,CAAC5W,IAAI,YAAA6W,0BAAA3W,CAAA;MAAA,YAAAA,CAAA,IAAwFyW,iBAAiB,EAzsC3B7c,EAAE,CAAAmc,iBAAA,CAysC2Clb,qBAAqB;IAAA,CAA4D;EAAE;EAChO;IAAS,IAAI,CAAC+b,IAAI,kBA1sC8Ehd,EAAE,CAAAid,iBAAA;MAAAxW,IAAA,EA0sCJoW,iBAAiB;MAAAlK,SAAA;MAAAC,SAAA;MAAAwJ,QAAA;MAAAC,YAAA,WAAAa,+BAAAla,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA1sCfhD,EAAE,CAAAuc,WAAA,sCAAAtZ,GAAA,CAAAgR,KAAA,qDAAAhR,GAAA,CAAAgR,KAAA,gDAAAhR,GAAA,CAAAK,QAAA,iCAAAL,GAAA,CAAA6Z,UAAA,4CAAA7Z,GAAA,CAAA6Z,UAAA,wCAAA7Z,GAAA,CAAA0Y,cAAA;QAAA;MAAA;MAAAc,MAAA;QAAAxI,KAAA;QAAA3Q,QAAA;QAAAwZ,UAAA;MAAA;MAAAK,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,2BAAAta,EAAA,EAAAC,GAAA;MAAAsa,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EA0sCqwE;EAAE;AAC72E;AACA;EAAA,QAAAhY,SAAA,oBAAAA,SAAA,KA5sCoGzF,EAAE,CAAA6G,iBAAA,CA4sCXgW,iBAAiB,EAAc,CAAC;IAC/GpW,IAAI,EAAE9F,SAAS;IACfmG,IAAI,EAAE,CAAC;MAAE0W,aAAa,EAAE5c,iBAAiB,CAAC8c,IAAI;MAAED,eAAe,EAAE5c,uBAAuB,CAAC8c,MAAM;MAAE9K,QAAQ,EAAE,qBAAqB;MAAEwK,QAAQ,EAAE,EAAE;MAAEvK,IAAI,EAAE;QAC1I,OAAO,EAAE,qBAAqB;QAC9B,2CAA2C,EAAE,2BAA2B;QACxE,qCAAqC,EAAE,qBAAqB;QAC5D,sCAAsC,EAAE,UAAU;QAClD,qCAAqC,EAAE,0BAA0B;QACjE,kCAAkC,EAAE,uBAAuB;QAC3D,iCAAiC,EAAE;MACvC,CAAC;MAAEyK,MAAM,EAAE,CAAC,6iDAA6iD;IAAE,CAAC;EACxkD,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE9W,IAAI,EAAEO,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DR,IAAI,EAAEpG;MACV,CAAC,EAAE;QACCoG,IAAI,EAAEnG,MAAM;QACZwG,IAAI,EAAE,CAAC7F,qBAAqB;MAChC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEgT,KAAK,EAAE,CAAC;MACpCxN,IAAI,EAAE/F;IACV,CAAC,CAAC;IAAE4C,QAAQ,EAAE,CAAC;MACXmD,IAAI,EAAE/F;IACV,CAAC,CAAC;IAAEoc,UAAU,EAAE,CAAC;MACbrW,IAAI,EAAE/F;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMkd,uBAAuB,CAAC;EAC1B;IAAS,IAAI,CAAC1X,IAAI,YAAA2X,gCAAAzX,CAAA;MAAA,YAAAA,CAAA,IAAwFwX,uBAAuB;IAAA,CAAkD;EAAE;EACrL;IAAS,IAAI,CAACrX,IAAI,kBAtuC8EvG,EAAE,CAAAwG,gBAAA;MAAAC,IAAA,EAsuCSmX;IAAuB,EAAgG;EAAE;EACpO;IAAS,IAAI,CAAClX,IAAI,kBAvuC8E1G,EAAE,CAAA2G,gBAAA;MAAAC,OAAA,GAuuC4C1B,eAAe;IAAA,EAAI;EAAE;AACvK;AACA;EAAA,QAAAO,SAAA,oBAAAA,SAAA,KAzuCoGzF,EAAE,CAAA6G,iBAAA,CAyuCX+W,uBAAuB,EAAc,CAAC;IACrHnX,IAAI,EAAErG,QAAQ;IACd0G,IAAI,EAAE,CAAC;MACCF,OAAO,EAAE,CAAC1B,eAAe,CAAC;MAC1B6B,OAAO,EAAE,CAAC8V,iBAAiB,CAAC;MAC5BjJ,YAAY,EAAE,CAACiJ,iBAAiB;IACpC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA,MAAMiB,2BAA2B,GAAG,IAAI5d,cAAc,CAAC,6BAA6B,CAAC;;AAErF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM6d,qBAAqB,GAAG7V,aAAa,CAAC,MAAM,EACjD,CAAC;AACF;AACA,IAAI8V,wBAAwB,GAAG,CAAC;AAChC,MAAMC,gBAAgB,SAASF,qBAAqB,CAAC;EACjD5Y,WAAWA,CAACqE,MAAM,EAAE;IAChB,KAAK,CAAC,CAAC;IACP;IACA,IAAI,CAAC0U,QAAQ,GAAI,sBAAqBF,wBAAwB,EAAG,EAAC;IAClE,IAAI,CAACG,MAAM,GAAG3U,MAAM,EAAE4U,WAAW,IAAI,KAAK;EAC9C;EACA;IAAS,IAAI,CAAClY,IAAI,YAAAmY,yBAAAjY,CAAA;MAAA,YAAAA,CAAA,IAAwF6X,gBAAgB,EAvxC1Bje,EAAE,CAAAmc,iBAAA,CAuxC0C2B,2BAA2B;IAAA,CAA4D;EAAE;EACrO;IAAS,IAAI,CAACrL,IAAI,kBAxxC8EzS,EAAE,CAAA0S,iBAAA;MAAAjM,IAAA,EAwxCJwX,gBAAgB;MAAAxB,MAAA;QAAAvY,KAAA;MAAA;MAAAoa,QAAA,GAxxCdte,EAAE,CAAAue,0BAAA;IAAA,EAwxCgF;EAAE;AACxL;AACA;EAAA,QAAA9Y,SAAA,oBAAAA,SAAA,KA1xCoGzF,EAAE,CAAA6G,iBAAA,CA0xCXoX,gBAAgB,EAAc,CAAC;IAC9GxX,IAAI,EAAEhG;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEgG,IAAI,EAAEO,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DR,IAAI,EAAEnG,MAAM;QACZwG,IAAI,EAAE,CAACgX,2BAA2B;MACtC,CAAC,EAAE;QACCrX,IAAI,EAAEpG;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE6D,KAAK,EAAE,CAAC;MACpCuC,IAAI,EAAE/F;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA;AACA,MAAM8d,YAAY,GAAG,IAAIte,cAAc,CAAC,aAAa,CAAC;AACtD;AACA;AACA;AACA,MAAMue,WAAW,SAASR,gBAAgB,CAAC;EACvC;IAAS,IAAI,CAAC/X,IAAI;MAAA,IAAAwY,wBAAA;MAAA,gBAAAC,oBAAAvY,CAAA;QAAA,QAAAsY,wBAAA,KAAAA,wBAAA,GA9yC8E1e,EAAE,CAAA4e,qBAAA,CA8yCQH,WAAW,IAAArY,CAAA,IAAXqY,WAAW;MAAA;IAAA,GAAqD;EAAE;EAC5K;IAAS,IAAI,CAACzB,IAAI,kBA/yC8Ehd,EAAE,CAAAid,iBAAA;MAAAxW,IAAA,EA+yCJgY,WAAW;MAAA9L,SAAA;MAAAC,SAAA;MAAAwJ,QAAA;MAAAC,YAAA,WAAAwC,yBAAA7b,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA/yCThD,EAAE,CAAA8e,WAAA,SAAA7b,GAAA,CAAAkb,MAAA,oCAAAlb,GAAA,CAAAkb,MAAA,UAAAlb,GAAA,CAAAK,QAAA,CAAAyb,QAAA,uBAAA9b,GAAA,CAAAkb,MAAA,UAAAlb,GAAA,CAAAib,QAAA;QAAA;MAAA;MAAAzB,MAAA;QAAAnZ,QAAA;MAAA;MAAAoZ,QAAA;MAAA4B,QAAA,GAAFte,EAAE,CAAAgf,kBAAA,CA+yCyS,CAAC;QAAEtN,OAAO,EAAE8M,YAAY;QAAES,WAAW,EAAER;MAAY,CAAC,CAAC,GA/yChWze,EAAE,CAAAue,0BAAA;MAAAW,kBAAA,EAAArc,GAAA;MAAAsa,KAAA;MAAAC,IAAA;MAAA+B,MAAA;MAAA9B,QAAA,WAAA+B,qBAAApc,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFhD,EAAE,CAAAqf,eAAA,CAAAzc,GAAA;UAAF5C,EAAE,CAAA2D,cAAA,aA+yCkjB,CAAC,aAAD,CAAC;UA/yCrjB3D,EAAE,CAAA4D,MAAA,EA+yC8mB,CAAC;UA/yCjnB5D,EAAE,CAAAsf,YAAA,EA+yCuoB,CAAC;UA/yC1oBtf,EAAE,CAAA6D,YAAA,CA+yC8oB,CAAC,CAAD,CAAC;UA/yCjpB7D,EAAE,CAAAsf,YAAA,KA+yCwtB,CAAC;QAAA;QAAA,IAAAtc,EAAA;UA/yC3tBhD,EAAE,CAAAuc,WAAA,4BAAAtZ,GAAA,CAAAK,QA+yC4hB,CAAC;UA/yC/hBtD,EAAE,CAAAqD,UAAA,OAAAJ,GAAA,CAAAib,QA+yCijB,CAAC;UA/yCpjBle,EAAE,CAAA+D,SAAA,EA+yC8mB,CAAC;UA/yCjnB/D,EAAE,CAAAgE,kBAAA,KAAAf,GAAA,CAAAiB,KAAA,KA+yC8mB,CAAC;QAAA;MAAA;MAAAqZ,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAAsnC;EAAE;AAC70D;AACA;EAAA,QAAAhY,SAAA,oBAAAA,SAAA,KAjzCoGzF,EAAE,CAAA6G,iBAAA,CAizCX4X,WAAW,EAAc,CAAC;IACzGhY,IAAI,EAAE9F,SAAS;IACfmG,IAAI,EAAE,CAAC;MAAE+L,QAAQ,EAAE,cAAc;MAAE6J,QAAQ,EAAE,aAAa;MAAEc,aAAa,EAAE5c,iBAAiB,CAAC8c,IAAI;MAAED,eAAe,EAAE5c,uBAAuB,CAAC8c,MAAM;MAAElB,MAAM,EAAE,CAAC,UAAU,CAAC;MAAE3J,IAAI,EAAE;QACpK,OAAO,EAAE,kBAAkB;QAC3B,aAAa,EAAE,yBAAyB;QACxC,sBAAsB,EAAE,qCAAqC;QAC7D,wBAAwB,EAAE;MAC9B,CAAC;MAAErB,SAAS,EAAE,CAAC;QAAEC,OAAO,EAAE8M,YAAY;QAAES,WAAW,EAAER;MAAY,CAAC,CAAC;MAAEpB,QAAQ,EAAE,kTAAkT;MAAEE,MAAM,EAAE,CAAC,45BAA45B;IAAE,CAAC;EACvzC,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA,IAAIgC,gBAAgB,GAAG,CAAC;AACxB;AACA,MAAMC,wBAAwB,CAAC;EAC3Bra,WAAWA,CAAA,CACX;EACAsa,MAAM,EACN;EACAC,WAAW,GAAG,KAAK,EAAE;IACjB,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,WAAW,GAAGA,WAAW;EAClC;AACJ;AACA,MAAMC,cAAc,CAAC;EACjB;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,OAAO,IAAI,IAAI,CAACA,OAAO,CAACD,QAAQ;EAChD;EACA;EACA,IAAIrc,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACuc,SAAS;EACzB;EACA;EACA,IAAIxc,QAAQA,CAAA,EAAG;IACX,OAAQ,IAAI,CAACW,KAAK,IAAI,IAAI,CAACA,KAAK,CAACX,QAAQ,IAAK,IAAI,CAAC8E,SAAS;EAChE;EACA,IAAI9E,QAAQA,CAAC+E,KAAK,EAAE;IAChB,IAAI,CAACD,SAAS,GAAGjG,qBAAqB,CAACkG,KAAK,CAAC;EACjD;EACA;EACA,IAAIS,aAAaA,CAAA,EAAG;IAChB,OAAO,CAAC,EAAE,IAAI,CAAC+W,OAAO,IAAI,IAAI,CAACA,OAAO,CAAC/W,aAAa,CAAC;EACzD;EACA;EACA,IAAIiX,4BAA4BA,CAAA,EAAG;IAC/B,OAAO,CAAC,EAAE,IAAI,CAACF,OAAO,IAAI,IAAI,CAACA,OAAO,CAACE,4BAA4B,CAAC;EACxE;EACA5a,WAAWA,CAAC6a,QAAQ,EAAEC,kBAAkB,EAAEJ,OAAO,EAAE5b,KAAK,EAAE;IACtD,IAAI,CAAC+b,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACJ,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC5b,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC6b,SAAS,GAAG,KAAK;IACtB,IAAI,CAACI,OAAO,GAAG,KAAK;IACpB,IAAI,CAAC9X,SAAS,GAAG,KAAK;IACtB,IAAI,CAAC+X,oBAAoB,GAAG,EAAE;IAC9B;IACA,IAAI,CAACC,EAAE,GAAI,cAAab,gBAAgB,EAAG,EAAC;IAC5C;IACA;IACA,IAAI,CAACc,iBAAiB,GAAG,IAAIvf,YAAY,CAAC,CAAC;IAC3C;IACA,IAAI,CAACwf,aAAa,GAAG,IAAI/d,OAAO,CAAC,CAAC;EACtC;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIge,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACL,OAAO;EACvB;EACA;AACJ;AACA;AACA;EACI,IAAIM,SAASA,CAAA,EAAG;IACZ;IACA,OAAO,CAAC,IAAI,CAACC,KAAK,EAAE7X,aAAa,CAAC8X,WAAW,IAAI,EAAE,EAAEC,IAAI,CAAC,CAAC;EAC/D;EACA;EACAC,MAAMA,CAACC,SAAS,GAAG,IAAI,EAAE;IACrB,IAAI,CAAC,IAAI,CAACf,SAAS,EAAE;MACjB,IAAI,CAACA,SAAS,GAAG,IAAI;MACrB,IAAI,CAACG,kBAAkB,CAACa,YAAY,CAAC,CAAC;MACtC,IAAID,SAAS,EAAE;QACX,IAAI,CAACE,yBAAyB,CAAC,CAAC;MACpC;IACJ;EACJ;EACA;EACAC,QAAQA,CAACH,SAAS,GAAG,IAAI,EAAE;IACvB,IAAI,IAAI,CAACf,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,GAAG,KAAK;MACtB,IAAI,CAACG,kBAAkB,CAACa,YAAY,CAAC,CAAC;MACtC,IAAID,SAAS,EAAE;QACX,IAAI,CAACE,yBAAyB,CAAC,CAAC;MACpC;IACJ;EACJ;EACA;EACAE,KAAKA,CAACC,OAAO,EAAEC,OAAO,EAAE;IACpB;IACA;IACA,MAAMlO,OAAO,GAAG,IAAI,CAACmO,eAAe,CAAC,CAAC;IACtC,IAAI,OAAOnO,OAAO,CAACgO,KAAK,KAAK,UAAU,EAAE;MACrChO,OAAO,CAACgO,KAAK,CAACE,OAAO,CAAC;IAC1B;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIE,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC,IAAI,CAACnB,OAAO,EAAE;MACf,IAAI,CAACA,OAAO,GAAG,IAAI;MACnB,IAAI,CAACD,kBAAkB,CAACa,YAAY,CAAC,CAAC;IAC1C;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIQ,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAACpB,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,GAAG,KAAK;MACpB,IAAI,CAACD,kBAAkB,CAACa,YAAY,CAAC,CAAC;IAC1C;EACJ;EACA;EACAS,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACf,SAAS;EACzB;EACA;EACAgB,cAAcA,CAAC7M,KAAK,EAAE;IAClB,IAAI,CAACA,KAAK,CAAC8M,OAAO,KAAKhf,KAAK,IAAIkS,KAAK,CAAC8M,OAAO,KAAK/e,KAAK,KAAK,CAACC,cAAc,CAACgS,KAAK,CAAC,EAAE;MAChF,IAAI,CAAC+M,qBAAqB,CAAC,CAAC;MAC5B;MACA/M,KAAK,CAACgN,cAAc,CAAC,CAAC;IAC1B;EACJ;EACA;AACJ;AACA;AACA;EACID,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAAC,IAAI,CAACpe,QAAQ,EAAE;MAChB,IAAI,CAACwc,SAAS,GAAG,IAAI,CAACF,QAAQ,GAAG,CAAC,IAAI,CAACE,SAAS,GAAG,IAAI;MACvD,IAAI,CAACG,kBAAkB,CAACa,YAAY,CAAC,CAAC;MACtC,IAAI,CAACC,yBAAyB,CAAC,IAAI,CAAC;IACxC;EACJ;EACA;EACA;EACA;EACA;EACAa,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACte,QAAQ,GAAG,IAAI,GAAG,GAAG;EACrC;EACA;EACA8d,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAACpB,QAAQ,CAACpX,aAAa;EACtC;EACAiZ,kBAAkBA,CAAA,EAAG;IACjB;IACA;IACA;IACA;IACA;IACA,IAAI,IAAI,CAAC/B,SAAS,EAAE;MAChB,MAAMU,SAAS,GAAG,IAAI,CAACA,SAAS;MAChC,IAAIA,SAAS,KAAK,IAAI,CAACL,oBAAoB,EAAE;QACzC,IAAI,IAAI,CAACA,oBAAoB,EAAE;UAC3B,IAAI,CAACG,aAAa,CAACnW,IAAI,CAAC,CAAC;QAC7B;QACA,IAAI,CAACgW,oBAAoB,GAAGK,SAAS;MACzC;IACJ;EACJ;EACAzE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACuE,aAAa,CAACxV,QAAQ,CAAC,CAAC;EACjC;EACA;EACAiW,yBAAyBA,CAACrB,WAAW,GAAG,KAAK,EAAE;IAC3C,IAAI,CAACW,iBAAiB,CAACyB,IAAI,CAAC,IAAItC,wBAAwB,CAAC,IAAI,EAAEE,WAAW,CAAC,CAAC;EAChF;EACA;IAAS,IAAI,CAACxZ,IAAI,YAAA6b,uBAAA3b,CAAA;MAj/C8EpG,EAAE,CAAAgiB,gBAAA;IAAA,CAi/CgF;EAAE;EACpL;IAAS,IAAI,CAACvP,IAAI,kBAl/C8EzS,EAAE,CAAA0S,iBAAA;MAAAjM,IAAA,EAk/CJkZ,cAAc;MAAAsC,SAAA,WAAAC,qBAAAlf,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAl/CZhD,EAAE,CAAAmiB,WAAA,CAAArf,GAAA;QAAA;QAAA,IAAAE,EAAA;UAAA,IAAAof,EAAA;UAAFpiB,EAAE,CAAAqiB,cAAA,CAAAD,EAAA,GAAFpiB,EAAE,CAAAsiB,WAAA,QAAArf,GAAA,CAAAwd,KAAA,GAAA2B,EAAA,CAAAvW,KAAA;QAAA;MAAA;MAAA4Q,MAAA;QAAApU,KAAA;QAAA+X,EAAA;QAAA9c,QAAA;MAAA;MAAAif,OAAA;QAAAlC,iBAAA;MAAA;IAAA,EAk/CyP;EAAE;AACjW;AACA;EAAA,QAAA5a,SAAA,oBAAAA,SAAA,KAp/CoGzF,EAAE,CAAA6G,iBAAA,CAo/CX8Y,cAAc,EAAc,CAAC;IAC5GlZ,IAAI,EAAEhG;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEgG,IAAI,EAAEzG,EAAE,CAACoB;IAAW,CAAC,EAAE;MAAEqF,IAAI,EAAEzG,EAAE,CAACwiB;IAAkB,CAAC,EAAE;MAAE/b,IAAI,EAAEO;IAAU,CAAC,EAAE;MAAEP,IAAI,EAAEwX;IAAiB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE5V,KAAK,EAAE,CAAC;MAC1K5B,IAAI,EAAE/F;IACV,CAAC,CAAC;IAAE0f,EAAE,EAAE,CAAC;MACL3Z,IAAI,EAAE/F;IACV,CAAC,CAAC;IAAE4C,QAAQ,EAAE,CAAC;MACXmD,IAAI,EAAE/F;IACV,CAAC,CAAC;IAAE2f,iBAAiB,EAAE,CAAC;MACpB5Z,IAAI,EAAE1F;IACV,CAAC,CAAC;IAAE0f,KAAK,EAAE,CAAC;MACRha,IAAI,EAAEzF,SAAS;MACf8F,IAAI,EAAE,CAAC,MAAM,EAAE;QAAE2b,MAAM,EAAE;MAAK,CAAC;IACnC,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA,MAAMC,SAAS,SAAS/C,cAAc,CAAC;EACnCxa,WAAWA,CAAC8N,OAAO,EAAE0P,iBAAiB,EAAEnZ,MAAM,EAAEvF,KAAK,EAAE;IACnD,KAAK,CAACgP,OAAO,EAAE0P,iBAAiB,EAAEnZ,MAAM,EAAEvF,KAAK,CAAC;EACpD;EACA;IAAS,IAAI,CAACiC,IAAI,YAAA0c,kBAAAxc,CAAA;MAAA,YAAAA,CAAA,IAAwFsc,SAAS,EAzgDnB1iB,EAAE,CAAAmc,iBAAA,CAygDmCnc,EAAE,CAACoB,UAAU,GAzgDlDpB,EAAE,CAAAmc,iBAAA,CAygD6Dnc,EAAE,CAACwiB,iBAAiB,GAzgDnFxiB,EAAE,CAAAmc,iBAAA,CAygD8F2B,2BAA2B,MAzgD3H9d,EAAE,CAAAmc,iBAAA,CAygDsJqC,YAAY;IAAA,CAA4D;EAAE;EAClU;IAAS,IAAI,CAACxB,IAAI,kBA1gD8Ehd,EAAE,CAAAid,iBAAA;MAAAxW,IAAA,EA0gDJic,SAAS;MAAA/P,SAAA;MAAAC,SAAA,WAAwD,QAAQ;MAAAwJ,QAAA;MAAAC,YAAA,WAAAwG,uBAAA7f,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA1gDvEhD,EAAE,CAAA8iB,UAAA,mBAAAC,mCAAA;YAAA,OA0gDJ9f,GAAA,CAAAye,qBAAA,CAAsB,CAAC;UAAA,uBAAAsB,qCAAAC,MAAA;YAAA,OAAvBhgB,GAAA,CAAAue,cAAA,CAAAyB,MAAqB,CAAC;UAAA;QAAA;QAAA,IAAAjgB,EAAA;UA1gDpBhD,EAAE,CAAAkjB,cAAA,OAAAjgB,GAAA,CAAAmd,EAAA;UAAFpgB,EAAE,CAAA8e,WAAA,kBAAA7b,GAAA,CAAAM,QAAA,mBAAAN,GAAA,CAAAK,QAAA,CAAAyb,QAAA;UAAF/e,EAAE,CAAAuc,WAAA,4BAAAtZ,GAAA,CAAAM,QAAA,6BAAAN,GAAA,CAAA2c,QAAA,2BAAA3c,GAAA,CAAAsd,MAAA,6BAAAtd,GAAA,CAAAK,QAAA;QAAA;MAAA;MAAAoZ,QAAA;MAAA4B,QAAA,GAAFte,EAAE,CAAAue,0BAAA;MAAAW,kBAAA,EAAA9a,GAAA;MAAA+Y,KAAA;MAAAC,IAAA;MAAA+B,MAAA;MAAA9B,QAAA,WAAA8F,mBAAAngB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFhD,EAAE,CAAAqf,eAAA,CAAAlb,GAAA;UAAFnE,EAAE,CAAAojB,UAAA,IAAArgB,wCAAA,gCA0gDqoC,CAAC;UA1gDxoC/C,EAAE,CAAAsf,YAAA,EA0gDsrC,CAAC;UA1gDzrCtf,EAAE,CAAA2D,cAAA,gBA0gD4uC,CAAC;UA1gD/uC3D,EAAE,CAAAsf,YAAA,KA0gDqwC,CAAC;UA1gDxwCtf,EAAE,CAAA6D,YAAA,CA0gD4wC,CAAC;UA1gD/wC7D,EAAE,CAAAojB,UAAA,IAAA5f,wCAAA,gCA0gDikD,CAAC;UA1gDpkDxD,EAAE,CAAAojB,UAAA,IAAA1f,yBAAA,iBA0gDgvD,CAAC;UA1gDnvD1D,EAAE,CAAAkD,SAAA,YA0gD07D,CAAC;QAAA;QAAA,IAAAF,EAAA;UA1gD77DhD,EAAE,CAAAqD,UAAA,SAAAJ,GAAA,CAAA2c,QA0gDk+B,CAAC;UA1gDr+B5f,EAAE,CAAA+D,SAAA,EA0gD+5C,CAAC;UA1gDl6C/D,EAAE,CAAAqD,UAAA,UAAAJ,GAAA,CAAA2c,QAAA,IAAA3c,GAAA,CAAAM,QAAA,KAAAN,GAAA,CAAA8c,4BA0gD+5C,CAAC;UA1gDl6C/f,EAAE,CAAA+D,SAAA,EA0gDmtD,CAAC;UA1gDttD/D,EAAE,CAAAqD,UAAA,SAAAJ,GAAA,CAAAgB,KAAA,IAAAhB,GAAA,CAAAgB,KAAA,CAAAka,MA0gDmtD,CAAC;UA1gDttDne,EAAE,CAAA+D,SAAA,EA0gD+3D,CAAC;UA1gDl4D/D,EAAE,CAAAqD,UAAA,qBAAAJ,GAAA,CAAAme,eAAA,EA0gD+3D,CAAC,sBAAAne,GAAA,CAAAK,QAAA,IAAAL,GAAA,CAAA6F,aAAD,CAAC;QAAA;MAAA;MAAAua,YAAA,GAAgpG9H,SAAS,EAAwP5Z,EAAE,CAAC2hB,IAAI,EAA6FzG,iBAAiB;MAAAU,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAAkL;EAAE;AAChqL;AACA;EAAA,QAAAhY,SAAA,oBAAAA,SAAA,KA5gDoGzF,EAAE,CAAA6G,iBAAA,CA4gDX6b,SAAS,EAAc,CAAC;IACvGjc,IAAI,EAAE9F,SAAS;IACfmG,IAAI,EAAE,CAAC;MAAE+L,QAAQ,EAAE,YAAY;MAAE6J,QAAQ,EAAE,WAAW;MAAE5J,IAAI,EAAE;QAClD,MAAM,EAAE,QAAQ;QAChB,iCAAiC,EAAE,UAAU;QAC7C,iCAAiC,EAAE,UAAU;QAC7C,+BAA+B,EAAE,QAAQ;QACzC,iCAAiC,EAAE,UAAU;QAC7C,MAAM,EAAE,IAAI;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,sBAAsB,EAAE,UAAU;QAClC,sBAAsB,EAAE,qBAAqB;QAC7C,SAAS,EAAE,yBAAyB;QACpC,WAAW,EAAE,wBAAwB;QACrC,OAAO,EAAE;MACb,CAAC;MAAE0K,aAAa,EAAE5c,iBAAiB,CAAC8c,IAAI;MAAED,eAAe,EAAE5c,uBAAuB,CAAC8c,MAAM;MAAEN,QAAQ,EAAE,w4CAAw4C;MAAEE,MAAM,EAAE,CAAC,0hGAA0hG;IAAE,CAAC;EACjiJ,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE9W,IAAI,EAAEzG,EAAE,CAACoB;IAAW,CAAC,EAAE;MAAEqF,IAAI,EAAEzG,EAAE,CAACwiB;IAAkB,CAAC,EAAE;MAAE/b,IAAI,EAAEO,SAAS;MAAEC,UAAU,EAAE,CAAC;QACvHR,IAAI,EAAEpG;MACV,CAAC,EAAE;QACCoG,IAAI,EAAEnG,MAAM;QACZwG,IAAI,EAAE,CAACgX,2BAA2B;MACtC,CAAC;IAAE,CAAC,EAAE;MAAErX,IAAI,EAAEgY,WAAW;MAAExX,UAAU,EAAE,CAAC;QACpCR,IAAI,EAAEpG;MACV,CAAC,EAAE;QACCoG,IAAI,EAAEnG,MAAM;QACZwG,IAAI,EAAE,CAAC0X,YAAY;MACvB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS+E,6BAA6BA,CAACC,WAAW,EAAErC,OAAO,EAAEsC,YAAY,EAAE;EACvE,IAAIA,YAAY,CAAC7W,MAAM,EAAE;IACrB,IAAI8W,YAAY,GAAGvC,OAAO,CAACwC,OAAO,CAAC,CAAC;IACpC,IAAIC,MAAM,GAAGH,YAAY,CAACE,OAAO,CAAC,CAAC;IACnC,IAAIE,YAAY,GAAG,CAAC;IACpB,KAAK,IAAI7W,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwW,WAAW,GAAG,CAAC,EAAExW,CAAC,EAAE,EAAE;MACtC,IAAI0W,YAAY,CAAC1W,CAAC,CAAC,CAAC/I,KAAK,IAAIyf,YAAY,CAAC1W,CAAC,CAAC,CAAC/I,KAAK,KAAK2f,MAAM,CAACC,YAAY,CAAC,EAAE;QACzEA,YAAY,EAAE;MAClB;IACJ;IACA,OAAOA,YAAY;EACvB;EACA,OAAO,CAAC;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,wBAAwBA,CAACC,YAAY,EAAEC,YAAY,EAAEC,qBAAqB,EAAEC,WAAW,EAAE;EAC9F,IAAIH,YAAY,GAAGE,qBAAqB,EAAE;IACtC,OAAOF,YAAY;EACvB;EACA,IAAIA,YAAY,GAAGC,YAAY,GAAGC,qBAAqB,GAAGC,WAAW,EAAE;IACnE,OAAOlJ,IAAI,CAACxO,GAAG,CAAC,CAAC,EAAEuX,YAAY,GAAGG,WAAW,GAAGF,YAAY,CAAC;EACjE;EACA,OAAOC,qBAAqB;AAChC;AAEA,MAAME,eAAe,CAAC;EAClB;IAAS,IAAI,CAACje,IAAI,YAAAke,wBAAAhe,CAAA;MAAA,YAAAA,CAAA,IAAwF+d,eAAe;IAAA,CAAkD;EAAE;EAC7K;IAAS,IAAI,CAAC5d,IAAI,kBAxlD8EvG,EAAE,CAAAwG,gBAAA;MAAAC,IAAA,EAwlDS0d;IAAe,EAAkK;EAAE;EAC9R;IAAS,IAAI,CAACzd,IAAI,kBAzlD8E1G,EAAE,CAAA2G,gBAAA;MAAAC,OAAA,GAylDoC+V,eAAe,EAAE9a,YAAY,EAAEqD,eAAe,EAAE0Y,uBAAuB;IAAA,EAAI;EAAE;AACvN;AACA;EAAA,QAAAnY,SAAA,oBAAAA,SAAA,KA3lDoGzF,EAAE,CAAA6G,iBAAA,CA2lDXsd,eAAe,EAAc,CAAC;IAC7G1d,IAAI,EAAErG,QAAQ;IACd0G,IAAI,EAAE,CAAC;MACCF,OAAO,EAAE,CAAC+V,eAAe,EAAE9a,YAAY,EAAEqD,eAAe,EAAE0Y,uBAAuB,CAAC;MAClF7W,OAAO,EAAE,CAAC2b,SAAS,EAAEjE,WAAW,CAAC;MACjC7K,YAAY,EAAE,CAAC8O,SAAS,EAAEjE,WAAW;IACzC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA,MAAM4F,oBAAoB,GAAG;EAAE/P,OAAO,EAAE;AAAK,CAAC;AAC9C;AACA,MAAMgQ,uBAAuB,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,YAAY,CAAC;AAC9E;AACA,MAAMC,sBAAsB,GAAG,iCAAiC;AAChE;AACA,MAAMC,kBAAkB,GAAG,8BAA8B;AACzD;AACA,MAAMC,iBAAiB,GAAG,4BAA4B;AACtD;AACA,MAAMC,iBAAiB,GAAG,4BAA4B;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,CAAC;EAClBxf,WAAWA,CAAA,EAAG;IACV,IAAI,CAACG,SAAS,GAAGnF,MAAM,CAACyB,QAAQ,EAAE;MAAE+D,QAAQ,EAAE;IAAK,CAAC,CAAC;IACrD,IAAI,CAACgW,cAAc,GAAGxb,MAAM,CAACe,uBAAuB,EAAE;MAAEyE,QAAQ,EAAE;IAAK,CAAC,CAAC;IACzE,IAAI,CAACif,oBAAoB,GAAGzkB,MAAM,CAACmb,yBAAyB,EAAE;MAAE3V,QAAQ,EAAE;IAAK,CAAC,CAAC;IACjF,IAAI,CAACwH,SAAS,GAAGhN,MAAM,CAAC4B,QAAQ,CAAC;IACjC,IAAI,CAAC0U,OAAO,GAAGtW,MAAM,CAACgB,MAAM,CAAC;IAC7B,IAAI,CAAC0jB,MAAM,GAAG,IAAIpQ,GAAG,CAAC,CAAC;IACvB;IACA,IAAI,CAACqQ,cAAc,GAAInQ,KAAK,IAAK;MAC7B,IAAI,EAAEA,KAAK,CAACC,MAAM,YAAYmQ,WAAW,CAAC,EAAE;QACxC;MACJ;MACA,MAAMC,WAAW,GAAGrQ,KAAK,CAACC,MAAM;MAChC;MACA,MAAM3B,OAAO,GAAG+R,WAAW,CAACC,OAAO,CAAE,IAAGV,sBAAuB,GAAE,CAAC;MAClE,IAAItR,OAAO,EAAE;QACT,IAAI,CAACiS,aAAa,CAACjS,OAAO,CAAC;MAC/B;IACJ,CAAC;IACD,IAAI,CAACwD,OAAO,CAACjB,iBAAiB,CAAC,MAAM;MACjC,KAAK,MAAMb,KAAK,IAAI2P,uBAAuB,EAAE;QACzC,IAAI,CAAChf,SAAS,EAAEoQ,gBAAgB,CAACf,KAAK,EAAE,IAAI,CAACmQ,cAAc,EAAET,oBAAoB,CAAC;MACtF;IACJ,CAAC,CAAC;EACN;EACAtI,WAAWA,CAAA,EAAG;IACV,MAAMoJ,KAAK,GAAG,IAAI,CAACN,MAAM,CAACjK,IAAI,CAAC,CAAC;IAChC,KAAK,MAAM9H,IAAI,IAAIqS,KAAK,EAAE;MACtB,IAAI,CAACC,aAAa,CAACtS,IAAI,CAAC;IAC5B;IACA,KAAK,MAAM6B,KAAK,IAAI2P,uBAAuB,EAAE;MACzC,IAAI,CAAChf,SAAS,EAAEwQ,mBAAmB,CAACnB,KAAK,EAAE,IAAI,CAACmQ,cAAc,EAAET,oBAAoB,CAAC;IACzF;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIgB,eAAeA,CAACvS,IAAI,EAAEiB,MAAM,EAAE;IAC1B;IACAjB,IAAI,CAACwS,YAAY,CAACf,sBAAsB,EAAE,EAAE,CAAC;IAC7C;IACA,IAAIxQ,MAAM,CAACR,SAAS,IAAI,CAACT,IAAI,CAACyS,YAAY,CAACf,kBAAkB,CAAC,EAAE;MAC5D1R,IAAI,CAACwS,YAAY,CAACd,kBAAkB,EAAEzQ,MAAM,CAACR,SAAS,IAAI,EAAE,CAAC;IACjE;IACA;IACA,IAAIQ,MAAM,CAACwD,QAAQ,EAAE;MACjBzE,IAAI,CAACwS,YAAY,CAACb,iBAAiB,EAAE,EAAE,CAAC;IAC5C;IACA,IAAI1Q,MAAM,CAACzQ,QAAQ,EAAE;MACjBwP,IAAI,CAACwS,YAAY,CAACZ,iBAAiB,EAAE,EAAE,CAAC;IAC5C;EACJ;EACA;EACAc,SAASA,CAAC1S,IAAI,EAAE;IACZ,MAAMkF,MAAM,GAAG,IAAI,CAAC6M,MAAM,CAAChQ,GAAG,CAAC/B,IAAI,CAAC;IACpC,OAAOkF,MAAM,IAAI,IAAI,CAACkN,aAAa,CAACpS,IAAI,CAAC;EAC7C;EACA;EACA2S,WAAWA,CAAC3S,IAAI,EAAExP,QAAQ,EAAE;IACxB,MAAM0U,MAAM,GAAG,IAAI,CAAC6M,MAAM,CAAChQ,GAAG,CAAC/B,IAAI,CAAC;IACpC;IACA,IAAIkF,MAAM,EAAE;MACRA,MAAM,CAAC1U,QAAQ,GAAGA,QAAQ;MAC1B;IACJ;IACA;IACA;IACA,IAAIA,QAAQ,EAAE;MACVwP,IAAI,CAACwS,YAAY,CAACZ,iBAAiB,EAAE,EAAE,CAAC;IAC5C,CAAC,MACI;MACD5R,IAAI,CAAC4S,eAAe,CAAChB,iBAAiB,CAAC;IAC3C;EACJ;EACA;EACAQ,aAAaA,CAACpS,IAAI,EAAE;IAChB,IAAI,CAAC,IAAI,CAACxN,SAAS,EAAE;MACjB;IACJ;IACA,MAAMqgB,cAAc,GAAG,IAAI,CAACd,MAAM,CAAChQ,GAAG,CAAC/B,IAAI,CAAC;IAC5C,IAAI6S,cAAc,EAAE;MAChB,OAAOA,cAAc;IACzB;IACA;IACA7S,IAAI,CAAC8S,aAAa,CAAC,aAAa,CAAC,EAAE5d,MAAM,CAAC,CAAC;IAC3C,MAAMkR,QAAQ,GAAG,IAAI,CAAC5T,SAAS,CAACmC,aAAa,CAAC,MAAM,CAAC;IACrDyR,QAAQ,CAACxR,SAAS,CAACC,GAAG,CAAC,YAAY,EAAEmL,IAAI,CAAC+S,YAAY,CAACrB,kBAAkB,CAAC,CAAC;IAC3E1R,IAAI,CAACgT,MAAM,CAAC5M,QAAQ,CAAC;IACrB;IACA,MAAMlB,MAAM,GAAG,IAAIuD,SAAS,CAAC,IAAIna,UAAU,CAAC8X,QAAQ,CAAC,EAAE,IAAI,CAACzC,OAAO,EAAE,IAAI,CAACtJ,SAAS,EAAE,IAAI,CAACyX,oBAAoB,GAAG,IAAI,CAACA,oBAAoB,GAAG5d,SAAS,EAAE,IAAI,CAAC2U,cAAc,GAAG,IAAI,CAACA,cAAc,GAAG3U,SAAS,CAAC;IAC9MgR,MAAM,CAAC3N,cAAc,GAAG,IAAI;IAC5B2N,MAAM,CAAC6C,OAAO,GAAG/H,IAAI;IACrBkF,MAAM,CAACT,QAAQ,GAAGzE,IAAI,CAACyS,YAAY,CAACd,iBAAiB,CAAC;IACtDzM,MAAM,CAAC1U,QAAQ,GAAGwP,IAAI,CAACyS,YAAY,CAACb,iBAAiB,CAAC;IACtD,IAAI,CAACqB,YAAY,CAACjT,IAAI,EAAEkF,MAAM,CAAC;IAC/B,OAAOA,MAAM;EACjB;EACA+N,YAAYA,CAACjT,IAAI,EAAEkF,MAAM,EAAE;IACvBlF,IAAI,CAAC4S,eAAe,CAACnB,sBAAsB,CAAC;IAC5C,IAAI,CAACM,MAAM,CAACvP,GAAG,CAACxC,IAAI,EAAEkF,MAAM,CAAC;EACjC;EACAoN,aAAaA,CAACtS,IAAI,EAAE;IAChB,MAAMkF,MAAM,GAAG,IAAI,CAAC6M,MAAM,CAAChQ,GAAG,CAAC/B,IAAI,CAAC;IACpC,IAAIkF,MAAM,EAAE;MACR;MACA;MACAA,MAAM,CAAC+D,WAAW,CAAC,CAAC;MACpB,IAAI,CAAC8I,MAAM,CAACjP,MAAM,CAAC9C,IAAI,CAAC;IAC5B;EACJ;EACA;IAAS,IAAI,CAAC5M,IAAI,YAAA8f,wBAAA5f,CAAA;MAAA,YAAAA,CAAA,IAAwFue,eAAe;IAAA,CAAoD;EAAE;EAC/K;IAAS,IAAI,CAAC5T,KAAK,kBA3uD6E/Q,EAAE,CAAAgR,kBAAA;MAAAC,KAAA,EA2uDY0T,eAAe;MAAA1f,OAAA,EAAf0f,eAAe,CAAAze,IAAA;MAAAlB,UAAA,EAAc;IAAM,EAAG;EAAE;AAC1J;AACA;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KA7uDoGzF,EAAE,CAAA6G,iBAAA,CA6uDX8d,eAAe,EAAc,CAAC;IAC7Gle,IAAI,EAAEjG,UAAU;IAChBsG,IAAI,EAAE,CAAC;MAAE9B,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC;AAAA;;AAEtD;AACA;AACA;;AAEA,SAASX,eAAe,EAAEK,kBAAkB,EAAEuG,WAAW,EAAEmH,iBAAiB,EAAErN,sBAAsB,EAAE0H,gBAAgB,EAAE1B,eAAe,EAAEC,uBAAuB,EAAEkG,uBAAuB,EAAEsN,YAAY,EAAEV,2BAA2B,EAAExC,yBAAyB,EAAEpW,eAAe,EAAEqN,OAAO,EAAEmB,aAAa,EAAE9B,mBAAmB,EAAE6M,WAAW,EAAEiE,SAAS,EAAEyB,eAAe,EAAE3E,wBAAwB,EAAE3C,iBAAiB,EAAEe,uBAAuB,EAAErC,SAAS,EAAEoJ,eAAe,EAAEhI,eAAe,EAAE1P,iBAAiB,EAAEsE,gBAAgB,EAAEsC,SAAS,EAAEyC,cAAc,EAAEvE,4BAA4B,EAAEtQ,OAAO,EAAEwc,gBAAgB,EAAE0B,cAAc,EAAE4D,6BAA6B,EAAEO,wBAAwB,EAAE/N,4BAA4B,EAAEzN,UAAU,EAAEO,kBAAkB,EAAEX,aAAa,EAAEkB,eAAe,EAAEgB,gBAAgB,EAAEpB,aAAa,EAAE+J,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}