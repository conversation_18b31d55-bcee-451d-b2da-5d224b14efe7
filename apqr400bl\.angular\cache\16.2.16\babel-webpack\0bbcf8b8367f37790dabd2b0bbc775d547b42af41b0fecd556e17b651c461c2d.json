{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class ApqrFlowComponent {\n  constructor() {}\n  ngOnInit() {}\n  static {\n    this.ɵfac = function ApqrFlowComponent_Factory(t) {\n      return new (t || ApqrFlowComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ApqrFlowComponent,\n      selectors: [[\"app-apqr-flow\"]],\n      decls: 5,\n      vars: 0,\n      consts: [[1, \"component-container\"]],\n      template: function ApqrFlowComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h2\");\n          i0.ɵɵtext(2, \"aPQR Flow Component\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"p\");\n          i0.ɵɵtext(4, \"This is the aPQR Flow component content.\");\n          i0.ɵɵelementEnd()();\n        }\n      },\n      styles: [\".component-container[_ngcontent-%COMP%] {\\n  padding: 2rem;\\n  background-color: white;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\\n}\\n\\nh2[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n  color: #333;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbGF5b3V0L2NvbXBvbmVudHMvYXBxci1mbG93L2FwcXItZmxvdy5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGFBQUE7RUFDQSx1QkFBQTtFQUNBLGtCQUFBO0VBQ0EseUNBQUE7QUFDRjs7QUFFQTtFQUNFLGFBQUE7RUFDQSxXQUFBO0FBQ0YiLCJzb3VyY2VzQ29udGVudCI6WyIuY29tcG9uZW50LWNvbnRhaW5lciB7XG4gIHBhZGRpbmc6IDJyZW07XG4gIGJhY2tncm91bmQtY29sb3I6IHdoaXRlO1xuICBib3JkZXItcmFkaXVzOiA4cHg7XG4gIGJveC1zaGFkb3c6IDAgMnB4IDRweCByZ2JhKDAsIDAsIDAsIDAuMDUpO1xufVxuXG5oMiB7XG4gIG1hcmdpbi10b3A6IDA7XG4gIGNvbG9yOiAjMzMzO1xufVxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["ApqrFlowComponent", "constructor", "ngOnInit", "selectors", "decls", "vars", "consts", "template", "ApqrFlowComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\cal_wf\\Augment\\4-img\\apqr400bl\\src\\app\\layout\\components\\apqr-flow\\apqr-flow.component.ts", "C:\\cal_wf\\Augment\\4-img\\apqr400bl\\src\\app\\layout\\components\\apqr-flow\\apqr-flow.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\n\n@Component({\n  selector: 'app-apqr-flow',\n  templateUrl: './apqr-flow.component.html',\n  styleUrls: ['./apqr-flow.component.scss']\n})\nexport class ApqrFlowComponent implements OnInit {\n\n  constructor() { }\n\n  ngOnInit(): void {\n  }\n\n}\n", "<div class=\"component-container\">\n  <h2>aPQR Flow Component</h2>\n  <p>This is the aPQR Flow component content.</p>\n</div>\n"], "mappings": ";AAOA,OAAM,MAAOA,iBAAiB;EAE5BC,YAAA,GAAgB;EAEhBC,QAAQA,CAAA,GACR;;;uBALWF,iBAAiB;IAAA;EAAA;;;YAAjBA,iBAAiB;MAAAG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP9BE,EAAA,CAAAC,cAAA,aAAiC;UAC3BD,EAAA,CAAAE,MAAA,0BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5BH,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAE,MAAA,+CAAwC;UAAAF,EAAA,CAAAG,YAAA,EAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}